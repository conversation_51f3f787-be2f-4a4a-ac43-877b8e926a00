# Producer 系统环境变量配置模板
# 复制此文件为 .env 并填入真实的API密钥

# =============================================================================
# 🔑 文本生成服务 API 密钥
# =============================================================================

# OpenAI API (GPT系列模型)
# 获取地址: https://platform.openai.com/api-keys
# OPENAI_API_KEY=sk-your-openai-api-key-here

# Google AI API (Gemini系列模型，也用于图像生成)
# 获取地址: https://aistudio.google.com/
# 支持: Gemini文本生成 + Imagen图像生成
GOOGLE_AI_API_KEY=your-google-ai-api-key-here
# 兼容别名
# GEMINI_API_KEY=your-google-ai-api-key-here

# 阿里云通义千问 (中文能力强)
# 获取地址: https://dashscope.aliyun.com
# DASHSCOPE_API_KEY=sk-your-dashscope-api-key-here

# Anthropic Claude API (创意写作能力强)
# 获取地址: https://console.anthropic.com
# ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# DeepSeek API (高性价比，代码能力强)
# 获取地址: https://platform.deepseek.com
# DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# 智谱AI GLM (中文理解好，价格便宜)
# 获取地址: https://open.bigmodel.cn
# GLM_API_KEY=your-glm-api-key-here

# 字节豆包 (多模态能力强)
# 获取地址: https://www.volcengine.com/product/doubao
# DOUBAO_API_KEY=your-doubao-api-key-here

# 月之暗面 Kimi (长文本处理能力强)
# 获取地址: https://platform.moonshot.cn
# KIMI_API_KEY=sk-your-kimi-api-key-here

# =============================================================================
# 🎨 图像生成服务 API 密钥
# =============================================================================

# FLUX API (高质量图像生成)
# 获取地址: https://api.bfl.ml
# FLUX_API_KEY=your-flux-api-key-here

# Stability AI (Stable Diffusion官方API)
# 获取地址: https://platform.stability.ai
# STABILITY_API_KEY=sk-your-stability-api-key-here

# =============================================================================
# 🎬 视频生成服务 API 密钥
# =============================================================================

# 快手可灵 (中国领先视频AI)
# 获取地址: https://klingai.kuaishou.com
# KLING_API_KEY=your-kling-api-key-here

# RunwayML (视频生成备用方案)
# 获取地址: https://runwayml.com
# RUNWAY_API_KEY=your-runway-api-key-here

# =============================================================================
# 🔊 语音合成服务 API 密钥
# =============================================================================

# ElevenLabs (高质量语音合成)
# 获取地址: https://elevenlabs.io
# ELEVENLABS_API_KEY=your-elevenlabs-api-key-here

# Azure Speech Services (微软语音服务)
# 获取地址: https://azure.microsoft.com/speech
# AZURE_SPEECH_KEY=your-azure-speech-key-here
# AZURE_SPEECH_REGION=your-azure-region

# =============================================================================
# 🖥️ 本地模型路径配置
# =============================================================================

# CosyVoice 本地模型路径 (阿里开源语音合成)
# COSYVOICE_MODEL_PATH=/path/to/cosyvoice/models

# FLUX 本地模型路径
# FLUX_SCHNELL_MODEL_PATH=/path/to/flux/models

# Stable Video Diffusion 本地模型路径
# SVD_MODEL_PATH=/path/to/svd/models

# ComfyUI 安装路径
# COMFYUI_PATH=/path/to/ComfyUI

# =============================================================================
# 📊 成本控制配置
# =============================================================================

# 每日成本限制 (美元)
# DAILY_COST_LIMIT=10.0

# 每月成本限制 (美元)
# MONTHLY_COST_LIMIT=100.0

# =============================================================================
# 🔧 系统配置
# =============================================================================

# 调试模式
# DEBUG=false

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
# LOG_LEVEL=INFO

# 临时文件目录
# TEMP_DIR=./temp

# 输出文件目录
# OUTPUT_DIR=./output