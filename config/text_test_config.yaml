# 文本生成链路测试专用配置文件
# 简化配置，专注于文本生成功能测试

# 系统基础配置
system:
  name: "文本生成链路测试"
  version: "1.0.0"
  mode: "text_only"  # 专门的文本测试模式
  debug: true  # 开启调试模式便于测试
  log_level: "DEBUG"

# 输出配置
output:
  base_dir: "./output/text_test_results"
  temp_dir: "./temp/text_test"
  cleanup_temp: false  # 保留临时文件便于调试

# 预算控制（文本生成成本较低）
budget:
  daily_limit_usd: 5.0   # 较小的预算限制
  monthly_limit_usd: 20.0
  cost_tracking: true
  alert_threshold: 0.8

# 文本生成配置（核心配置）
text_generation:
  # 主要模型配置（按优先级排序）
  primary_model: "glm-4-flash"     # 优先使用智谱GLM-4-Flash，稳定且便宜
  fallback_model: "deepseek"       # 备用deepseek, 还是便宜[]
  tertiary_model: "gemini-pro"      # 第三选择google, 便宜
  quaternary_model: "gpt-4-turbo"  # 最后选择gpt，贵


  #primary_model: "gpt-3.5-turbo"     # 优先使用OpenAI，稳定性好
  #fallback_model: "gemini-pro"       # 备用Google AI
  #tertiary_model: "glm-4-flash"      # 第三选择GLM
  #quaternary_model: "deepseek-chat"  # 最后选择DeepSeek
  
  # 生成参数
  max_tokens: 4000
  temperature: 0.7
  timeout: 60  # 增加超时时间，便于调试
  
  # 重试配置
  max_retries: 3
  retry_delay: 1.0
  
  # 测试模式特殊配置
  enable_validation: true   # 启用结果验证
  save_prompts: true       # 保存提示词便于调试
  detailed_logging: true   # 详细日志

# 文本链路特定配置
chains:
  # 大纲生成链路
  outline:
    model: "glm-4-flash"  # 使用稳定的模型
    max_tokens: 3000
    temperature: 0.8  # 稍高的创造性
    enable_validation: true
    
  # 场景生成链路
  scene:
    model: "glm-4-flash"  # 使用稳定的模型
    max_tokens: 4000
    temperature: 0.7
    enable_validation: true
    
  # 对话生成链路
  dialogue:
    model: "glm-4-flash"
    #model: "GLM-4.1V-Thinking-Flash"
    max_tokens: 3000
    temperature: 0.8  # 稍低的随机性，保持一致性
    enable_validation: true

# 测试配置
testing:
  # 测试数据配置
  default_duration: 3  # 默认3分钟短剧
  max_characters: 5    # 最多5个角色
  max_scenes: 8        # 最多8个场景
  
  # 验证配置
  min_character_count: 2
  min_scene_count: 3
  min_dialogue_lines: 5
  
  # 输出配置
  save_intermediate_results: true  # 保存中间结果
  export_formats: ["json", "txt"]  # 导出格式
  
# 成本优化配置
cost_optimization:
  prefer_free_models: true      # 优先使用免费模型
  batch_processing: false       # 测试时不使用批处理
  cache_results: true          # 缓存结果避免重复调用
  
# 错误处理配置
error_handling:
  continue_on_error: false     # 测试时遇到错误立即停止
  detailed_error_logs: true    # 详细错误日志
  save_error_context: true     # 保存错误上下文

# API配置（仅文本相关）
api:
  # GLM配置
  glm:
    base_url: "https://open.bigmodel.cn/api/paas/v4/"
    timeout: 60
    max_retries: 3
    
  # DeepSeek配置
  deepseek:
    base_url: "https://api.deepseek.com/v1/"
    timeout: 60
    max_retries: 3
    
  # 通用HTTP配置
  http:
    timeout: 60
    max_connections: 10
    retry_delay: 1.0

# 日志配置
logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./output/text_test_results/test.log"
  max_size: "10MB"
  backup_count: 3
  
# 开发调试配置
debug:
  print_prompts: true          # 打印提示词
  print_responses: true        # 打印响应
  measure_timing: true         # 测量执行时间
  validate_data_models: true   # 验证数据模型
  save_debug_info: true        # 保存调试信息