# 基于LangChain的历史短剧视频自动制作系统配置文件

# 系统基础配置
system:
  name: "历史短剧视频制作系统"
  version: "1.0.0"
  mode: "hybrid_optimized"  # hybrid_optimized | zero_cost
  debug: false
  log_level: "INFO"

# 输出配置
output:
  base_dir: "./output"
  temp_dir: "./temp"
  video_format: "mp4"
  audio_format: "wav"
  image_format: "png"
  cleanup_temp: true

# 预算控制
budget:
  daily_limit_usd: 15.0
  monthly_limit_usd: 100.0
  cost_tracking: true
  alert_threshold: 0.8  # 80%时发出警告
  
# 其他配置保持不变...
text_generation:
  primary_model: "glm-4-flash"
  fallback_model: "deepseek-chat"
  max_tokens: 4000
  temperature: 0.7
  timeout: 30
  
image_generation:
  primary_service: "google"
  fallback_service: "flux"
  resolution: "1024x1024"
  quality: "high"
  
  # Google Imagen 配置
  google:
    # 免费模型（取消注释即可使用）
    # model: "gemini-2.0-flash-exp"  # 免费模型，推荐开发测试
    
    # 高质量付费模型（当前使用）
    model: "imagen-4.0-generate-preview-06-06"  # 高质量模型，$0.02/张
    
    aspect_ratio: "1:1"             # 支持: 1:1, 3:4, 4:3, 9:16, 16:9
    num_images: 1                    # 每次生成图片数量（1-4）
    person_generation: "allow_adult" # 人物生成策略

# 视频生成配置
video_generation:
  primary_service: "google"         # 主要服务：google, kling, runway, svd, comfyui
  fallback_service: "svd"           # 备用服务
  model: "veo-2"                    # 模型选择：veo-2, kling-v1, kling-v1-5, gen3a
  resolution: "1080p"               # 分辨率：720p, 1080p, 4K
  fps: 24                           # 帧率
  duration: 5                       # 默认时长（秒）
  quality: "high"                   # 质量：low, medium, high
  aspect_ratio: "16:9"              # 宽高比：1:1, 16:9, 9:16
  style: "realistic"                # 风格：realistic, anime, cartoon

# 语音合成配置
voice_synthesis:
  primary_service: "edgetts"        # 主要服务：edgetts, cosyvoice, elevenlabs
  fallback_service: "cosyvoice"     # 备用服务
  language: "zh-CN"                 # 语言
  
  # 默认语音设置
  voice_id: "zh-CN-XiaoxiaoNeural"   # 默认语音ID
  male_voice_id: "zh-CN-YunxiNeural"   # 男声配置
  female_voice_id: "zh-CN-XiaoxiaoNeural" # 女声配置
  
  # 语音参数
  speed: 1.0                        # 语速（0.1-3.0）
  pitch: 1.0                        # 音调（0.1-3.0）
  voice_style: "neutral"            # 语音风格：neutral, cheerful, sad
  auto_gender_selection: true       # 自动根据角色性别选择语音
