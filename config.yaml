# Web文件管理器配置文件

# 应用基本信息
name: "Web文件管理器"
version: "1.0.0"
description: "现代化的Web文件管理系统"
timezone: "Asia/Shanghai"
language: "zh-CN"
theme: "light"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  debug: false
  workers: 1
  max_request_size: 104857600  # 100MB
  request_timeout: 300  # 5分钟

# 安全配置
security:
  secret_key: ""  # 留空将自动生成
  allowed_hosts:
    - "*"
  cors_origins:
    - "*"
  max_login_attempts: 5
  session_timeout: 3600  # 1小时
  csrf_protection: true

# 文件配置
file:
  upload_path: "./uploads"
  max_file_size: 104857600  # 100MB
  allowed_extensions:
    - ".txt"
    - ".md"
    - ".json"
    - ".xml"
    - ".csv"
    - ".log"
    - ".py"
    - ".js"
    - ".html"
    - ".css"
    - ".java"
    - ".cpp"
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"
    - ".pdf"
  thumbnail_size:
    - 200
    - 200

# 数据库配置
database:
  url: "sqlite:///app.db"
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600
  echo: false

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "./logs/app.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
  console_output: true
  json_format: false

# 缓存配置
cache:
  type: "memory"  # memory, redis, memcached
  url: ""
  default_timeout: 300  # 5分钟
  max_entries: 1000