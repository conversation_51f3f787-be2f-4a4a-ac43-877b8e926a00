"""配置管理模块

负责加载、验证和管理系统配置。
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv


class SystemConfig(BaseModel):
    """系统基础配置"""
    name: str = "历史短剧视频制作系统"
    version: str = "1.0.0"
    mode: str = Field(default="hybrid_optimized", pattern="^(hybrid_optimized|zero_cost)$")
    debug: bool = False
    log_level: str = Field(default="INFO", pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$")


class BudgetConfig(BaseModel):
    """预算控制配置"""
    daily_limit_usd: float = Field(default=2.0, ge=0)
    monthly_limit_usd: float = Field(default=50.0, ge=0)
    cost_tracking: bool = True
    alert_threshold: float = Field(default=0.8, ge=0, le=1)


class TextGenerationConfig(BaseModel):
    """文本生成配置"""
    primary_model: str = "glm-4-flash"
    fallback_model: str = "deepseek-chat"
    max_tokens: int = Field(default=4000, ge=1)
    temperature: float = Field(default=0.7, ge=0, le=2)
    timeout: int = Field(default=30, ge=1)


class GoogleImageConfig(BaseModel):
    """谷歌图像生成特定配置"""
    model: str = "gemini-2.0-flash-exp"  # 默认使用免费模型
    aspect_ratio: str = "1:1"
    num_images: int = Field(default=1, ge=1, le=4)
    person_generation: str = Field(default="allow_adult", pattern="^(dont_allow|allow_adult|allow_all)$")


class ImageGenerationConfig(BaseModel):
    """图像生成配置"""
    primary_service: str = Field(default="google", pattern="^(google|flux|sdxl-lightning|local)$")
    fallback_service: str = "flux"
    resolution: str = "1024x1024"
    quality: str = Field(default="high", pattern="^(low|medium|high)$")
    style: str = "cinematic"
    batch_size: int = Field(default=4, ge=1)
    google: GoogleImageConfig = GoogleImageConfig()  # Google特定配置


class VideoGenerationConfig(BaseModel):
    """视频生成配置"""
    primary_service: str = Field(default="kling", pattern="^(kling|runway|svd|comfyui|local|google)$")
    fallback_service: str = "svd"
    model: str = "kling-v1"  # 新增model字段
    resolution: str = "1080p"
    fps: int = Field(default=24, ge=1)
    duration: int = Field(default=5, ge=1)
    quality: str = Field(default="high", pattern="^(low|medium|high)$")
    aspect_ratio: str = "16:9"  # 新增宽高比配置
    style: str = "realistic"  # 新增风格配置


class VoiceSynthesisConfig(BaseModel):
    """语音合成配置"""
    primary_service: str = Field(default="edgetts", pattern="^(edgetts|cosyvoice|elevenlabs|azure)$")
    fallback_service: str = "cosyvoice"
    voice_id: str = "zh-CN-XiaoxiaoNeural"  # 默认中文女声
    male_voice_id: str = "zh-CN-YunxiNeural"  # 新增男声配置
    female_voice_id: str = "zh-CN-XiaoxiaoNeural"  # 新增女声配置
    language: str = "zh-CN"
    speed: float = Field(default=1.0, ge=0.1, le=3.0)
    pitch: float = Field(default=1.0, ge=0.1, le=3.0)
    voice_style: str = "neutral"  # 新增语音风格配置
    auto_gender_selection: bool = True  # 新增自动性别选择


class ModelRecommendationConfig(BaseModel):
    """模型推荐配置 - 用于成本优化的模型选择"""
    # 文本生成模型推荐
    text_generation_high_budget: List[str] = ["gpt-4o", "claude-3-sonnet", "gpt-4o-mini"]
    text_generation_medium_budget: List[str] = ["gpt-4o-mini", "claude-3-haiku", "qwen2.5-72b-instruct"]
    text_generation_low_budget: List[str] = ["qwen2.5-72b-instruct", "glm-4-plus"]
    
    # 图像生成模型推荐
    image_generation_high_budget: List[str] = ["flux-pro", "dall-e-3", "flux-dev"]
    image_generation_medium_budget: List[str] = ["flux-dev", "sdxl-lightning", "flux-schnell"]
    image_generation_low_budget: List[str] = ["flux-schnell", "sdxl-lightning"]
    
    # 视频生成模型推荐
    video_generation_high_budget: List[str] = ["runway-gen3", "kling-pro"]
    video_generation_medium_budget: List[str] = ["kling-pro", "kling-standard", "pika"]
    video_generation_low_budget: List[str] = ["kling-standard", "svd"]
    
    # 语音合成模型推荐
    voice_synthesis_high_budget: List[str] = ["elevenlabs-multilingual", "elevenlabs-turbo", "azure-neural"]
    voice_synthesis_medium_budget: List[str] = ["azure-neural", "cosyvoice"]
    voice_synthesis_low_budget: List[str] = ["cosyvoice", "azure-neural"]


class PerformanceConfig(BaseModel):
    """性能配置"""
    max_concurrent_tasks: int = Field(default=3, ge=1)
    batch_processing: bool = True
    cache_enabled: bool = True
    cache_ttl: int = Field(default=3600, ge=0)
    retry_attempts: int = Field(default=3, ge=0)
    retry_delay: int = Field(default=5, ge=0)


class QualityConfig(BaseModel):
    """质量控制配置"""
    auto_review: bool = True
    quality_threshold: float = Field(default=0.8, ge=0, le=1)
    manual_review_required: bool = False
    backup_generation: bool = True


class OutputConfig(BaseModel):
    """输出配置"""
    base_dir: str = "./output"
    temp_dir: str = "./temp"
    video_format: str = "mp4"
    audio_format: str = "wav"
    image_format: str = "png"
    cleanup_temp: bool = True


class AppConfig(BaseModel):
    """应用程序完整配置"""
    system: SystemConfig = SystemConfig()
    budget: BudgetConfig = BudgetConfig()
    text_generation: TextGenerationConfig = TextGenerationConfig()
    image_generation: ImageGenerationConfig = ImageGenerationConfig()
    video_generation: VideoGenerationConfig = VideoGenerationConfig()
    voice_synthesis: VoiceSynthesisConfig = VoiceSynthesisConfig()
    model_recommendations: ModelRecommendationConfig = ModelRecommendationConfig()  # 新增模型推荐配置
    performance: PerformanceConfig = PerformanceConfig()
    quality: QualityConfig = QualityConfig()
    output: OutputConfig = OutputConfig()


class EnvironmentSettings(BaseSettings):
    """环境变量设置"""
    # API Keys
    openai_api_key: Optional[str] = None
    dashscope_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    glm_api_key: Optional[str] = None
    deepseek_api_key: Optional[str] = None
    google_ai_api_key: Optional[str] = None
    gemini_api_key: Optional[str] = None
    flux_api_key: Optional[str] = None
    stability_api_key: Optional[str] = None
    kling_api_key: Optional[str] = None
    runway_api_key: Optional[str] = None
    elevenlabs_api_key: Optional[str] = None
    azure_speech_key: Optional[str] = None
    azure_speech_region: Optional[str] = None
    
    # Local Model Paths
    cosyvoice_model_path: str = "./models/cosyvoice"
    flux_schnell_model_path: str = "./models/flux-schnell"
    svd_model_path: str = "./models/svd"
    comfyui_path: str = "./ComfyUI"
    
    # System Settings
    output_dir: str = "./output"
    temp_dir: str = "./temp"
    log_level: str = "INFO"
    
    # Budget Control
    daily_budget_usd: Optional[float] = None
    monthly_budget_usd: Optional[float] = None
    cost_tracking_enabled: Optional[bool] = None
    
    # Quality Settings
    image_quality: Optional[str] = None
    video_quality: Optional[str] = None
    audio_quality: Optional[str] = None
    
    # Performance Settings
    max_concurrent_tasks: Optional[int] = None
    batch_size: Optional[int] = None
    cache_enabled: Optional[bool] = None
    
    model_config = SettingsConfigDict(env_file=".env", case_sensitive=False)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/config.yaml"
        self.env_settings = None
        self.app_config = None
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 加载环境变量
        load_dotenv()
        self.env_settings = EnvironmentSettings()
        
        # 加载YAML配置
        config_file = Path(self.config_path)
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            self.app_config = AppConfig(**config_data)
        else:
            self.app_config = AppConfig()
    
    def get_config(self) -> AppConfig:
        """获取应用配置"""
        return self.app_config
    
    def get_service_config(self, service_type: str) -> Dict[str, Any]:
        """获取服务配置"""
        if not self.env_settings:
            return {}
        
        service_configs = {
            'text': {
                'openai_api_key': self.env_settings.openai_api_key,
                'dashscope_api_key': self.env_settings.dashscope_api_key,
                'anthropic_api_key': self.env_settings.anthropic_api_key,
                'glm_api_key': self.env_settings.glm_api_key,
                'deepseek_api_key': self.env_settings.deepseek_api_key,
                'model': self.app_config.text_generation.primary_model if self.app_config and hasattr(self.app_config, 'text_generation') else 'glm-4-flash',
                'temperature': self.app_config.text_generation.temperature if self.app_config and hasattr(self.app_config, 'text_generation') else 0.7,
                'max_tokens': self.app_config.text_generation.max_tokens if self.app_config and hasattr(self.app_config, 'text_generation') else 4000
            },
            'image': {
                'google_ai_api_key': self.env_settings.google_ai_api_key,
                'gemini_api_key': self.env_settings.gemini_api_key,
                'stability_api_key': self.env_settings.stability_api_key,
                'flux_api_key': self.env_settings.flux_api_key,
                'primary_service': self.app_config.image_generation.primary_service if self.app_config and hasattr(self.app_config, 'image_generation') else 'flux'
            },
            'video': {
                'kling_api_key': self.env_settings.kling_api_key,
                'runway_api_key': self.env_settings.runway_api_key,
                'primary_service': self.app_config.video_generation.primary_service if self.app_config and hasattr(self.app_config, 'video_generation') else 'kling'
            },
            'audio': {
                'elevenlabs_api_key': self.env_settings.elevenlabs_api_key,
                'azure_speech_key': self.env_settings.azure_speech_key,
                'azure_speech_region': self.env_settings.azure_speech_region,
                'primary_service': self.app_config.voice_synthesis.primary_service if self.app_config and hasattr(self.app_config, 'voice_synthesis') else 'cosyvoice'
            }
        }
        return service_configs.get(service_type, {})
    
    def get_env_settings(self) -> EnvironmentSettings:
        """获取环境设置"""
        return self.env_settings
    
    def get_api_key(self, service: str) -> Optional[str]:
        """获取API密钥"""
        if not self.env_settings:
            return None
        
        key_mapping = {
            "openai": self.env_settings.openai_api_key,
            "dashscope": self.env_settings.dashscope_api_key,
            "anthropic": self.env_settings.anthropic_api_key,
            "glm": self.env_settings.glm_api_key,
            "deepseek": self.env_settings.deepseek_api_key,
            "google": self.env_settings.google_ai_api_key or self.env_settings.gemini_api_key,
            "google_ai": self.env_settings.google_ai_api_key,
            "gemini": self.env_settings.gemini_api_key,
            "flux": self.env_settings.flux_api_key,
            "stability": self.env_settings.stability_api_key,
            "kling": self.env_settings.kling_api_key,
            "runway": self.env_settings.runway_api_key,
            "elevenlabs": self.env_settings.elevenlabs_api_key,
            "azure_speech": self.env_settings.azure_speech_key,
        }
        return key_mapping.get(service)
    
    def is_service_available(self, service: str) -> bool:
        """检查服务是否可用"""
        api_key = self.get_api_key(service)
        return api_key is not None and api_key.strip() != ""
    
    def get_output_dir(self) -> Path:
        """获取输出目录"""
        if not self.env_settings:
            output_dir = Path("./output")
        else:
            output_dir = Path(self.env_settings.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        if not self.env_settings:
            temp_dir = Path("./temp")
        else:
            temp_dir = Path(self.env_settings.temp_dir)
        temp_dir.mkdir(parents=True, exist_ok=True)
        return temp_dir
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查必要的API密钥
        if self.app_config and self.app_config.system.mode == "hybrid_optimized":
            required_services = ["glm", "deepseek"]
            for service in required_services:
                if not self.is_service_available(service):
                    validation_result["errors"].append(f"Missing API key for {service}")
        
        # 检查目录权限
        try:
            self.get_output_dir()
            self.get_temp_dir()
        except Exception as e:
            validation_result["errors"].append(f"Directory access error: {e}")
        
        # 检查预算设置
        if self.app_config and self.app_config.budget.daily_limit_usd > self.app_config.budget.monthly_limit_usd:
            validation_result["warnings"].append("Daily budget exceeds monthly budget")
        
        validation_result["valid"] = len(validation_result["errors"]) == 0
        return validation_result


# 全局配置管理器实例
config_manager = ConfigManager()