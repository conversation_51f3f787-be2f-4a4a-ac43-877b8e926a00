"""
LLMCaller适配器
将LLMCaller适配到统一的LLMProviderProtocol接口，使用组合模式
"""

import logging
from typing import Dict, Any, List
import litellm

from .llm_interface import LLMProviderProtocol, LLMRequest, LLMResponse
from .llm_caller import LLMCaller
from .config import ConfigManager

logger = logging.getLogger(__name__)


class LLMCallerAdapter:
    """LLMCaller适配器，使用组合模式实现LLMProviderProtocol"""
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化适配器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.llm_caller = LLMCaller(config_manager)
        self.logger = logging.getLogger(__name__)
    
    async def call_llm(
        self, 
        prompt: str, 
        system_message: str = None, 
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        **kwargs
    ) -> str:
        """
        调用LLMCaller的call_llm_api方法
        
        Args:
            prompt: 提示词
            system_message: 系统消息
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        self.logger.info(f"Calling LLM with model: {model or 'default'}")
        
        # 设置模型名称（如果提供了）
        if model:
            original_model = self.llm_caller.model_name
            self.llm_caller.model_name = model
        
        try:
            # 调用LLMCaller
            response = await self.llm_caller.call_llm_api(prompt, system_message)
            return response
        finally:
            # 恢复原始模型名称（如果之前修改了）
            if model:
                self.llm_caller.model_name = original_model
    
    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表
        
        Returns:
            可用模型名称列表
        """
        try:
            # 过滤出当前配置支持的模型
            if self.config_manager:
                text_config = getattr(self.config_manager.app_config, 'text_generation', None)
                if text_config:
                    primary_model = getattr(text_config, 'primary_model', None)
                    fallback_model = getattr(text_config, 'fallback_model', None)
                    
                    # 如果配置了模型，只返回配置的模型
                    if primary_model:
                        return [primary_model] + ([fallback_model] if fallback_model else [])
            
            # 如果没有配置，返回一些常用模型
            return [
                "gpt-4",
                "gpt-3.5-turbo",
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "deepseek-chat",
                "glm-4"
            ]
            
        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return []
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取提供者信息
        
        Returns:
            提供者信息字典
        """
        return {
            "name": "LLMCaller",
            "description": "基于litellm的统一LLM调用器",
            "version": "1.0.0",
            "type": "direct",
            "supported_models": self.get_available_models(),
            "config": {
                "has_config_manager": self.config_manager is not None
            }
        }