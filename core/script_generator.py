"""
剧本生成器模块

独立的剧本生成系统，支持：
- 大纲生成
- 场景详细化
- 对话生成
- 剧本保存和导出
- 多剧本管理
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

from .config import ConfigManager
from .cost_control import CostController
from .models import ScriptData
from .llm_interface import LLMServiceManager, LLMRequest, LLMResponse


@dataclass
class ScriptProject:
    """剧本项目数据结构"""
    id: str
    title: str
    theme: str
    era: str
    target_duration: int
    created_at: str
    updated_at: str
    status: str  # draft, outline_complete, scenes_complete, dialogues_complete, final
    script_data: Optional[ScriptData] = None
    metadata: Optional[Dict[str, Any]] = None
    
    # 添加额外属性以支持组合模式
    genre: str = "剧情"
    plot_summary: str = ""
    key_conflicts: List[str] = None
    emotional_arc: str = ""
    visual_style: Dict[str, Any] = None
    audio_style: Dict[str, Any] = None
    estimated_scenes: int = 0
    estimated_cost: float = 0.0
    production_notes: List[str] = None
    
    def __post_init__(self):
        """初始化后的处理"""
        if self.key_conflicts is None:
            self.key_conflicts = []
        if self.visual_style is None:
            self.visual_style = {}
        if self.audio_style is None:
            self.audio_style = {}
        if self.production_notes is None:
            self.production_notes = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        if self.script_data:
            script_dict = self.script_data.dict()
            # 处理datetime序列化
            if 'created_at' in script_dict and hasattr(script_dict['created_at'], 'isoformat'):
                script_dict['created_at'] = script_dict['created_at'].isoformat()
            if 'updated_at' in script_dict and hasattr(script_dict['updated_at'], 'isoformat'):
                script_dict['updated_at'] = script_dict['updated_at'].isoformat()
            data['script_data'] = script_dict
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScriptProject':
        """从字典创建项目"""
        script_data = None
        if data.get('script_data'):
            script_data = ScriptData(**data['script_data'])
            
            # 设置最终润色相关属性
            script_data.final_summary = data.get('final_summary', script_data.summary)
            script_data.character_development_notes = data.get('character_development_notes', '')
            script_data.scene_flow_notes = data.get('scene_flow_notes', '')
            script_data.dialogue_improvements = data.get('dialogue_improvements', '')
            script_data.overall_quality_rating = data.get('overall_quality_rating', 5)
            script_data.production_recommendations = data.get('production_recommendations', [])
        
        # 创建项目实例
        project = cls(
            id=data['id'],
            title=data['title'],
            theme=data['theme'],
            era=data['era'],
            target_duration=data['target_duration'],
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            status=data['status'],
            script_data=script_data,
            metadata=data.get('metadata', {}),
            genre=data.get('genre', '剧情'),
            plot_summary=data.get('plot_summary', ''),
            key_conflicts=data.get('key_conflicts', []),
            emotional_arc=data.get('emotional_arc', ''),
            visual_style=data.get('visual_style', {}),
            audio_style=data.get('audio_style', {}),
            estimated_scenes=data.get('estimated_scenes', 0),
            estimated_cost=data.get('estimated_cost', 0.0),
            production_notes=data.get('production_notes', [])
        )
        
        return project


class ScriptGenerator:
    """剧本生成器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.cost_controller = CostController(config_manager)
        
        # 初始化日志器
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        
        # 初始化LLM服务管理器
        self.llm_service_manager = LLMServiceManager(config_manager)
        self.llm_service_manager.initialize_provider()
        
        # 项目存储目录
        self.projects_dir = self.config_manager.get_output_dir() / "scripts"
        self.projects_dir.mkdir(parents=True, exist_ok=True)
    
    async def create_project(self, title: str, theme: str, era: str = "现代", 
                           target_duration: int = 60, metadata: Optional[Dict[str, Any]] = None) -> ScriptProject:
        """创建新的剧本项目"""
        project_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        project = ScriptProject(
            id=project_id,
            title=title,
            theme=theme,
            era=era,
            target_duration=target_duration,
            created_at=now,
            updated_at=now,
            status="draft",
            metadata=metadata or {}
        )
        
        # 保存项目
        await self.save_project(project)
        return project
    
    async def generate_outline(self, project: ScriptProject, 
                             character_count: int = 3, scene_count: int = 5) -> ScriptProject:
        """为项目生成大纲"""
        print(f"🎬 为项目 '{project.title}' 生成大纲...")
        
        # 构建提示词
        prompt = f"""
        请为以下剧本生成一个详细大纲：
        
        标题：{project.title}
        主题：{project.theme}
        时代：{project.era}
        目标时长：{project.target_duration}秒
        
        请包含以下内容：
        1. 剧情摘要
        2. 主要角色（约{character_count}个）
        3. 场景列表（约{scene_count}个）
        
        请以JSON格式返回，包含以下字段：
        - title: 剧本标题
        - theme: 主题
        - dynasty: 时代
        - duration: 时长（秒）
        - genre: 类型
        - characters: 角色列表，每个角色包含name和description
        - scenes: 场景列表，每个场景包含title, location, time_period, description, duration
        - plot_summary: 剧情摘要
        - key_conflicts: 主要冲突列表
        - emotional_arc: 情感发展线
        """
        
        # 构建系统消息
        system_message = """
        你是一位专业的剧本创作助手，擅长创作各种类型的剧本大纲。
        请根据用户提供的信息，创作一个结构完整、内容丰富的剧本大纲。
        确保大纲包含角色、场景和剧情发展等关键元素。
        """
        
        # 创建LLM请求
        request = LLMRequest(
            prompt=prompt,
            system_message=system_message,
            temperature=0.7,
            max_tokens=4000
        )
        
        # 调用LLM服务
        try:
            response = await self.llm_service_manager.call_llm(request)
            response_text = response.content if response else None
            
            # 检查响应内容是否为空
            if not response_text:
                raise ValueError("模型返回的内容为空")
            
            # 解析响应
            try:
                outline_data = json.loads(response_text)
            except json.JSONDecodeError:
                # 如果返回的不是JSON格式，尝试提取JSON部分
                import re
                json_match = re.search(r'```json\n(.*?)\n```', response_text, re.DOTALL)
                if json_match:
                    outline_data = json.loads(json_match.group(1))
                else:
                    raise ValueError("无法解析模型返回的大纲数据")
            
            # 直接使用解析后的数据
            outline_data = {
                "title": outline_data.get("title", project.title),
                "theme": outline_data.get("theme", project.theme),
                "dynasty": outline_data.get("dynasty", project.era),
                "duration": outline_data.get("duration", project.target_duration),
                "genre": outline_data.get("genre", "剧情"),
                "characters": outline_data.get("characters", []),
                "scenes": outline_data.get("scenes", []),
                "plot_summary": outline_data.get("plot_summary", ""),
                "key_conflicts": outline_data.get("key_conflicts", []),
                "emotional_arc": outline_data.get("emotional_arc", ""),
                "visual_style": outline_data.get("visual_style", {}),
                "audio_style": outline_data.get("audio_style", {}),
                "estimated_scenes": len(outline_data.get("scenes", [])),
                "estimated_cost": 0.0,
                "production_notes": []
            }
            
        except Exception as e:
            self.logger.error(f"❌ 大纲生成失败: {str(e)}")
            # 创建错误响应数据
            outline_data = {
                "title": project.title,
                "theme": project.theme,
                "dynasty": project.era,
                "duration": project.target_duration,
                "genre": "剧情",
                "characters": [],
                "scenes": [],
                "plot_summary": f"生成失败: {str(e)}",
                "key_conflicts": [],
                "emotional_arc": "",
                "visual_style": {},
                "audio_style": {},
                "estimated_scenes": 0,
                "estimated_cost": 0.0,
                "production_notes": [f"错误: {str(e)}"]
            }
        
        # 创建ScriptData
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        script_data = ScriptData(
            script_id=f"script_{project.title}_{timestamp}",
            title=outline_data["title"],
            theme=project.theme,
            era=project.era,
            summary=outline_data["plot_summary"],  # 使用plot_summary而不是summary
            characters=outline_data["characters"],
            scenes=outline_data["scenes"],
            dialogues=[],  # 初始化为空列表
            media_cues=[]  # 初始化为空列表
        )
        
        # 更新项目
        project.script_data = script_data
        project.status = "outline_complete"
        project.updated_at = datetime.now().isoformat()
        
        # 保存项目
        await self.save_project(project)
        
        print("✅ 大纲生成完成！")
        print(f"📝 摘要: {outline_data['plot_summary']}")
        print(f"👥 角色数: {len(outline_data['characters'])}")
        print(f"🎬 场景数: {len(outline_data['scenes'])}")
        
        return project
    
    async def generate_detailed_scenes(self, project: ScriptProject) -> ScriptProject:
        """为项目生成详细场景"""
        print(f"🎬 为项目 '{project.title}' 生成详细场景...")
        
        if not project.script_data or not project.script_data.scenes:
            self.logger.error("❌ 项目没有场景信息，请先生成大纲")
            return project
        
        # 更新项目状态
        project.status = "generating_scenes"
        project.updated_at = datetime.now().isoformat()
        await self.save_project(project)
        
        # 为每个场景生成详细信息
        updated_scenes = []
        for i, scene in enumerate(project.script_data.scenes):
            print(f"📝 生成场景 {i+1}/{len(project.script_data.scenes)}: {scene.get('title', '未命名场景')}")
            
            # 构建提示词
            prompt = f"""
            请为以下剧本场景生成详细信息：
            
            剧本标题：{project.script_data.title}
            场景标题：{scene.get('title', '未命名场景')}
            场景位置：{scene.get('location', '未知地点')}
            场景时间：{scene.get('time_period', '未知时间')}
            场景描述：{scene.get('description', '无描述')}
            场景时长：{scene.get('duration', 60)}秒
            
            请生成以下内容：
            1. 详细场景描述（包含环境、氛围、视觉元素等）
            2. 角色动作和表情
            3. 镜头建议（包含景别、角度、运动等）
            4. 音效和背景音乐建议
            5. 灯光和色彩建议
            
            请以JSON格式返回，包含以下字段：
            - title: 场景标题
            - location: 场景位置
            - time_period: 场景时间
            - description: 详细场景描述
            - duration: 场景时长（秒）
            - visual_elements: 视觉元素列表
            - character_actions: 角色动作描述
            - camera_directions: 镜头建议列表
            - sound_effects: 音效建议列表
            - lighting: 灯光建议
            - color_palette: 色彩建议
            """
            
            # 构建系统消息
            system_message = """
            你是一位专业的场景描述专家，擅长创作生动、详细的场景描述。
            请根据提供的信息，为每个场景创建丰富的视觉和情感体验。
            确保描述能够为后续的对话生成提供充分的背景和氛围。
            """
            
            # 创建LLM请求
            request = LLMRequest(
                prompt=prompt,
                system_message=system_message,
                temperature=0.7,
                max_tokens=2000
            )
            
            # 调用LLM服务
            try:
                response = await self.llm_service_manager.call_llm(request)
                response_text = response.content
                
                # 检查响应内容是否为空
                if not response_text:
                    raise ValueError("模型返回的内容为空")
                
                # 解析响应
                try:
                    scene_data = json.loads(response_text)
                except json.JSONDecodeError:
                    # 如果返回的不是JSON格式，尝试提取JSON部分
                    import re
                    json_match = re.search(r'```json\n(.*?)\n```', response_text, re.DOTALL)
                    if json_match:
                        scene_data = json.loads(json_match.group(1))
                    else:
                        raise ValueError("无法解析模型返回的场景数据")
                
                # 更新场景信息
                updated_scene = scene.copy()
                updated_scene.update({
                    "description": scene_data.get("description", scene.get("description", "")),
                    "visual_elements": scene_data.get("visual_elements", []),
                    "character_actions": scene_data.get("character_actions", ""),
                    "camera_directions": scene_data.get("camera_directions", []),
                    "sound_effects": scene_data.get("sound_effects", []),
                    "lighting": scene_data.get("lighting", ""),
                    "color_palette": scene_data.get("color_palette", [])
                })
                
                print(f"  ✅ 场景 '{scene.get('title', '未命名场景')}' 详细描述已生成")
                
            except Exception as e:
                self.logger.error(f"❌ 场景 {i+1} 生成失败: {str(e)}")
                # 使用原始场景信息，添加错误标记
                updated_scene = scene.copy()
                updated_scene["generation_error"] = str(e)
            
            updated_scenes.append(updated_scene)
        
        # 更新项目
        project.script_data.scenes = updated_scenes
        project.status = "scenes_complete"
        project.updated_at = datetime.now().isoformat()
        
        # 保存项目
        await self.save_project(project)
        
        print("✅ 详细场景生成完成！")
        
        return project
    
    async def generate_dialogues(self, project: ScriptProject) -> ScriptProject:
        """为项目生成对话"""
        print(f"🎬 为项目 '{project.title}' 生成对话...")
        
        if not project.script_data or not project.script_data.scenes:
            self.logger.error("❌ 项目没有场景信息，请先生成大纲")
            return project
        
        if not project.script_data.characters:
            self.logger.error("❌ 项目没有角色信息，请先生成大纲")
            return project
        
        # 更新项目状态
        project.status = "generating_dialogues"
        project.updated_at = datetime.now().isoformat()
        await self.save_project(project)
        
        # 为每个场景生成对话
        updated_scenes = []
        all_dialogues = []
        
        for i, scene in enumerate(project.script_data.scenes):
            print(f"💬 生成场景 {i+1}/{len(project.script_data.scenes)} 的对话: {scene.get('title', '未命名场景')}")
            
            # 构建提示词
            characters_info = "\n".join([f"- {char.get('name', '未知角色')}: {char.get('description', '无描述')}" 
                                       for char in project.script_data.characters])
            
            prompt = f"""
            请为以下剧本场景生成对话：
            
            剧本标题：{project.script_data.title}
            场景标题：{scene.get('title', '未命名场景')}
            场景位置：{scene.get('location', '未知地点')}
            场景时间：{scene.get('time_period', '未知时间')}
            场景描述：{scene.get('description', '无描述')}
            
            角色信息：
            {characters_info}
            
            请根据场景描述和角色特点，生成自然、生动的对话。对话应该：
            1. 符合角色的性格和背景
            2. 推动剧情发展
            3. 体现场景的氛围和情感
            4. 长度适中，与场景时长相匹配
            
            请以JSON格式返回，包含以下字段：
            - scene_title: 场景标题
            - dialogues: 对话列表，每个对话包含character（角色名）、content（对话内容）、emotion（情感）、action（动作描述）
            - scene_notes: 场景备注
            """
            
            # 构建系统消息
            system_message = """
            你是一位专业的对话创作专家，擅长创作自然、生动的角色对话。
            请根据场景描述和角色特点，创作符合角色性格和场景氛围的对话。
            确保对话能够推动剧情发展，展现角色关系和情感变化。
            """
            
            # 创建LLM请求
            request = LLMRequest(
                prompt=prompt,
                system_message=system_message,
                temperature=0.7,
                max_tokens=3000
            )
            
            # 调用LLM服务
            try:
                response = await self.llm_service_manager.call_llm(request)
                response_text = response.content
                
                # 检查响应内容是否为空
                if not response_text:
                    raise ValueError("模型返回的内容为空")
                
                # 解析响应
                try:
                    dialogue_data = json.loads(response_text)
                except json.JSONDecodeError:
                    # 如果返回的不是JSON格式，尝试提取JSON部分
                    import re
                    json_match = re.search(r'```json\n(.*?)\n```', response_text, re.DOTALL)
                    if json_match:
                        dialogue_data = json.loads(json_match.group(1))
                    else:
                        raise ValueError("无法解析模型返回的对话数据")
                
                # 处理对话数据
                scene_dialogues = dialogue_data.get("dialogues", [])
                
                # 为每条对话添加场景信息
                for dialogue in scene_dialogues:
                    dialogue_entry = {
                        "scene_id": i + 1,
                        "scene_title": scene.get('title', '未命名场景'),
                        "character": dialogue.get("character", "未知角色"),
                        "content": dialogue.get("content", ""),
                        "emotion": dialogue.get("emotion", "中性"),
                        "action": dialogue.get("action", ""),
                        "timestamp": len(all_dialogues) + 1
                    }
                    all_dialogues.append(dialogue_entry)
                
                # 更新场景信息
                updated_scene = scene.copy()
                updated_scene["dialogues_generated"] = True
                updated_scene["scene_notes"] = dialogue_data.get("scene_notes", "")
                
                print(f"  ✅ 场景 '{scene.get('title', '未命名场景')}' 的对话已生成")
                
            except Exception as e:
                self.logger.error(f"❌ 场景 {i+1} 对话生成失败: {str(e)}")
                # 使用原始场景信息，添加错误标记
                updated_scene = scene.copy()
                updated_scene["dialogue_generation_error"] = str(e)
            
            updated_scenes.append(updated_scene)
        
        # 更新项目
        project.script_data.scenes = updated_scenes
        project.script_data.dialogues = all_dialogues
        project.status = "dialogues_complete"
        project.updated_at = datetime.now().isoformat()
        
        # 保存项目
        await self.save_project(project)
        
        print("✅ 对话生成完成！")
        print(f"💬 共生成 {len(all_dialogues)} 条对话")
        
        return project
    
    async def finalize_script(self, project: ScriptProject) -> ScriptProject:
        """完成剧本，进行最终润色和格式化"""
        print(f"🎬 完成剧本 '{project.title}' 的最终润色和格式化...")
        
        # 构建提示词
        script_summary = f"""
        剧本标题：{project.title}
        主题：{project.theme}
        时代：{project.era}
        类型：{project.genre if hasattr(project, 'genre') else '剧情'}
        剧情摘要：{project.script_data.summary if project.script_data else ''}
        
        主要角色：
        {chr(10).join([f"- {char.get('name', '未知角色')}: {char.get('description', '')}" for char in project.script_data.characters]) if project.script_data else ''}
        
        场景数量：{len(project.script_data.scenes) if project.script_data else 0}
        """
        
        prompt = f"""
        请对以下剧本进行最终润色和格式化：
        
        {script_summary}
        
        请完成以下任务：
        1. 检查剧本的整体结构和逻辑连贯性
        2. 优化角色对话，使其更自然、更符合角色性格
        3. 确保场景描述生动、详细
        4. 添加必要的转场和过渡说明
        5. 检查并修正任何不一致或错误
        
        请以JSON格式返回，包含以下字段：
        - title: 剧本标题
        - final_summary: 最终剧情摘要
        - character_development_notes: 角色发展说明
        - scene_flow_notes: 场景流程说明
        - dialogue_improvements: 对话改进建议
        - overall_quality_rating: 整体质量评分（1-10）
        - production_recommendations: 制作建议
        """
        
        # 构建系统消息
        system_message = """
        你是一位专业的剧本编辑和润色专家，擅长优化和完善剧本。
        请仔细检查剧本的各个方面，提供有价值的改进建议。
        确保剧本在结构、角色、对话和场景描述等方面都达到专业水准。
        """
        
        # 创建LLM请求
        request = LLMRequest(
            prompt=prompt,
            system_message=system_message,
            temperature=0.5,
            max_tokens=2000
        )
        
        # 调用LLM服务
        try:
            response = await self.llm_service_manager.call_llm(request)
            response_text = response.content if response else None
            
            # 检查响应内容是否为空
            if not response_text:
                raise ValueError("模型返回的内容为空")
            
            # 解析响应
            try:
                finalization_data = json.loads(response_text)
            except json.JSONDecodeError:
                # 如果返回的不是JSON格式，尝试提取JSON部分
                import re
                json_match = re.search(r'```json\n(.*?)\n```', response_text, re.DOTALL)
                if json_match:
                    finalization_data = json.loads(json_match.group(1))
                else:
                    raise ValueError("无法解析模型返回的剧本润色数据")
            
            # 更新项目信息
            if project.script_data:
                project.script_data.final_summary = finalization_data.get("final_summary", project.script_data.summary)
                project.script_data.character_development_notes = finalization_data.get("character_development_notes", "")
                project.script_data.scene_flow_notes = finalization_data.get("scene_flow_notes", "")
                project.script_data.dialogue_improvements = finalization_data.get("dialogue_improvements", "")
                project.script_data.overall_quality_rating = finalization_data.get("overall_quality_rating", 5)
                project.script_data.production_recommendations = finalization_data.get("production_recommendations", [])
            
            print(f"  ✅ 剧本 '{project.title}' 已完成润色和格式化")
            
        except Exception as e:
            self.logger.error(f"❌ 剧本 '{project.title}' 润色失败: {str(e)}")
            # 设置默认值
            if project.script_data:
                project.script_data.final_summary = project.script_data.summary
                project.script_data.character_development_notes = f"润色失败: {str(e)}"
                project.script_data.scene_flow_notes = ""
                project.script_data.dialogue_improvements = ""
                project.script_data.overall_quality_rating = 3
                project.script_data.production_recommendations = [f"错误: {str(e)}"]
        
        # 更新项目状态
        project.status = "final"
        project.updated_at = datetime.now().isoformat()
        
        # 保存项目
        await self.save_project(project)
        
        # 生成最终剧本文件
        await self._export_script(project)
        
        print(f"✅ 剧本 '{project.title}' 已完成")
        return project
    
    async def save_project(self, project: ScriptProject):
        """保存项目到文件"""
        project_file = self.projects_dir / f"{project.id}.json"
        
        with open(project_file, 'w', encoding='utf-8') as f:
            json.dump(project.to_dict(), f, ensure_ascii=False, indent=2)
    
    async def load_project(self, project_id: str) -> Optional[ScriptProject]:
        """加载项目"""
        project_file = self.projects_dir / f"{project_id}.json"
        
        if not project_file.exists():
            return None
        
        with open(project_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return ScriptProject.from_dict(data)
    
    async def list_projects(self) -> List[ScriptProject]:
        """列出所有项目"""
        projects = []
        
        for project_file in self.projects_dir.glob("*.json"):
            try:
                with open(project_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                projects.append(ScriptProject.from_dict(data))
            except Exception as e:
                print(f"Warning: Failed to load project {project_file}: {e}")
        
        # 按创建时间排序
        projects.sort(key=lambda p: p.created_at, reverse=True)
        return projects
    
    async def delete_project(self, project_id: str) -> bool:
        """删除项目"""
        project_file = self.projects_dir / f"{project_id}.json"
        
        if project_file.exists():
            project_file.unlink()
            return True
        return False
    
    async def _export_script(self, project: ScriptProject):
        """导出剧本为可读格式"""
        if not project.script_data:
            return
        
        export_dir = self.projects_dir / "exports"
        export_dir.mkdir(exist_ok=True)
        
        # 导出为文本格式
        script_file = export_dir / f"{project.title}_{project.id[:8]}.txt"
        
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(f"剧本标题: {project.script_data.title}\n")
            f.write(f"主题: {project.theme}\n")
            f.write(f"时代: {project.era}\n")
            f.write(f"目标时长: {project.target_duration}秒\n")
            f.write(f"创建时间: {project.created_at}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"剧情摘要:\n{project.script_data.summary}\n\n")
            
            f.write("角色列表:\n")
            for i, char in enumerate(project.script_data.characters, 1):
                f.write(f"{i}. {char.name} - {char.description}\n")
            f.write("\n")
            
            f.write("场景列表:\n")
            for i, scene in enumerate(project.script_data.scenes, 1):
                f.write(f"\n场景 {i}: {scene.title}\n")
                f.write(f"地点: {scene.location}\n")
                f.write(f"时间: {scene.time_period}\n")
                f.write(f"描述: {scene.description}\n")
                
                if hasattr(scene, 'dialogue_lines') and scene.dialogue_lines:
                    f.write("对话:\n")
                    for line in scene.dialogue_lines:
                        f.write(f"  {line.character_name}: {line.content}\n")
        
        print(f"📄 剧本已导出到: {script_file}")


class ScriptManager:
    """剧本管理器，提供高级剧本管理功能"""
    
    def __init__(self, config_manager):
        """初始化剧本管理器"""
        self.config_manager = config_manager
        self.script_generator = ScriptGenerator(config_manager)
    
    async def generate_complete_script(self, title: str, theme: str, era: str, 
                                      target_duration: int = 600) -> ScriptProject:
        """一键生成完整剧本"""
        print(f"🎬 开始生成完整剧本: {title}")
        
        # 创建项目
        project = await self.script_generator.create_project(
            title=title,
            theme=theme,
            era=era,
            target_duration=target_duration
        )
        
        # 生成大纲
        project = await self.script_generator.generate_outline(project)
        
        # 生成详细场景
        project = await self.script_generator.generate_detailed_scenes(project)
        
        # 生成对话
        project = await self.script_generator.generate_dialogues(project)
        
        # 完成剧本
        project = await self.script_generator.finalize_script(project)
        
        print(f"🎉 完整剧本 '{title}' 生成完成！")
        return project
    
    async def generate_outline_only(self, title: str, theme: str, era: str, 
                                  target_duration: int = 600) -> ScriptProject:
        """只生成大纲"""
        print(f"🎬 开始生成剧本大纲: {title}")
        
        # 创建项目
        project = await self.script_generator.create_project(
            title=title,
            theme=theme,
            era=era,
            target_duration=target_duration
        )
        
        # 生成大纲
        project = await self.script_generator.generate_outline(project)
        
        print(f"🎉 剧本大纲 '{title}' 生成完成！")
        return project
    
    async def continue_from_outline(self, project_id: str) -> ScriptProject:
        """从大纲继续生成完整剧本"""
        print(f"🎬 从大纲继续生成剧本: {project_id}")
        
        # 加载项目
        project = await self.script_generator.load_project(project_id)
        
        if project.status != "outline_complete":
            self.logger.error(f"❌ 项目 '{project_id}' 的状态不是 'outline_complete'，无法继续生成")
            return project
        
        # 生成详细场景
        project = await self.script_generator.generate_detailed_scenes(project)
        
        # 生成对话
        project = await self.script_generator.generate_dialogues(project)
        
        # 完成剧本
        project = await self.script_generator.finalize_script(project)
        
        print(f"🎉 剧本 '{project.title}' 生成完成！")
        return project
    
    async def get_video_production_script(self, project_id: str) -> dict:
        """获取视频制作剧本数据"""
        print(f"🎬 获取视频制作剧本数据: {project_id}")
        
        # 加载项目
        project = await self.script_generator.load_project(project_id)
        
        if project.status != "final":
            self.logger.error(f"❌ 项目 '{project_id}' 未完成，无法获取视频制作剧本数据")
            return {}
        
        # 构建视频制作剧本数据
        video_script = {
            "title": project.title,
            "theme": project.theme,
            "era": project.era,
            "genre": project.genre,
            "target_duration": project.target_duration,
            "plot_summary": project.plot_summary,
            "key_conflicts": project.key_conflicts,
            "emotional_arc": project.emotional_arc,
            "visual_style": project.visual_style,
            "audio_style": project.audio_style,
            "estimated_scenes": project.estimated_scenes,
            "estimated_cost": project.estimated_cost,
            "production_notes": project.production_notes,
            "characters": [],
            "scenes": []
        }
        
        # 添加角色数据
        if project.script_data and project.script_data.characters:
            for char in project.script_data.characters:
                char_data = {
                    "name": char.name,
                    "title": char.title,
                    "description": char.description,
                    "personality": char.personality,
                    "appearance": char.appearance,
                    "voice_style": char.voice_style,
                    "era": char.era,
                    "social_status": char.social_status
                }
                video_script["characters"].append(char_data)
        
        # 添加场景数据
        if project.script_data and project.script_data.scenes:
            for scene in project.script_data.scenes:
                scene_data = {
                    "scene_id": scene.scene_id,
                    "title": scene.title,
                    "location": scene.location,
                    "time_period": scene.time_period,
                    "weather": scene.weather,
                    "atmosphere": scene.atmosphere,
                    "background_description": scene.background_description,
                    "lighting": scene.lighting,
                    "camera_angle": scene.camera_angle,
                    "duration": scene.duration
                }
                video_script["scenes"].append(scene_data)
        
        # 添加最终润色信息
        if project.script_data:
            video_script["final_summary"] = project.script_data.final_summary
            video_script["character_development_notes"] = project.script_data.character_development_notes
            video_script["scene_flow_notes"] = project.script_data.scene_flow_notes
            video_script["dialogue_improvements"] = project.script_data.dialogue_improvements
            video_script["overall_quality_rating"] = project.script_data.overall_quality_rating
            video_script["production_recommendations"] = project.script_data.production_recommendations
        
        print(f"🎉 视频制作剧本数据获取完成！")
        return video_script
