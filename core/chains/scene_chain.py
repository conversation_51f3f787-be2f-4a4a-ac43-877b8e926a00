"""场景详细生成链路

负责将大纲中的场景信息扩展为详细的制作指令，包括：
- 详细的视觉描述和镜头设计
- 角色动作和表情指导
- 环境音效和背景音乐
- 图像和视频生成提示词
"""

import asyncio
import sys
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

from langchain.chains.base import Chain
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.schema.runnable import RunnablePassthrough
from pydantic import BaseModel, Field
import jinja2

from ..models import (
    MediaType, TaskStatus, QualityLevel,
    CharacterData, SceneData, ProjectData
)
from ..config import ConfigManager
from ..cost_control import CostController


class SceneRequest(BaseModel):
    """场景详细化请求"""
    scene_data: SceneData = Field(description="基础场景数据")
    characters: List[CharacterData] = Field(description="相关角色数据")
    project_context: Dict[str, Any] = Field(description="项目上下文")
    style_preferences: Dict[str, str] = Field(default_factory=dict, description="风格偏好")
    technical_requirements: Dict[str, Any] = Field(default_factory=dict, description="技术要求")


class ShotData(BaseModel):
    """镜头数据"""
    shot_id: str = Field(description="镜头ID")
    type: str = Field(description="镜头类型：特写/中景/全景/远景")
    angle: str = Field(description="镜头角度：平视/俯视/仰视/侧视")
    movement: str = Field(description="镜头运动：静止/推拉/摇移/跟随")
    duration: float = Field(description="镜头时长（秒）")
    depth_of_field: str = Field(description="景深效果")
    composition: str = Field(description="构图描述")


class DialogueData(BaseModel):
    """对白数据"""
    character: str = Field(description="角色名称")
    text: str = Field(description="对白内容")
    emotion: str = Field(description="情感状态")
    tone: str = Field(description="语调")
    timing: float = Field(description="开始时间（秒）")
    duration: float = Field(description="持续时间（秒）")


class AudioData(BaseModel):
    """音频数据"""
    background_music: Dict[str, Any] = Field(description="背景音乐")
    ambient_sounds: List[Dict[str, str]] = Field(description="环境音效")
    sound_effects: List[Dict[str, str]] = Field(description="特效音")
    voice_requirements: Dict[str, str] = Field(description="配音要求")


class VisualEffects(BaseModel):
    """视觉效果"""
    color_grading: str = Field(description="调色方案")
    filters: List[str] = Field(description="滤镜效果")
    transitions: Dict[str, str] = Field(description="转场效果")
    overlays: List[str] = Field(description="叠加效果")


class SceneResponse(BaseModel):
    """场景详细化响应"""
    scene_id: str
    title: str
    duration: float
    
    # 环境设定
    environment: Dict[str, str] = Field(description="环境描述")
    lighting: Dict[str, str] = Field(description="光线设定")
    weather: Dict[str, str] = Field(description="天气状况")
    
    # 镜头设计
    shots: List[ShotData] = Field(description="镜头列表")
    
    # 角色信息
    character_actions: Dict[str, Dict[str, str]] = Field(description="角色动作")
    character_positions: Dict[str, str] = Field(description="角色位置")
    character_expressions: Dict[str, str] = Field(description="角色表情")
    
    # 对白内容
    dialogues: List[DialogueData] = Field(description="对白列表")
    
    # 音频设计
    audio: AudioData = Field(description="音频设计")
    
    # 视觉效果
    visual_effects: VisualEffects = Field(description="视觉效果")
    
    # 生成提示词
    image_prompts: List[str] = Field(description="图像生成提示词")
    video_prompts: List[str] = Field(description="视频生成提示词")
    audio_prompts: List[str] = Field(description="音频生成提示词")
    
    # 制作指导
    production_notes: List[str] = Field(description="制作备注")
    technical_specs: Dict[str, Any] = Field(description="技术规格")
    quality_checkpoints: List[str] = Field(description="质量检查点")


class SceneOutputParser(BaseOutputParser[SceneResponse]):
    """场景输出解析器"""
    
    def parse(self, text: str) -> SceneResponse:
        """解析LLM输出为结构化场景数据"""
        try:
            import json
            logger.info(f"🔍 解析场景响应，长度: {len(text)} 字符")
            
            # 预处理文本，移除markdown代码块标记
            processed_text = text.strip()
            if processed_text.startswith('```json'):
                # 找到结束标记
                end_marker = processed_text.find('```', 6)
                if end_marker != -1:
                    processed_text = processed_text[7:end_marker].strip()
                else:
                    # 如果没有结束标记，尝试移除开始标记
                    processed_text = processed_text[7:].strip()
            elif processed_text.startswith('```'):
                # 找到结束标记
                end_marker = processed_text.find('```', 3)
                if end_marker != -1:
                    processed_text = processed_text[4:end_marker].strip()
                else:
                    # 如果没有结束标记，尝试移除开始标记
                    processed_text = processed_text[4:].strip()
            
            # 检查是否是错误响应
            if processed_text.strip().startswith('{') and '"error"' in processed_text:
                logger.warning("⚠️  检测到错误响应")
                data = json.loads(processed_text)
                if data.get('scene_id') == 'error':
                    logger.error(f"❌ 错误详情: {data.get('error', '未知错误')}")
                    return SceneResponse(**data)
            
            # 尝试解析JSON
            data = json.loads(processed_text)
            
            # 验证必要字段
            if not data.get('scene_id'):
                data['scene_id'] = f"scene_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if not data.get('title'):
                data['title'] = "未命名场景"
            if not data.get('duration'):
                data['duration'] = 0.0
            if not data.get('shots'):
                data['shots'] = []
            
            # 数据类型转换和修正
            # 1. 处理environment.props字段（将列表转换为字符串）
            if 'environment' in data and 'props' in data['environment']:
                if isinstance(data['environment']['props'], list):
                    data['environment']['props'] = ', '.join(str(item) for item in data['environment']['props'])
            
            # 2. 处理audio.sound_effects中的timing字段（将浮点数转换为字符串）
            if 'audio' in data and 'sound_effects' in data['audio']:
                for effect in data['audio']['sound_effects']:
                    if 'timing' in effect and isinstance(effect['timing'], (int, float)):
                        effect['timing'] = str(effect['timing'])
            
            logger.info(f"✅ 场景解析成功，ID: {data.get('scene_id')}, 镜头数: {len(data.get('shots', []))}")
            
            return SceneResponse(**data)
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {str(e)}")
            logger.error(f"   原始内容前200字符: {text[:200]}...")
            
            # 返回默认结构
            return SceneResponse(
                scene_id="parse_error",
                title="解析失败",
                duration=0.0,
                environment={},
                lighting={},
                weather={},
                shots=[],
                character_actions={},
                character_positions={},
                character_expressions={},
                dialogues=[],
                audio=AudioData(
                    background_music={},
                    ambient_sounds=[],
                    sound_effects=[],
                    voice_requirements={}
                ),
                visual_effects=VisualEffects(
                    color_grading="",
                    filters=[],
                    transitions={},
                    overlays=[]
                ),
                image_prompts=[],
                video_prompts=[],
                audio_prompts=[],
                production_notes=[f"JSON解析错误: {str(e)}", f"原始内容: {text[:200]}..."],
                technical_specs={},
                quality_checkpoints=[]
            )
        except Exception as e:
            logger.error(f"❌ 场景解析失败: {str(e)}")
            import traceback
            logger.error(f"   错误堆栈: {traceback.format_exc()}")
            
            # 返回默认结构
            return SceneResponse(
                scene_id="error",
                title="解析失败",
                duration=0.0,
                environment={},
                lighting={},
                weather={},
                shots=[],
                character_actions={},
                character_positions={},
                character_expressions={},
                dialogues=[],
                audio=AudioData(
                    background_music={},
                    ambient_sounds=[],
                    sound_effects=[],
                    voice_requirements={}
                ),
                visual_effects=VisualEffects(
                    color_grading="",
                    filters=[],
                    transitions={},
                    overlays=[]
                ),
                image_prompts=[],
                video_prompts=[],
                audio_prompts=[],
                production_notes=[f"解析错误: {str(e)}"],
                technical_specs={},
                quality_checkpoints=[]
            )
        except Exception as e:
            logger.error(f"❌ 场景解析失败: {str(e)}")
            import traceback
            logger.error(f"   错误堆栈: {traceback.format_exc()}")
            
            # 返回默认结构
            return SceneResponse(
                scene_id="error",
                title="解析失败",
                duration=0.0,
                environment={},
                lighting={},
                weather={},
                shots=[],
                character_actions={},
                character_positions={},
                character_expressions={},
                dialogues=[],
                audio=AudioData(
                    background_music={},
                    ambient_sounds=[],
                    sound_effects=[],
                    voice_requirements={}
                ),
                visual_effects=VisualEffects(
                    color_grading="",
                    filters=[],
                    transitions={},
                    overlays=[]
                ),
                image_prompts=[],
                video_prompts=[],
                audio_prompts=[],
                production_notes=[f"解析错误: {str(e)}"],
                technical_specs={},
                quality_checkpoints=[]
            )
    
    @property
    def _type(self) -> str:
        return "scene_output_parser"


class SceneChain:
    """场景详细生成链路"""
    
    def __init__(self, config_manager: ConfigManager, cost_controller: CostController):
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.llm = self._initialize_llm()
        self.template_env = self._initialize_templates()
        self.output_parser = SceneOutputParser()
        
        # 构建链路
        self.chain = self._build_chain()
    
    def _initialize_llm(self):
        """初始化语言模型"""
        from ..llm_caller_adapter import LLMCallerAdapter
        self.llm_adapter = LLMCallerAdapter(self.config_manager)
        
        text_config = self.config_manager.get_service_config('text')
        return {
            'model': text_config.get('model', 'gpt-4'),
            'temperature': text_config.get('temperature', 0.7),
            'max_tokens': text_config.get('max_tokens', 4000)
        }
    
    def _initialize_templates(self):
        """初始化模板环境"""
        template_dir = Path(__file__).parent.parent.parent / 'templates'
        return jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_dir),
            autoescape=True
        )
    
    def _build_chain(self):
        """构建处理链路"""
        prompt_template = PromptTemplate(
            input_variables=[
                "scene_data", "characters", "project_context",
                "style_preferences", "technical_requirements"
            ],
            template=self._get_scene_prompt_template()
        )
        
        chain = (
            RunnablePassthrough()
            | prompt_template
            | self._llm_call
            | self.output_parser
        )
        
        return chain
    
    def _get_scene_prompt_template(self) -> str:
        """获取场景详细化提示模板"""
        return """
你是一位专业的影视制作导演，擅长将剧本场景转换为详细的拍摄指导。

请根据以下信息，为场景制作详细的拍摄和制作指导：

## 基础场景信息
{scene_data}

## 相关角色信息
{characters}

## 项目上下文
{project_context}

## 风格偏好
{style_preferences}

## 技术要求
{technical_requirements}

## 输出要求
请以JSON格式输出详细的场景制作指导，包含以下结构：

```json
{{
  "scene_id": "场景唯一标识",
  "title": "场景标题",
  "duration": 场景时长秒数,
  
  "environment": {{
    "main_setting": "主要场景描述",
    "background_elements": "背景元素描述",
    "props": "道具清单",
    "atmosphere": "氛围描述"
  }},
  
  "lighting": {{
    "type": "光线类型（自然光/人工光/混合）",
    "direction": "光线方向",
    "intensity": "光线强度",
    "color_temperature": "色温",
    "mood": "光线营造的情绪"
  }},
  
  "weather": {{
    "condition": "天气状况",
    "visibility": "能见度",
    "effects": "天气效果"
  }},
  
  "shots": [
    {{
      "shot_id": "镜头ID",
      "type": "镜头类型",
      "angle": "拍摄角度",
      "movement": "镜头运动",
      "duration": 镜头时长,
      "depth_of_field": "景深效果",
      "composition": "构图描述"
    }}
  ],
  
  "character_actions": {{
    "角色名": {{
      "action": "动作描述",
      "gesture": "手势描述",
      "movement": "移动路径"
    }}
  }},
  
  "character_positions": {{
    "角色名": "位置描述"
  }},
  
  "character_expressions": {{
    "角色名": "表情描述"
  }},
  
  "dialogues": [
    {{
      "character": "角色名",
      "text": "对白内容",
      "emotion": "情感状态",
      "tone": "语调",
      "timing": 开始时间,
      "duration": 持续时间
    }}
  ],
  
  "audio": {{
    "background_music": {{
      "style": "音乐风格",
      "mood": "音乐情绪",
      "volume": "音量级别",
      "instruments": "主要乐器"
    }},
    "ambient_sounds": [
      {{
        "type": "环境音类型",
        "description": "音效描述",
        "volume": "音量"
      }}
    ],
    "sound_effects": [
      {{
        "trigger": "触发时机",
        "sound": "音效描述",
        "timing": "时间点"
      }}
    ],
    "voice_requirements": {{
      "clarity": "清晰度要求",
      "emotion": "情感表达",
      "pace": "语速要求"
    }}
  }},
  
  "visual_effects": {{
    "color_grading": "调色方案",
    "filters": ["滤镜效果列表"],
    "transitions": {{
      "in": "入场转场",
      "out": "出场转场"
    }},
    "overlays": ["叠加效果列表"]
  }},
  
  "image_prompts": [
    "详细的图像生成提示词，包含场景、角色、光线、构图等要素"
  ],
  
  "video_prompts": [
    "视频生成提示词，包含动作、镜头运动、时长等要素"
  ],
  
  "audio_prompts": [
    "音频生成提示词，包含音乐风格、音效、配音要求等"
  ],
  
  "production_notes": [
    "制作备注和注意事项"
  ],
  
  "technical_specs": {{
    "resolution": "分辨率要求",
    "fps": "帧率要求",
    "codec": "编码格式",
    "quality": "质量等级"
  }},
  
  "quality_checkpoints": [
    "质量检查要点"
  ]
}}
```

## 制作要点
1. 确保镜头设计符合场景情绪和剧情需要
2. 角色动作和表情要与对白内容协调
3. 音效设计要增强场景氛围
4. 图像提示词要足够详细，便于AI生成
5. 考虑技术实现的可行性和成本控制
6. 保持与整体项目风格的一致性

请开始制作详细指导：
"""
    
    async def _llm_call(self, prompt) -> str:
        """调用语言模型"""
        try:
            # 使用LLMCallerAdapter调用模型
            return await self.llm_adapter.call_llm(
                prompt=prompt,
                model=self.llm['model'],
                temperature=self.llm['temperature'],
                max_tokens=self.llm['max_tokens']
            )
            
        except Exception as e:
            import json
            error_msg = f"LLM调用失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            error_response = {
                "error": error_msg,
                "scene_id": "error",
                "title": "错误",
                "duration": 0.0,
                "environment": {},
                "lighting": {},
                "weather": {},
                "shots": [],
                "character_actions": {},
                "character_positions": {},
                "character_expressions": {},
                "dialogues": [],
                "audio": {
                    "background_music": {},
                    "ambient_sounds": [],
                    "sound_effects": [],
                    "voice_requirements": {}
                },
                "visual_effects": {
                    "color_grading": "",
                    "filters": [],
                    "transitions": {},
                    "overlays": []
                },
                "image_prompts": [],
                "video_prompts": [],
                "audio_prompts": [],
                "production_notes": [error_msg],
                "technical_specs": {},
                "quality_checkpoints": []
            }
            return json.dumps(error_response, ensure_ascii=False)
    
    async def generate_scene_details(self, request: SceneRequest) -> SceneResponse:
        """生成场景详细信息"""
        try:
            # 成本检查
            estimated_cost = self._estimate_cost(request)
            if not await self.cost_controller.check_budget(estimated_cost):
                raise ValueError(f"预算不足，需要 ${estimated_cost:.2f}")
            
            # 准备输入数据
            input_data = {
                "scene_data": request.scene_data.dict(),
                "characters": [char.dict() for char in request.characters],
                "project_context": request.project_context,
                "style_preferences": request.style_preferences,
                "technical_requirements": request.technical_requirements
            }
            
            # 执行链路
            result = await self.chain.ainvoke(input_data)
            
            # 记录成本
            await self.cost_controller.record_cost(estimated_cost, "scene_generation")
            
            return result
            
        except Exception as e:
            # 返回错误响应
            return SceneResponse(
                scene_id=f"error_{datetime.now().timestamp()}",
                title="生成失败",
                duration=0.0,
                environment={},
                lighting={},
                weather={},
                shots=[],
                character_actions={},
                character_positions={},
                character_expressions={},
                dialogues=[],
                audio=AudioData(
                    background_music={},
                    ambient_sounds=[],
                    sound_effects=[],
                    voice_requirements={}
                ),
                visual_effects=VisualEffects(
                    color_grading="",
                    filters=[],
                    transitions={},
                    overlays=[]
                ),
                image_prompts=[],
                video_prompts=[],
                audio_prompts=[],
                production_notes=[f"错误: {str(e)}"],
                technical_specs={},
                quality_checkpoints=[]
            )
    
    def _estimate_cost(self, request: SceneRequest) -> float:
        """估算生成成本"""
        base_cost = 0.08  # 基础成本
        duration_factor = request.scene_data.duration / 60  # 时长因子
        character_factor = len(request.characters) * 0.01  # 角色数量因子
        
        return base_cost + (duration_factor * 0.03) + character_factor
    
    async def optimize_for_production(self, scene: SceneResponse) -> SceneResponse:
        """为制作优化场景"""
        """根据制作能力和成本约束优化场景设计"""
        
        # 优化镜头数量
        if len(scene.shots) > 8:
            # 合并相似镜头
            scene.shots = scene.shots[:8]
            scene.production_notes.append("已优化镜头数量以控制制作成本")
        
        # 优化角色数量
        if len(scene.character_actions) > 4:
            # 减少次要角色的动作复杂度
            scene.production_notes.append("已简化部分角色动作以降低制作难度")
        
        # 优化特效使用
        if len(scene.visual_effects.filters) > 3:
            scene.visual_effects.filters = scene.visual_effects.filters[:3]
            scene.production_notes.append("已减少视觉特效以控制后期成本")
        
        return scene
    
    def export_to_template(self, scene: SceneResponse) -> str:
        """导出为模板格式"""
        try:
            template = self.template_env.get_template('scene_template.j2')
            return template.render(
                scene_number=scene.scene_id,
                scene_title=scene.title,
                duration=scene.duration,
                location=scene.environment.get('main_setting', ''),
                time_period=scene.environment.get('time', ''),
                environment=scene.environment,
                shot=scene.shots[0] if scene.shots else {},
                characters=[
                    {
                        'name': name,
                        'position': scene.character_positions.get(name, ''),
                        'action': scene.character_actions.get(name, {}).get('action', ''),
                        'expression': scene.character_expressions.get(name, ''),
                        'costume': '',
                        'props': ''
                    }
                    for name in scene.character_positions.keys()
                ],
                dialogues=scene.dialogues,
                audio=scene.audio,
                transition=scene.visual_effects.transitions,
                image_prompt=scene.image_prompts[0] if scene.image_prompts else '',
                video=scene.technical_specs,
                post_processing={
                    'color_grading': scene.visual_effects.color_grading,
                    'effects': scene.visual_effects.filters,
                    'subtitle_style': 'default'
                }
            )
        except Exception as e:
            return f"模板渲染失败: {str(e)}"
    
    async def batch_generate_scenes(self, requests: List[SceneRequest]) -> List[SceneResponse]:
        """批量生成场景"""
        results = []
        
        for request in requests:
            try:
                result = await self.generate_scene_details(request)
                results.append(result)
                
                # 添加延迟以避免API限制
                await asyncio.sleep(0.5)
                
            except Exception as e:
                # 添加错误场景
                error_scene = SceneResponse(
                    scene_id=f"batch_error_{len(results)}",
                    title="批量生成失败",
                    duration=0.0,
                    environment={},
                    lighting={},
                    weather={},
                    shots=[],
                    character_actions={},
                    character_positions={},
                    character_expressions={},
                    dialogues=[],
                    audio=AudioData(
                        background_music={},
                        ambient_sounds=[],
                        sound_effects=[],
                        voice_requirements={}
                    ),
                    visual_effects=VisualEffects(
                        color_grading="",
                        filters=[],
                        transitions={},
                        overlays=[]
                    ),
                    image_prompts=[],
                    video_prompts=[],
                    audio_prompts=[],
                    production_notes=[f"批量生成错误: {str(e)}"],
                    technical_specs={},
                    quality_checkpoints=[]
                )
                results.append(error_scene)
        
        return results