"""剧本大纲生成链路

负责根据用户输入的主题、朝代、时长等要求，生成完整的历史短剧大纲。
包括角色设定、场景安排、情节发展等核心要素。
"""

import asyncio
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from loguru import logger

from langchain.chains.base import Chain
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.schema.runnable import RunnablePassthrough
from pydantic import BaseModel, Field
import jinja2

from ..models import (
    MediaType, TaskStatus, QualityLevel,
    CharacterData, SceneData, ProjectData
)
from ..config import ConfigManager
from ..cost_control import CostController


class OutlineRequest(BaseModel):
    """大纲生成请求"""
    title: str = Field(description="剧本标题")
    theme: str = Field(description="主题内容")
    dynasty: str = Field(description="历史朝代")
    duration: int = Field(description="时长（分钟）")
    genre: str = Field(default="历史剧情", description="剧本类型")
    target_audience: str = Field(default="成人", description="目标观众")
    tone: str = Field(default="严肃", description="整体基调")
    key_elements: List[str] = Field(default_factory=list, description="关键元素")
    constraints: List[str] = Field(default_factory=list, description="创作约束")


class OutlineResponse(BaseModel):
    """大纲生成响应"""
    title: str
    theme: str
    dynasty: str
    duration: int
    genre: str
    
    # 角色信息
    characters: List[CharacterData]
    
    # 场景信息
    scenes: List[SceneData]
    
    # 整体信息
    plot_summary: str = Field(description="剧情概要")
    key_conflicts: List[str] = Field(description="主要冲突")
    emotional_arc: str = Field(description="情感弧线")
    visual_style: Dict[str, str] = Field(description="视觉风格")
    audio_style: Dict[str, str] = Field(description="音频风格")
    
    # 制作信息
    estimated_scenes: int = Field(description="预估场景数")
    estimated_cost: float = Field(description="预估成本")
    production_notes: List[str] = Field(description="制作备注")

    # 新增: 规范化字段的验证器，确保数值字段为数字
    @classmethod
    def _normalize_cost(cls, v: Any) -> float:
        """将任意形式的成本输入转换为浮点数（美元）
        支持以下情况：
        - 直接数字或数字字符串（如 "0.12", 0.12, "1,234.5"）
        - 含货币符号/单位（如 "$0.12", "0.12 USD", "0.12美元", "100元"）
        - 中文等级描述（如 "低", "适中/中等", "高", "很高/极高"）映射为预设估值
        - 其他无法解析的情况返回 0.0
        """
        try:
            if v is None:
                return 0.0
            if isinstance(v, (int, float)):
                return float(v)
            if isinstance(v, str):
                s = v.strip().lower()
                # 去除常见货币符号与单位
                for token in ["$", "usd", "美元", "rmb", "cny", "元", "块", "人民币"]:
                    s = s.replace(token, " ")
                s = s.replace(",", " ")
                # 提取第一个数字
                import re
                m = re.search(r"[-+]?[0-9]*\.?[0-9]+", s)
                if m:
                    try:
                        return float(m.group(0))
                    except Exception:
                        pass
                # 中文等级映射
                zh_map = {
                    "低": 0.05,
                    "较低": 0.07,
                    "中": 0.10,
                    "中等": 0.10,
                    "适中": 0.10,
                    "较高": 0.15,
                    "高": 0.20,
                    "很高": 0.30,
                    "极高": 0.35,
                }
                for k, val in zh_map.items():
                    if k in v:
                        return val
            # 默认
            return 0.0
        except Exception:
            return 0.0

    @classmethod
    def _to_int(cls, v: Any, default: int = 0) -> int:
        """将输入安全转换为整数，无法解析时返回默认值"""
        try:
            if v is None:
                return default
            if isinstance(v, bool):
                return int(v)
            if isinstance(v, (int,)):
                return int(v)
            if isinstance(v, float):
                return int(v)
            if isinstance(v, str):
                s = v.strip()
                # 提取第一个整数
                import re
                m = re.search(r"[-+]?[0-9]+", s)
                if m:
                    return int(m.group(0))
        except Exception:
            pass
        return default

    # Pydantic v2 字段级校验器
    from pydantic import field_validator

    @field_validator('estimated_cost', mode='before')
    def _validate_estimated_cost(cls, v: Any) -> float:
        """在模型构建前将 estimated_cost 规范化为 float"""
        return cls._normalize_cost(v)

    @field_validator('estimated_scenes', 'duration', mode='before')
    def _validate_int_fields(cls, v: Any) -> int:
        """在模型构建前将整数字段（estimated_scenes、duration）规范化为 int"""
        return cls._to_int(v, default=0)


class OutlineOutputParser(BaseOutputParser[OutlineResponse]):
    """大纲输出解析器"""
    
    def parse(self, text: str) -> OutlineResponse:
        """解析LLM输出为结构化大纲"""
        import json
        import re
        
        # 尝试多种解析方法
        try:
            # 方法1: 直接JSON解析
            data = json.loads(text)
            return self._create_outline_response(data)
        except json.JSONDecodeError:
            pass
        
        try:
            # 方法2: 提取JSON代码块
            json_match = re.search(r'```json\s*({.*?})\s*```', text, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(1))
                return self._create_outline_response(data)
        except (json.JSONDecodeError, AttributeError):
            pass
        
        try:
            # 方法3: 查找第一个完整的JSON对象
            json_match = re.search(r'{.*}', text, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(0))
                return self._create_outline_response(data)
        except (json.JSONDecodeError, AttributeError):
            pass
        
        # 方法4: 文本解析（作为最后的备选方案）
        return self._parse_text_format(text)
    
    def _create_outline_response(self, data: dict) -> OutlineResponse:
        """从字典创建OutlineResponse对象"""
        # 确保必需字段存在
        required_fields = {
            'title': data.get('title', '未命名剧本'),
            'theme': data.get('theme', ''),
            'dynasty': data.get('dynasty', ''),
            'duration': data.get('duration', 5),
            'genre': data.get('genre', '历史剧情'),
            'plot_summary': data.get('plot_summary', ''),
            'key_conflicts': data.get('key_conflicts', []),
            'emotional_arc': data.get('emotional_arc', ''),
            'visual_style': data.get('visual_style', {}),
            'audio_style': data.get('audio_style', {}),
            'estimated_scenes': data.get('estimated_scenes', 3),
            'estimated_cost': data.get('estimated_cost', 0.0),
            'production_notes': data.get('production_notes', [])
        }
        
        # 处理角色数据
        characters = []
        for char_data in data.get('characters', []):
            if isinstance(char_data, dict):
                characters.append(CharacterData(**char_data))
        
        # 处理场景数据
        scenes = []
        for scene_data in data.get('scenes', []):
            if isinstance(scene_data, dict):
                scenes.append(SceneData(**scene_data))
        
        return OutlineResponse(
            characters=characters,
            scenes=scenes,
            **required_fields
        )
    
    def _parse_text_format(self, text: str) -> OutlineResponse:
        """解析文本格式的输出"""
        import re
        
        # 提取基本信息
        title = self._extract_field(text, r'标题[:：]\s*(.+)')
        theme = self._extract_field(text, r'主题[:：]\s*(.+)')
        dynasty = self._extract_field(text, r'朝代[:：]\s*(.+)')
        
        return OutlineResponse(
            title=title or "文本解析剧本",
            theme=theme or "",
            dynasty=dynasty or "",
            duration=5,
            genre="历史剧情",
            characters=[],
            scenes=[],
            plot_summary=text[:200] + "..." if len(text) > 200 else text,
            key_conflicts=[],
            emotional_arc="",
            visual_style={},
            audio_style={},
            estimated_scenes=3,
            estimated_cost=0.0,
            production_notes=["使用文本解析模式"]
        )
    
    def _extract_field(self, text: str, pattern: str) -> str:
        """从文本中提取字段"""
        import re
        match = re.search(pattern, text)
        return match.group(1).strip() if match else ""
    
    @property
    def _type(self) -> str:
        return "outline_output_parser"


class OutlineChain:
    """剧本大纲生成链路"""
    
    def __init__(self, config_manager: ConfigManager, cost_controller: CostController):
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.llm = self._initialize_llm()
        self.template_env = self._initialize_templates()
        self.output_parser = OutlineOutputParser()
        
        # 构建链路
        self.chain = self._build_chain()
    
    def _initialize_llm(self):
        """初始化语言模型"""
        from ..llm_caller_adapter import LLMCallerAdapter
        self.llm_adapter = LLMCallerAdapter(self.config_manager)
        
        text_config = self.config_manager.get_service_config('text')
        return {
            'model': text_config.get('model', 'gpt-4'),
            'temperature': text_config.get('temperature', 0.7),
            'max_tokens': text_config.get('max_tokens', 4000)
        }
    
    def _initialize_templates(self):
        """初始化模板环境"""
        template_dir = Path(__file__).parent.parent.parent / 'templates'
        return jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_dir),
            autoescape=True
        )
    
    def _build_chain(self):
        """构建处理链路"""
        # 创建提示模板
        prompt_template = PromptTemplate(
            input_variables=[
                "title", "theme", "dynasty", "duration", "genre",
                "target_audience", "tone", "key_elements", "constraints"
            ],
            template=self._get_outline_prompt_template()
        )
        
        # 构建链路
        chain = (
            RunnablePassthrough()
            | prompt_template
            | self._llm_call
            | self.output_parser
        )
        
        return chain
    
    def _get_outline_prompt_template(self) -> str:
        """获取大纲生成提示模板"""
        return """
你是一位专业的历史短剧编剧，擅长创作引人入胜的历史题材短剧。

请根据以下要求创作一个完整的历史短剧大纲：

## 基本要求
- 标题：{title}
- 主题：{theme}
- 朝代：{dynasty}
- 时长：{duration}分钟
- 类型：{genre}
- 目标观众：{target_audience}
- 整体基调：{tone}

## 关键元素
{key_elements}

## 创作约束
{constraints}

## 输出要求
- 严格只输出 JSON，不要包含任何额外文本或注释
- 所有数值字段必须为数字类型（不得包含单位或中文描述）：
  - duration（分钟，int）
  - scenes[*].duration（秒，int）
  - estimated_scenes（int）
  - estimated_cost（float，单位：USD，不得输出“低/适中/高”等文字）

```json
{{
  "title": "剧本标题",
  "theme": "主题内容", 
  "dynasty": "历史朝代",
  "duration": 3,
  "genre": "剧本类型",
  "characters": [
    {{
      "name": "角色名称",
      "title": "角色称号",
      "description": "角色描述",
      "personality": ["性格特点1", "性格特点2"],
      "appearance": "外貌描述",
      "voice_style": "声音风格",
      "era": "所属时代",
      "social_status": "社会地位"
    }}
  ],
  "scenes": [
    {{
      "scene_id": "scene_001",
      "title": "场景标题",
      "location": "场景地点",
      "time_period": "时间设定",
      "weather": "天气",
      "atmosphere": "氛围描述",
      "background_description": "背景描述",
      "lighting": "光照条件",
      "camera_angle": "镜头角度",
      "duration": 20
    }}
  ],
  "plot_summary": "整体剧情概要",
  "key_conflicts": ["主要冲突列表"],
  "emotional_arc": "情感发展弧线描述",
  "visual_style": {{
    "color_tone": "色调风格",
    "composition": "构图风格",
    "costume_style": "服装风格",
    "scene_style": "场景风格"
  }},
  "audio_style": {{
    "background_music": "背景音乐风格",
    "sound_effects": "音效风格",
    "voice_style": "配音风格"
  }},
  "estimated_scenes": 5,
  "estimated_cost": 0.12,
  "production_notes": ["制作备注列表"]
}}
```

## 创作要点
1. 确保剧情紧凑，适合{duration}分钟的短剧格式
2. 角色设定要符合{dynasty}朝代的历史背景
3. 场景安排要有明确的戏剧冲突和情感起伏
4. 每个场景时长不超过30秒，总时长控制在{duration}分钟内
5. 视觉和音频风格要与历史题材相匹配
6. 考虑制作成本和技术可行性

请开始创作：
"""
    
    async def _llm_call(self, prompt) -> str:
        """调用语言模型"""
        import time
        start_time = time.time()
        
        try:
            logger.info(f"🤖 开始调用LLM，模型: {self.llm['model']}, 温度: {self.llm['temperature']}, 最大tokens: {self.llm['max_tokens']}")
            prompt_text = str(prompt) if hasattr(prompt, 'text') else str(prompt)
            logger.info(f"📏 提示词长度: {len(prompt_text)} 字符")
            
            # 使用LLMCallerAdapter调用模型
            result = await self.llm_adapter.call_llm(
                prompt=prompt,
                model=self.llm['model'],
                temperature=self.llm['temperature'],
                max_tokens=self.llm['max_tokens']
            )
            
            call_time = time.time() - start_time
            logger.info(f"✅ LLM调用完成，耗时: {call_time:.2f}秒")
            logger.info(f"📄 响应长度: {len(result)} 字符")
            
            return result
            
        except Exception as e:
            call_time = time.time() - start_time
            logger.error(f"❌ LLM调用失败，耗时: {call_time:.2f}秒，错误: {str(e)}")
            return f'{{"error": "LLM调用失败: {str(e)}", "title": "错误", "theme": "", "dynasty": "", "duration": 0, "genre": "", "characters": [], "scenes": [], "plot_summary": "生成失败: {str(e)}", "key_conflicts": [], "emotional_arc": "", "visual_style": {{}}, "audio_style": {{}}, "estimated_scenes": 0, "estimated_cost": 0.0, "production_notes": []}}'
    
    async def generate_outline(self, request: OutlineRequest) -> OutlineResponse:
        """生成剧本大纲"""
        import time
        start_time = time.time()
        
        try:
            # 成本检查
            cost_check_start = time.time()
            estimated_cost = self._estimate_cost(request)
            if not await self.cost_controller.check_budget(estimated_cost):
                raise ValueError(f"预算不足，需要 ${estimated_cost:.2f}")
            cost_check_time = time.time() - cost_check_start
            logger.info(f"📊 成本检查耗时: {cost_check_time:.2f}秒")
            
            # 准备输入数据
            data_prep_start = time.time()
            input_data = {
                "title": request.title,
                "theme": request.theme,
                "dynasty": request.dynasty,
                "duration": request.duration,
                "genre": request.genre,
                "target_audience": request.target_audience,
                "tone": request.tone,
                "key_elements": "\n".join(f"- {elem}" for elem in request.key_elements),
                "constraints": "\n".join(f"- {constraint}" for constraint in request.constraints)
            }
            data_prep_time = time.time() - data_prep_start
            logger.info(f"📝 数据准备耗时: {data_prep_time:.2f}秒")
            
            # 执行链路
            chain_start = time.time()
            logger.info(f"🚀 开始执行大纲生成链路，使用模型: {self.llm['model']}")
            result = await self.chain.ainvoke(input_data)
            chain_time = time.time() - chain_start
            logger.info(f"⚡ 链路执行耗时: {chain_time:.2f}秒")
            
            # 记录成本
            cost_record_start = time.time()
            await self.cost_controller.record_cost(estimated_cost, "outline_generation")
            cost_record_time = time.time() - cost_record_start
            logger.info(f"💰 成本记录耗时: {cost_record_time:.2f}秒")
            
            total_time = time.time() - start_time
            logger.info(f"✅ 大纲生成总耗时: {total_time:.2f}秒 (成本检查: {cost_check_time:.2f}s, 数据准备: {data_prep_time:.2f}s, 链路执行: {chain_time:.2f}s, 成本记录: {cost_record_time:.2f}s)")
            
            return result
            
        except Exception as e:
            # 返回错误响应
            return OutlineResponse(
                title=request.title,
                theme=request.theme,
                dynasty=request.dynasty,
                duration=request.duration,
                genre=request.genre,
                characters=[],
                scenes=[],
                plot_summary=f"生成失败: {str(e)}",
                key_conflicts=[],
                emotional_arc="",
                visual_style={},
                audio_style={},
                estimated_scenes=0,
                estimated_cost=0.0,
                production_notes=[f"错误: {str(e)}"]
            )
    
    def _estimate_cost(self, request: OutlineRequest) -> float:
        """估算生成成本"""
        # 基础成本计算
        base_cost = 0.05  # 基础成本
        duration_factor = request.duration / 60  # 时长因子
        complexity_factor = len(request.key_elements) * 0.01  # 复杂度因子
        
        return base_cost + (duration_factor * 0.02) + complexity_factor
    
    async def validate_outline(self, outline: OutlineResponse) -> Dict[str, Any]:
        """验证大纲质量"""
        validation_result = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "suggestions": []
        }
        
        # 检查基本完整性
        if not outline.characters:
            validation_result["errors"].append("缺少角色设定")
            validation_result["is_valid"] = False
        
        if not outline.scenes:
            validation_result["errors"].append("缺少场景设定")
            validation_result["is_valid"] = False
        
        # 检查时长合理性
        total_scene_duration = sum(scene.duration for scene in outline.scenes)
        expected_duration = outline.duration * 60  # 转换为秒
        
        if abs(total_scene_duration - expected_duration) > expected_duration * 0.2:
            validation_result["warnings"].append(
                f"场景总时长({total_scene_duration}s)与预期时长({expected_duration}s)差异较大"
            )
        
        # 检查角色数量
        if len(outline.characters) > 8:
            validation_result["warnings"].append("角色数量较多，可能增加制作复杂度")
        
        # 检查场景数量
        if len(outline.scenes) > outline.duration * 2:
            validation_result["warnings"].append("场景切换频繁，可能影响观看体验")
        
        return validation_result
    
    def export_to_template(self, outline: OutlineResponse) -> str:
        """导出为模板格式"""
        try:
            template = self.template_env.get_template('script_template.j2')
            return template.render(
                title=outline.title,
                genre=outline.genre,
                duration=outline.duration,
                dynasty=outline.dynasty,
                theme=outline.theme,
                characters=outline.characters,
                scenes=outline.scenes,
                visual_style=outline.visual_style,
                audio_style=outline.audio_style
            )
        except Exception as e:
            return f"模板渲染失败: {str(e)}"