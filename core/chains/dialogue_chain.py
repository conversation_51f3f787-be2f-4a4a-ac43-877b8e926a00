"""对白生成链路

负责为场景生成符合历史背景和角色特征的对白内容，包括：
- 角色对白的语言风格和用词习惯
- 情感表达和语调控制
- 历史朝代的语言特色
- 配音指导和时间控制
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

from langchain.chains.base import Chain
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.schema.runnable import RunnablePassthrough
from pydantic import BaseModel, Field
import jinja2

from ..models import (
    MediaType, TaskStatus, QualityLevel,
    CharacterData, SceneData, ProjectData
)
from ..config import ConfigManager
from ..cost_control import CostController


class DialogueRequest(BaseModel):
    """对白生成请求"""
    scene_data: SceneData = Field(description="场景数据")
    characters: List[CharacterData] = Field(description="参与角色")
    plot_context: str = Field(description="剧情上下文")
    emotional_context: str = Field(description="情感上下文")
    dynasty: str = Field(description="历史朝代")
    duration: float = Field(description="对白总时长（秒）")
    style_requirements: Dict[str, str] = Field(default_factory=dict, description="风格要求")
    constraints: List[str] = Field(default_factory=list, description="创作约束")


class DialogueLineData(BaseModel):
    """单句对白数据"""
    line_id: str = Field(description="对白ID")
    character: str = Field(description="角色名称")
    text: str = Field(description="对白内容")
    emotion: str = Field(description="情感状态")
    tone: str = Field(description="语调")
    volume: str = Field(description="音量")
    pace: str = Field(description="语速")
    emphasis: List[str] = Field(description="重音词汇")
    pause_before: float = Field(default=0.0, description="前置停顿（秒）")
    pause_after: float = Field(default=0.0, description="后置停顿（秒）")
    timing: float = Field(description="开始时间（秒）")
    duration: float = Field(description="持续时间（秒）")
    subtitle_text: str = Field(description="字幕文本")
    pronunciation_guide: str = Field(default="", description="发音指导")


class VoiceDirection(BaseModel):
    """配音指导"""
    character: str = Field(description="角色名称")
    voice_characteristics: Dict[str, str] = Field(description="声音特征")
    emotional_range: Dict[str, str] = Field(description="情感范围")
    speaking_style: Dict[str, str] = Field(description="说话风格")
    special_instructions: List[str] = Field(description="特殊指导")


class DialogueResponse(BaseModel):
    """对白生成响应"""
    scene_id: str
    total_duration: float
    
    # 对白内容
    dialogue_lines: List[DialogueLineData] = Field(description="对白列表")
    
    # 配音指导
    voice_directions: List[VoiceDirection] = Field(description="配音指导")
    
    # 语言特色
    language_style: Dict[str, str] = Field(description="语言风格")
    historical_accuracy: Dict[str, str] = Field(description="历史准确性")
    
    # 情感设计
    emotional_arc: List[Dict[str, Any]] = Field(description="情感弧线")
    conflict_points: List[Dict[str, str]] = Field(description="冲突点")
    
    # 技术要求
    audio_cues: List[Dict[str, Any]] = Field(description="音频提示")
    subtitle_formatting: Dict[str, str] = Field(description="字幕格式")
    
    # 质量控制
    readability_score: float = Field(description="可读性评分")
    authenticity_score: float = Field(description="真实性评分")
    engagement_score: float = Field(description="吸引力评分")
    
    # 制作指导
    production_notes: List[str] = Field(description="制作备注")
    quality_checkpoints: List[str] = Field(description="质量检查点")


class DialogueOutputParser(BaseOutputParser[DialogueResponse]):
    """对白输出解析器"""
    
    def parse(self, text: str) -> DialogueResponse:
        """解析LLM输出为结构化对白数据"""
        try:
            import json
            logger.info(f"🔍 解析对白响应，长度: {len(text)} 字符")
            
            # 预处理文本，移除markdown代码块标记
            processed_text = text.strip()
            if processed_text.startswith('```json'):
                # 找到结束标记
                end_marker = processed_text.find('```', 6)
                if end_marker != -1:
                    processed_text = processed_text[7:end_marker].strip()
                else:
                    # 如果没有结束标记，尝试移除开始标记
                    processed_text = processed_text[7:].strip()
            elif processed_text.startswith('```'):
                # 找到结束标记
                end_marker = processed_text.find('```', 3)
                if end_marker != -1:
                    processed_text = processed_text[4:end_marker].strip()
                else:
                    # 如果没有结束标记，尝试移除开始标记
                    processed_text = processed_text[4:].strip()
            
            # 检查是否是错误响应
            if processed_text.strip().startswith('{') and '"error"' in processed_text:
                logger.warning("⚠️  检测到错误响应")
                data = json.loads(processed_text)
                if data.get('scene_id') == 'error':
                    logger.error(f"❌ LLM返回错误: {data.get('error_msg', '未知错误')}")
                    return DialogueResponse(
                        scene_id="error",
                        total_duration=0.0,
                        dialogue_lines=[],
                        voice_directions=[],
                        language_style={},
                        historical_accuracy={},
                        emotional_arc=[],
                        conflict_points=[],
                        audio_cues=[],
                        subtitle_formatting={},
                        readability_score=0.0,
                        authenticity_score=0.0,
                        engagement_score=0.0,
                        production_notes=[f"LLM错误: {data.get('error_msg', '未知错误')}"],
                        quality_checkpoints=[]
                    )
            
            # 解析JSON
            data = json.loads(processed_text)
            
            # 数据类型转换处理
            # 确保数值类型正确
            if 'total_duration' in data and not isinstance(data['total_duration'], (int, float)):
                try:
                    data['total_duration'] = float(data['total_duration'])
                except (ValueError, TypeError):
                    data['total_duration'] = 0.0
            
            # 处理emotional_arc中的timestamp字段
            if 'emotional_arc' in data and isinstance(data['emotional_arc'], list):
                for arc in data['emotional_arc']:
                    # 确保timestamp字段是字符串类型
                    if 'timestamp' in arc and not isinstance(arc['timestamp'], str):
                        try:
                            arc['timestamp'] = str(arc['timestamp'])
                        except (ValueError, TypeError):
                            arc['timestamp'] = ""
            
            # 处理conflict_points中的timestamp字段
            if 'conflict_points' in data and isinstance(data['conflict_points'], list):
                for point in data['conflict_points']:
                    # 确保timestamp字段是字符串类型
                    if 'timestamp' in point and not isinstance(point['timestamp'], str):
                        try:
                            point['timestamp'] = str(point['timestamp'])
                        except (ValueError, TypeError):
                            point['timestamp'] = ""
            
            # 处理audio_cues中的timestamp字段
            if 'audio_cues' in data and isinstance(data['audio_cues'], list):
                for cue in data['audio_cues']:
                    # 确保timestamp字段是字符串类型
                    if 'timestamp' in cue and not isinstance(cue['timestamp'], str):
                        try:
                            cue['timestamp'] = str(cue['timestamp'])
                        except (ValueError, TypeError):
                            cue['timestamp'] = ""
            
            # 处理dialogue_lines中的数值字段
            if 'dialogue_lines' in data and isinstance(data['dialogue_lines'], list):
                for line in data['dialogue_lines']:
                    # 确保数值字段正确
                    for field in ['pause_before', 'pause_after', 'timing', 'duration']:
                        if field in line and not isinstance(line[field], (int, float)):
                            try:
                                line[field] = float(line[field])
                            except (ValueError, TypeError):
                                line[field] = 0.0 if field in ['pause_before', 'pause_after'] else 0.0
            
            # 处理评分字段
            for score_field in ['readability_score', 'authenticity_score', 'engagement_score']:
                if score_field in data and not isinstance(data[score_field], (int, float)):
                    try:
                        data[score_field] = float(data[score_field])
                    except (ValueError, TypeError):
                        data[score_field] = 0.0
            
            return DialogueResponse(**data)
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {str(e)}")
            logger.error(f"   原始文本: {text[:200]}...")
            # 返回默认结构
            return DialogueResponse(
                scene_id="error",
                total_duration=0.0,
                dialogue_lines=[],
                voice_directions=[],
                language_style={},
                historical_accuracy={},
                emotional_arc=[],
                conflict_points=[],
                audio_cues=[],
                subtitle_formatting={},
                readability_score=0.0,
                authenticity_score=0.0,
                engagement_score=0.0,
                production_notes=[f"JSON解析错误: {str(e)}"],
                quality_checkpoints=[]
            )
        except Exception as e:
            logger.error(f"❌ 对白解析失败: {str(e)}")
            # 返回默认结构
            return DialogueResponse(
                scene_id="error",
                total_duration=0.0,
                dialogue_lines=[],
                voice_directions=[],
                language_style={},
                historical_accuracy={},
                emotional_arc=[],
                conflict_points=[],
                audio_cues=[],
                subtitle_formatting={},
                readability_score=0.0,
                authenticity_score=0.0,
                engagement_score=0.0,
                production_notes=[f"解析错误: {str(e)}"],
                quality_checkpoints=[]
            )
    
    @property
    def _type(self) -> str:
        return "dialogue_output_parser"


class DialogueChain:
    """对白生成链路"""
    
    def __init__(self, config_manager: ConfigManager, cost_controller: CostController):
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.llm = self._initialize_llm()
        self.template_env = self._initialize_templates()
        self.output_parser = DialogueOutputParser()
        
        # 历史朝代语言特色库
        self.dynasty_language_styles = self._load_dynasty_styles()
        
        # 构建链路
        self.chain = self._build_chain()
    
    def _initialize_llm(self):
        """初始化语言模型"""
        from ..llm_caller_adapter import LLMCallerAdapter
        self.llm_adapter = LLMCallerAdapter(self.config_manager)
        
        text_config = self.config_manager.get_service_config('text')
        return {
            'model': text_config.get('model', 'gpt-4'),
            'temperature': text_config.get('temperature', 0.8),  # 对白需要更多创造性
            'max_tokens': text_config.get('max_tokens', 4000)
        }
    
    def _initialize_templates(self):
        """初始化模板环境"""
        template_dir = Path(__file__).parent.parent.parent / 'templates'
        return jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_dir),
            autoescape=True
        )
    
    def _load_dynasty_styles(self) -> Dict[str, Dict[str, Any]]:
        """加载历史朝代语言风格"""
        return {
            "唐朝": {
                "formal_address": ["陛下", "殿下", "大人", "公子", "小姐"],
                "common_phrases": ["敢问", "不敢", "恭敬不如从命", "有劳"],
                "sentence_patterns": ["之", "者", "也", "矣", "乎"],
                "vocabulary_style": "文雅古典",
                "tone_characteristics": "庄重典雅"
            },
            "宋朝": {
                "formal_address": ["官人", "娘子", "相公", "小娘子"],
                "common_phrases": ["却是", "便是", "只是", "原来"],
                "sentence_patterns": ["的", "了", "着", "过"],
                "vocabulary_style": "文雅通俗",
                "tone_characteristics": "温文尔雅"
            },
            "明朝": {
                "formal_address": ["大人", "老爷", "夫人", "公子", "小姐"],
                "common_phrases": ["这便", "那便", "如何", "怎的"],
                "sentence_patterns": ["的", "了", "着", "呢"],
                "vocabulary_style": "通俗易懂",
                "tone_characteristics": "朴实自然"
            },
            "清朝": {
                "formal_address": ["大人", "老爷", "太太", "少爷", "小姐"],
                "common_phrases": ["这个", "那个", "怎么", "什么"],
                "sentence_patterns": ["的", "了", "着", "呢", "吧"],
                "vocabulary_style": "接近现代",
                "tone_characteristics": "自然流畅"
            }
        }
    
    def _build_chain(self):
        """构建处理链路"""
        prompt_template = PromptTemplate(
            input_variables=[
                "scene_data", "characters", "plot_context", "emotional_context",
                "dynasty", "duration", "style_requirements", "constraints",
                "dynasty_style"
            ],
            template=self._get_dialogue_prompt_template()
        )
        
        chain = (
            RunnablePassthrough()
            | prompt_template
            | self._llm_call
            | self.output_parser
        )
        
        return chain
    
    def _get_dialogue_prompt_template(self) -> str:
        """获取对白生成提示模板"""
        return """
你是一位专业的历史剧编剧和对白设计师，擅长创作符合历史背景的精彩对白。

请根据以下信息为场景创作对白内容：

## 场景信息
{scene_data}

## 角色信息
{characters}

## 剧情上下文
{plot_context}

## 情感上下文
{emotional_context}

## 历史背景
朝代：{dynasty}
语言风格：{dynasty_style}

## 时长要求
总时长：{duration}秒

## 风格要求
{style_requirements}

## 创作约束
{constraints}

## 输出要求
请以JSON格式输出完整的对白设计，包含以下结构：

```json
{{
  "scene_id": "场景ID",
  "total_duration": 总时长秒数,
  
  "dialogue_lines": [
    {{
      "line_id": "对白唯一标识",
      "character": "角色名称",
      "text": "对白内容",
      "emotion": "情感状态（喜悦/愤怒/悲伤/恐惧/惊讶/平静）",
      "tone": "语调（温和/严厉/激动/低沉/高亢）",
      "volume": "音量（轻声/正常/大声/怒吼/耳语）",
      "pace": "语速（缓慢/正常/快速/急促）",
      "emphasis": ["需要重音的词汇"],
      "pause_before": 前置停顿秒数,
      "pause_after": 后置停顿秒数,
      "timing": 开始时间秒数,
      "duration": 持续时间秒数,
      "subtitle_text": "字幕显示文本",
      "pronunciation_guide": "发音指导（如有特殊读音）"
    }}
  ],
  
  "voice_directions": [
    {{
      "character": "角色名称",
      "voice_characteristics": {{
        "pitch": "音高（高/中/低）",
        "timbre": "音色描述",
        "accent": "口音特色",
        "resonance": "共鸣特点"
      }},
      "emotional_range": {{
        "joy": "喜悦时的声音特点",
        "anger": "愤怒时的声音特点",
        "sadness": "悲伤时的声音特点",
        "fear": "恐惧时的声音特点"
      }},
      "speaking_style": {{
        "rhythm": "说话节奏",
        "articulation": "咬字特点",
        "breathing": "换气习惯"
      }},
      "special_instructions": ["特殊配音指导"]
    }}
  ],
  
  "language_style": {{
    "formality_level": "正式程度",
    "vocabulary_complexity": "词汇复杂度",
    "sentence_structure": "句式特点",
    "cultural_elements": "文化元素"
  }},
  
  "historical_accuracy": {{
    "period_appropriate": "时代适宜性评估",
    "cultural_sensitivity": "文化敏感性检查",
    "linguistic_authenticity": "语言真实性"
  }},
  
  "emotional_arc": [
    {{
      "timestamp": 时间点,
      "emotion": "情感状态",
      "intensity": "强度（1-10）",
      "transition": "情感转换描述"
    }}
  ],
  
  "conflict_points": [
    {{
      "timestamp": "冲突发生时间",
      "type": "冲突类型",
      "description": "冲突描述",
      "resolution": "解决方式"
    }}
  ],
  
  "audio_cues": [
    {{
      "timestamp": 时间点,
      "type": "音频类型（对白/音效/音乐）",
      "description": "音频描述",
      "volume_level": "音量级别"
    }}
  ],
  
  "subtitle_formatting": {{
    "font_style": "字体风格",
    "color_scheme": "颜色方案",
    "positioning": "位置设定",
    "timing_rules": "时间规则"
  }},
  
  "readability_score": 可读性评分（0-10）,
  "authenticity_score": 真实性评分（0-10）,
  "engagement_score": 吸引力评分（0-10）,
  
  "production_notes": [
    "制作备注和注意事项"
  ],
  
  "quality_checkpoints": [
    "质量检查要点"
  ]
}}
```

## 创作要点
1. 对白要符合{dynasty}朝代的语言特色和社会背景
2. 每个角色的说话方式要体现其身份、性格和教育背景
3. 情感表达要自然真实，避免过于戏剧化
4. 考虑配音的可行性，避免过于复杂的发音要求
5. 字幕要简洁明了，便于观众理解
6. 总时长要控制在{duration}秒以内
7. 对白要推进剧情发展，增强戏剧张力

请开始创作对白：
"""
    
    async def _llm_call(self, prompt: str) -> str:
        """调用语言模型"""
        try:
            # 使用LLMCallerAdapter调用模型
            return await self.llm_adapter.call_llm(
                prompt=prompt,
                model=self.llm['model'],
                temperature=self.llm['temperature'],
                max_tokens=self.llm['max_tokens']
            )
            
        except Exception as e:
            logger.error(f"❌ LLM调用失败: {str(e)}")
            return f'{{"error": "LLM调用失败: {str(e)}", "scene_id": "error", "total_duration": 0.0, "dialogue_lines": [], "voice_directions": [], "language_style": {{}}, "historical_accuracy": {{}}, "emotional_arc": [], "conflict_points": [], "audio_cues": [], "subtitle_formatting": {{}}, "readability_score": 0.0, "authenticity_score": 0.0, "engagement_score": 0.0, "production_notes": [], "quality_checkpoints": []}}'
    
    async def generate_dialogue(self, request: DialogueRequest) -> DialogueResponse:
        """生成对白内容"""
        try:
            # 成本检查
            estimated_cost = self._estimate_cost(request)
            if not await self.cost_controller.check_budget(estimated_cost):
                logger.error(f"❌ 预算不足，需要 ${estimated_cost:.2f}")
                raise ValueError(f"预算不足，需要 ${estimated_cost:.2f}")
            
            logger.info(f"📝 开始生成对白，场景ID: {request.scene_data.scene_id}")
            logger.info(f"   朝代: {request.dynasty}")
            logger.info(f"   时长: {request.duration}秒")
            logger.info(f"   角色数: {len(request.characters)}")
            
            # 获取朝代语言风格
            dynasty_style = self.dynasty_language_styles.get(
                request.dynasty, 
                self.dynasty_language_styles["明朝"]  # 默认使用明朝风格
            )
            
            # 准备输入数据
            input_data = {
                "scene_data": request.scene_data.dict(),
                "characters": [char.dict() for char in request.characters],
                "plot_context": request.plot_context,
                "emotional_context": request.emotional_context,
                "dynasty": request.dynasty,
                "duration": request.duration,
                "style_requirements": request.style_requirements,
                "constraints": "\n".join(f"- {constraint}" for constraint in request.constraints),
                "dynasty_style": dynasty_style
            }
            
            # 执行链路
            result = await self.chain.ainvoke(input_data)
            
            # 后处理优化
            result = await self._post_process_dialogue(result, request)
            
            # 记录成本
            await self.cost_controller.record_cost(estimated_cost, "dialogue_generation")
            
            logger.info(f"✅ 对白生成完成，场景ID: {request.scene_data.scene_id}")
            logger.info(f"   对白行数: {len(result.dialogue_lines)}")
            logger.info(f"   可读性评分: {result.readability_score}")
            logger.info(f"   真实性评分: {result.authenticity_score}")
            logger.info(f"   吸引力评分: {result.engagement_score}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 对白生成失败: {str(e)}")
            # 返回错误响应
            return DialogueResponse(
                scene_id=f"error_{datetime.now().timestamp()}",
                total_duration=0.0,
                dialogue_lines=[],
                voice_directions=[],
                language_style={},
                historical_accuracy={},
                emotional_arc=[],
                conflict_points=[],
                audio_cues=[],
                subtitle_formatting={},
                readability_score=0.0,
                authenticity_score=0.0,
                engagement_score=0.0,
                production_notes=[f"错误: {str(e)}"],
                quality_checkpoints=[]
            )
    
    async def _post_process_dialogue(self, dialogue: DialogueResponse, request: DialogueRequest) -> DialogueResponse:
        """后处理对白内容"""
        # 时长校准
        total_duration = sum(line.duration for line in dialogue.dialogue_lines)
        if abs(total_duration - request.duration) > request.duration * 0.1:
            # 调整对白时长
            scale_factor = request.duration / total_duration if total_duration > 0 else 1
            for line in dialogue.dialogue_lines:
                line.duration *= scale_factor
                line.timing *= scale_factor
        
        # 使用AI评价系统进行质量评估
        try:
            from ..historical_story_evaluator import HistoricalStoryEvaluator
            evaluator = HistoricalStoryEvaluator(model_name=self.llm['model'])
            
            # 准备评价数据
            dialogue_text = "\n".join([f"{line.character}: {line.text}" for line in dialogue.dialogue_lines])
            evaluation_context = {
                "dynasty": request.dynasty,
                "context": request.plot_context
            }
            evaluation_result = await evaluator.evaluate(
                content=dialogue_text,
                context=evaluation_context
            )
            
            # 更新评分
            dialogue.readability_score = evaluation_result.get("readability_score", 0.0)
            dialogue.authenticity_score = evaluation_result.get("authenticity_score", 0.0)
            dialogue.engagement_score = evaluation_result.get("engagement_score", 0.0)
            
            # 添加AI评价备注
            dialogue.production_notes.extend(evaluation_result.get("suggestions", []))
            
        except Exception as e:
            logger.warning(f"⚠️ AI评价系统调用失败，使用传统评分方法: {str(e)}")
            # 回退到传统评分方法
            dialogue.readability_score = self._calculate_readability_score(dialogue)
            dialogue.authenticity_score = self._calculate_authenticity_score(dialogue, request.dynasty)
            dialogue.engagement_score = self._calculate_engagement_score(dialogue)
        
        return dialogue
    
    def _calculate_readability_score(self, dialogue: DialogueResponse) -> float:
        """计算可读性评分"""
        if not dialogue.dialogue_lines:
            return 0.0
        
        total_score = 0.0
        for line in dialogue.dialogue_lines:
            # 基于文本长度和复杂度评分
            text_length = len(line.text)
            if 5 <= text_length <= 50:  # 理想长度
                score = 10.0
            elif text_length < 5:
                score = 5.0  # 太短
            else:
                score = max(3.0, 10.0 - (text_length - 50) * 0.1)  # 太长
            
            total_score += score
        
        return min(10.0, total_score / len(dialogue.dialogue_lines))
    
    def _calculate_authenticity_score(self, dialogue: DialogueResponse, dynasty: str) -> float:
        """计算历史真实性评分"""
        if not dialogue.dialogue_lines:
            return 0.0
        
        dynasty_style = self.dynasty_language_styles.get(dynasty, {})
        if not dynasty_style:
            return 5.0  # 中等评分
        
        # 检查是否使用了合适的敬语和用词
        formal_addresses = dynasty_style.get('formal_address', [])
        common_phrases = dynasty_style.get('common_phrases', [])
        
        score = 7.0  # 基础分
        
        for line in dialogue.dialogue_lines:
            text = line.text
            # 检查敬语使用
            if any(addr in text for addr in formal_addresses):
                score += 0.5
            # 检查常用短语
            if any(phrase in text for phrase in common_phrases):
                score += 0.3
        
        return min(10.0, score)
    
    def _calculate_engagement_score(self, dialogue: DialogueResponse) -> float:
        """计算吸引力评分"""
        if not dialogue.dialogue_lines:
            return 0.0
        
        score = 5.0  # 基础分
        
        # 检查情感变化
        emotions = [line.emotion for line in dialogue.dialogue_lines]
        unique_emotions = len(set(emotions))
        score += min(3.0, unique_emotions * 0.5)
        
        # 检查冲突点
        if dialogue.conflict_points:
            score += min(2.0, len(dialogue.conflict_points) * 0.5)
        
        return min(10.0, score)
    
    def _estimate_cost(self, request: DialogueRequest) -> float:
        """估算生成成本"""
        base_cost = 0.06  # 基础成本
        duration_factor = request.duration / 60  # 时长因子
        character_factor = len(request.characters) * 0.015  # 角色数量因子
        
        return base_cost + (duration_factor * 0.04) + character_factor
    
    async def optimize_for_voice_acting(self, dialogue: DialogueResponse) -> DialogueResponse:
        """为配音优化对白"""
        """优化对白以便于配音演员表演"""
        
        for line in dialogue.dialogue_lines:
            # 添加换气提示
            if len(line.text) > 30:
                # 长句子添加换气标记
                line.pronunciation_guide += " [换气]"
            
            # 调整语速
            if line.pace == "急促" and len(line.text) > 20:
                line.pace = "快速"  # 避免过于急促导致不清晰
                line.production_notes = getattr(line, 'production_notes', []) + ["注意保持清晰度"]
        
        return dialogue
    
    def export_subtitle_file(self, dialogue: DialogueResponse, format: str = "srt") -> str:
        """导出字幕文件"""
        if format.lower() == "srt":
            return self._export_srt(dialogue)
        elif format.lower() == "vtt":
            return self._export_vtt(dialogue)
        else:
            return "不支持的字幕格式"
    
    def _export_srt(self, dialogue: DialogueResponse) -> str:
        """导出SRT格式字幕"""
        srt_content = []
        
        for i, line in enumerate(dialogue.dialogue_lines, 1):
            start_time = self._seconds_to_srt_time(line.timing)
            end_time = self._seconds_to_srt_time(line.timing + line.duration)
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(f"{line.character}: {line.subtitle_text}")
            srt_content.append("")  # 空行
        
        return "\n".join(srt_content)
    
    def _export_vtt(self, dialogue: DialogueResponse) -> str:
        """导出VTT格式字幕"""
        vtt_content = ["WEBVTT", ""]
        
        for line in dialogue.dialogue_lines:
            start_time = self._seconds_to_vtt_time(line.timing)
            end_time = self._seconds_to_vtt_time(line.timing + line.duration)
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(f"{line.character}: {line.subtitle_text}")
            vtt_content.append("")  # 空行
        
        return "\n".join(vtt_content)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """将秒数转换为VTT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    async def batch_generate_dialogues(self, requests: List[DialogueRequest]) -> List[DialogueResponse]:
        """批量生成对白"""
        logger.info(f"📝 开始批量生成对白，请求数: {len(requests)}")
        results = []
        
        for i, request in enumerate(requests):
            try:
                logger.info(f"   处理第 {i+1}/{len(requests)} 个请求，场景ID: {request.scene_data.scene_id}")
                result = await self.generate_dialogue(request)
                results.append(result)
                
                # 添加延迟以避免API限制
                await asyncio.sleep(0.3)
                
            except Exception as e:
                logger.error(f"❌ 批量生成第 {i+1} 个对白失败: {str(e)}")
                # 添加错误对白
                error_dialogue = DialogueResponse(
                    scene_id=f"batch_error_{len(results)}",
                    total_duration=0.0,
                    dialogue_lines=[],
                    voice_directions=[],
                    language_style={},
                    historical_accuracy={},
                    emotional_arc=[],
                    conflict_points=[],
                    audio_cues=[],
                    subtitle_formatting={},
                    readability_score=0.0,
                    authenticity_score=0.0,
                    engagement_score=0.0,
                    production_notes=[f"批量生成错误: {str(e)}"],
                    quality_checkpoints=[]
                )
                results.append(error_dialogue)
        
        logger.info(f"✅ 批量生成对白完成，成功: {len(results)}")
        return results