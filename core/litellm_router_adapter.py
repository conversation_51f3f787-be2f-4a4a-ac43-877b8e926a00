"""
LiteLLMRouter适配器
将LiteLLMRouter适配到统一的LLMProviderProtocol接口，使用组合模式
"""

import logging
from typing import Dict, Any, List, Optional

from .llm_interface import LLMProviderProtocol, LLMRequest, LLMResponse
from .litellm_router import Lite<PERSON>MRouter
from .config import ConfigManager

logger = logging.getLogger(__name__)


class LiteLLMRouterAdapter:
    """LiteLLMRouter适配器，使用组合模式实现LLMProviderProtocol"""
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化适配器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.router = LiteLLMRouter(config_manager)
        self.logger = logging.getLogger(__name__)
    
    async def call_llm(
        self, 
        prompt: str, 
        system_message: str = None, 
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        **kwargs
    ) -> str:
        """
        调用LiteLLMRouter的route_request方法
        
        Args:
            prompt: 提示词
            system_message: 系统消息
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        self.logger.info(f"Routing LLM request with model: {model or 'auto'}")
        
        # 构建消息列表
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": prompt})
        
        # 构建路由请求
        routing_request = {
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            **kwargs
        }
        
        # 如果指定了模型，添加到请求中
        if model:
            routing_request["model"] = model
        
        # 调用路由器
        response = await self.router.route_request(routing_request)
        
        return response.get("content", "")
    
    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表
        
        Returns:
            可用模型名称列表
        """
        try:
            # 获取路由器中的所有模型组
            model_groups = self.router.get_model_groups()
            
            # 收集所有模型
            models = []
            for group_name, group_info in model_groups.items():
                primary_model = group_info.get("primary_model")
                fallback_models = group_info.get("fallback_models", [])
                
                if primary_model:
                    models.append(primary_model)
                models.extend(fallback_models)
            
            return list(set(models))  # 去重
            
        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return []
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取提供者信息
        
        Returns:
            提供者信息字典
        """
        return {
            "name": "LiteLLMRouter",
            "description": "基于litellm的多模型路由器",
            "version": "1.0.0",
            "type": "router",
            "supported_models": self.get_available_models(),
            "model_groups": self.router.get_model_groups(),
            "routing_strategies": [strategy.value for strategy in self.router.RoutingStrategy],
            "config": {
                "has_config_manager": self.config_manager is not None
            }
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取路由器统计信息
        
        Returns:
            统计信息字典
        """
        return self.router.get_statistics()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.router.reset_statistics()
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态字典
        """
        return self.router.health_check()
    
    def get_model_group_info(self, group_name: str) -> Dict[str, Any]:
        """
        获取模型组信息
        
        Args:
            group_name: 模型组名称
            
        Returns:
            模型组信息字典
        """
        return self.router.get_model_group_info(group_name)