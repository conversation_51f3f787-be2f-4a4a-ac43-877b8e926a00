"""工作流引擎模块

负责协调整个视频制作流程，管理任务执行顺序和依赖关系。
"""

import asyncio
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass, field

from .models import (
    ScriptData, TaskStatus, CharacterData, SceneData, DialogueLine
)
from .config import ConfigManager
from .cost_control import CostController

# 导入适配器
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from adapters.video.kling_adapter import KlingVideoAdapter
from adapters.voice.edge_tts_adapter import EdgeTTSAdapter
from adapters.text.claude_adapter import ClaudeAdapter
from adapters.text.glm_adapter import LangChainGLMAdapter
from adapters.base import AdapterConfig


class WorkflowStage(str, Enum):
    """工作流阶段枚举"""
    SCRIPT_GENERATION = "script_generation"
    SCENE_PLANNING = "scene_planning"
    CHARACTER_DESIGN = "character_design"
    MEDIA_GENERATION = "media_generation"
    AUDIO_SYNTHESIS = "audio_synthesis"
    VIDEO_COMPOSITION = "video_composition"
    QUALITY_REVIEW = "quality_review"
    FINALIZATION = "finalization"


@dataclass
class WorkflowStep:
    """工作流步骤"""
    step_id: str
    name: str
    stage: WorkflowStage
    handler: Callable
    dependencies: List[str] = field(default_factory=list)
    estimated_duration: float = 0.0  # 分钟
    estimated_cost: float = 0.0  # 美元
    priority: int = 1
    retry_count: int = 3
    timeout: float = 300.0  # 秒


@dataclass
class WorkflowExecution:
    """工作流执行状态"""
    execution_id: str
    script_id: str
    status: TaskStatus
    current_stage: Optional[WorkflowStage]
    completed_steps: List[str] = field(default_factory=list)
    failed_steps: List[str] = field(default_factory=list)
    step_results: Dict[str, Any] = field(default_factory=dict)
    total_cost: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None


class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self, config_manager: ConfigManager, cost_controller: CostController):
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.config = config_manager.get_config()
        # 日志器
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        
        # 初始化适配器
        self._init_adapters()
        
        # 初始化文本生成链路
        self._initialize_text_chains()
        
        # 工作流步骤注册表
        self.workflow_steps: Dict[str, WorkflowStep] = {}
        self.step_handlers: Dict[str, Callable] = {}
        
        # 执行状态跟踪
        self.executions: Dict[str, WorkflowExecution] = {}
        self.active_tasks: Dict[str, asyncio.Task] = {}
        
        # 注册默认工作流步骤
        self._register_default_steps()
    
    def _init_adapters(self):
        """初始化适配器"""
        # 初始化文本生成适配器
        self._initialize_text_adapter()
        
        # 图像生成适配器 - 从配置文件读取模型
        google_api_key = self.config_manager.get_api_key("google")
        if not google_api_key:
            raise ValueError("Google API key not configured. Please set GOOGLE_AI_API_KEY in your environment.")
        
        # 从配置文件读取Google图像模型设置
        image_config = getattr(self.config_manager.app_config, 'image_generation', None)
        if image_config and hasattr(image_config, 'google') and hasattr(image_config.google, 'model'):
            google_model = image_config.google.model
            self.logger.info(f"Using configured Google image model: {google_model}")
        else:
            google_model = "gemini-2.0-flash-exp"  # 默认免费模型
            self.logger.warning(f"No Google model configured, using default: {google_model}")
        
        google_config = AdapterConfig(
            service_name="google_image",
            api_key=google_api_key,
            model=google_model,  # 使用配置文件中的模型
            max_retries=3
        )
        from adapters.image.google_adapter import GoogleImageAdapter
        self.image_adapter = GoogleImageAdapter(google_config, self.config_manager, self.cost_controller)
        
        # 视频生成适配器 - 从配置文件读取模型设置
        video_config = getattr(self.config_manager.app_config, 'video_generation', None)
        if video_config and hasattr(video_config, 'primary_service'):
            video_service = video_config.primary_service
            video_model = getattr(video_config, 'model', "kling-v1")  # 默认模型
            self.logger.info(f"Using configured video service: {video_service}, model: {video_model}")
        else:
            video_service = "kling"
            video_model = "kling-v1"  # 默认模型
            self.logger.warning("No video configuration found, using default: kling-v1")
        
        # 根据服务类型选择适配器
        if video_service == "google":
            google_api_key = self.config_manager.get_api_key("google")
            if not google_api_key:
                raise ValueError("Google API key not configured. Please set GOOGLE_AI_API_KEY in your environment.")
            
            video_adapter_config = AdapterConfig(
                service_name=video_service,
                api_key=google_api_key,
                model=video_model,
                max_retries=3
            )
            from adapters.video.google_adapter import GoogleVideoAdapter
            self.video_adapter = GoogleVideoAdapter(video_adapter_config, self.config_manager, self.cost_controller)
        else:
            # 默认使用Kling
            kling_api_key = self.config_manager.get_api_key("kling")
            if not kling_api_key:
                self.logger.warning("Kling API key not configured, video generation may fail")
                kling_api_key = "dummy_key"  # 允许测试模式
            
            video_adapter_config = AdapterConfig(
                service_name=video_service,
                api_key=kling_api_key,
                model=video_model,
                max_retries=3
            )
            self.video_adapter = KlingVideoAdapter(video_adapter_config, self.config_manager, self.cost_controller)
        
        # 语音合成适配器 - 从配置文件读取模型设置
        voice_config = getattr(self.config_manager.app_config, 'voice_synthesis', None)
        if voice_config and hasattr(voice_config, 'voice_id'):
            voice_model = voice_config.voice_id
            voice_service = voice_config.primary_service
            self.logger.info(f"Using configured voice service: {voice_service}, model: {voice_model}")
        else:
            voice_model = "zh-CN-XiaoxiaoNeural"  # 默认中文女声
            voice_service = "edgetts"
            self.logger.warning("No voice configuration found, using default: zh-CN-XiaoxiaoNeural")
        
        edgetts_config = AdapterConfig(
            service_name=voice_service,
            api_key="free",  # EdgeTTS不需要API密钥
            model=voice_model,  # 使用配置文件中的语音模型
            max_retries=3
        )
        self.voice_adapter = EdgeTTSAdapter(edgetts_config, self.config_manager, self.cost_controller)
    
    def _initialize_text_adapter(self):
        """初始化文本生成适配器"""
        # 从配置文件获取文本生成设置
        text_config = getattr(self.config_manager.app_config, 'text_generation', None)
        
        # 获取配置的主要模型和备用模型
        if text_config:
            primary_model = text_config.primary_model
            fallback_model = getattr(text_config, 'fallback_model', None)
            self.logger.info(f"Using configured primary model: {primary_model}")
        else:
            primary_model = "glm-4-flash"  # 默认模型
            fallback_model = "deepseek-chat"
            self.logger.warning("No text generation config found, using defaults")
        
        # 按配置顺序尝试初始化适配器
        model_configs = [
            (primary_model, self._get_api_key_for_model(primary_model)),
            (fallback_model, self._get_api_key_for_model(fallback_model)) if fallback_model else (None, None)
        ]
        
        # 添加其他可用模型作为备用
        additional_models = [
            ("glm-4-flash", self.config_manager.get_api_key("glm")),
            ("deepseek-chat", self.config_manager.get_api_key("deepseek")),
            ("claude-3-haiku-20240307", self.config_manager.get_api_key("anthropic"))
        ]
        
        # 合并模型配置，去重
        all_models = model_configs + [(m, k) for m, k in additional_models if m not in [primary_model, fallback_model]]
        
        for model_name, api_key in all_models:
            if not model_name or not api_key:
                continue
                
            try:
                if "glm" in model_name.lower():
                    glm_config = AdapterConfig(
                        service_name="glm",
                        api_key=api_key,
                        model=model_name,
                        base_url="https://open.bigmodel.cn/api/paas/v4/",
                        max_retries=3
                    )
                    self.text_adapter = LangChainGLMAdapter(glm_config, self.config_manager, self.cost_controller)
                    self.logger.info(f"Successfully initialized LangChain GLM adapter with model: {model_name}")
                    return
                    
                elif "deepseek" in model_name.lower():
                    deepseek_config = AdapterConfig(
                        service_name="deepseek",
                        api_key=api_key,
                        model=model_name,
                        base_url="https://api.deepseek.com/chat/completions",
                        max_retries=3
                    )
                    self.text_adapter = ClaudeAdapter(deepseek_config, self.config_manager, self.cost_controller)
                    self.logger.info(f"Successfully initialized DeepSeek adapter with model: {model_name}")
                    return
                    
                elif "claude" in model_name.lower():
                    claude_config = AdapterConfig(
                        service_name="anthropic",
                        api_key=api_key,
                        model=model_name,
                        max_retries=3
                    )
                    self.text_adapter = ClaudeAdapter(claude_config, self.config_manager, self.cost_controller)
                    self.logger.info(f"Successfully initialized Claude adapter with model: {model_name}")
                    return
                    
            except Exception as e:
                self.logger.warning(f"Failed to initialize {model_name}: {e}")
                continue
        
        # 如果所有配置都失败，抛出错误
        raise ValueError(
            "No text generation API key configured or all initialization attempts failed. "
            "Please configure at least one of: GLM_API_KEY, DEEPSEEK_API_KEY, or ANTHROPIC_API_KEY."
        )
    
    def _initialize_text_chains(self):
        """初始化文本生成链路"""
        try:
            # 初始化大纲生成链路
            from core.chains.outline_chain import OutlineChain
            self.outline_chain = OutlineChain(self.config_manager, self.cost_controller)
            self.logger.info("Successfully initialized outline chain")
            
            # 初始化场景生成链路
            from core.chains.scene_chain import SceneChain
            self.scene_chain = SceneChain(self.config_manager, self.cost_controller)
            self.logger.info("Successfully initialized scene chain")
            
            # 初始化对话生成链路
            from core.chains.dialogue_chain import DialogueChain
            self.dialogue_chain = DialogueChain(self.config_manager, self.cost_controller)
            self.logger.info("Successfully initialized dialogue chain")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize text chains: {e}")
            # 如果初始化失败，创建空的链路对象以避免属性错误
            self.outline_chain = None
            self.scene_chain = None
            self.dialogue_chain = None
    
    def _get_api_key_for_model(self, model_name: str) -> Optional[str]:
        """根据模型名称获取对应的API密钥"""
        if not model_name:
            return None
            
        model_name_lower = model_name.lower()
        
        if "glm" in model_name_lower or "zhipuai" in model_name_lower:
            return self.config_manager.get_api_key("glm")
        elif "deepseek" in model_name_lower:
            return self.config_manager.get_api_key("deepseek")
        elif "claude" in model_name_lower or "anthropic" in model_name_lower:
            return self.config_manager.get_api_key("anthropic")
        elif "gpt" in model_name_lower or "openai" in model_name_lower:
            return self.config_manager.get_api_key("openai")
        elif "qwen" in model_name_lower or "dashscope" in model_name_lower:
            return self.config_manager.get_api_key("dashscope")
        else:
            return None
    
    def _register_default_steps(self):
        """注册默认工作流步骤"""
        steps = [
            WorkflowStep(
                step_id="generate_outline",
                name="生成剧本大纲",
                stage=WorkflowStage.SCRIPT_GENERATION,
                handler=self._generate_outline,
                estimated_duration=5.0,
                estimated_cost=0.05
            ),
            WorkflowStep(
                step_id="generate_scenes",
                name="生成详细场景",
                stage=WorkflowStage.SCENE_PLANNING,
                handler=self._generate_scenes,
                dependencies=["generate_outline"],
                estimated_duration=8.0,
                estimated_cost=0.12
            ),
            WorkflowStep(
                step_id="generate_dialogues",
                name="生成角色对白",
                stage=WorkflowStage.CHARACTER_DESIGN,
                handler=self._generate_dialogues,
                dependencies=["generate_scenes"],
                estimated_duration=6.0,
                estimated_cost=0.08
            ),
            WorkflowStep(
                step_id="generate_images",
                name="生成图像",
                stage=WorkflowStage.MEDIA_GENERATION,
                handler=self._generate_images,
                dependencies=["generate_scenes", "generate_dialogues"],
                estimated_duration=10.0,
                estimated_cost=0.20
            ),
            WorkflowStep(
                step_id="generate_videos",
                name="生成视频",
                stage=WorkflowStage.MEDIA_GENERATION,
                handler=self._generate_videos,
                dependencies=["generate_images"],
                estimated_duration=15.0,
                estimated_cost=0.50
            ),
            WorkflowStep(
                step_id="synthesize_audio",
                name="合成音频",
                stage=WorkflowStage.AUDIO_SYNTHESIS,
                handler=self._synthesize_audio,
                dependencies=["generate_dialogues"],
                estimated_duration=5.0,
                estimated_cost=0.10
            ),
            WorkflowStep(
                step_id="compose_video",
                name="视频合成",
                stage=WorkflowStage.VIDEO_COMPOSITION,
                handler=self._compose_video,
                dependencies=["generate_videos", "synthesize_audio"],
                estimated_duration=8.0,
                estimated_cost=0.05
            ),
            WorkflowStep(
                step_id="quality_review",
                name="质量审核",
                stage=WorkflowStage.QUALITY_REVIEW,
                handler=self._quality_review,
                dependencies=["compose_video"],
                estimated_duration=3.0,
                estimated_cost=0.02
            ),
            WorkflowStep(
                step_id="finalize_output",
                name="最终输出",
                stage=WorkflowStage.FINALIZATION,
                handler=self._finalize_output,
                dependencies=["quality_review"],
                estimated_duration=2.0,
                estimated_cost=0.01
            )
        ]
        
        for step in steps:
            self.register_step(step)
    
    def register_step(self, step: WorkflowStep):
        """注册工作流步骤"""
        self.workflow_steps[step.step_id] = step
    
    def register_handler(self, step_id: str, handler: Callable):
        """注册步骤处理器"""
        self.step_handlers[step_id] = handler
    
    async def execute_video_production_workflow(self, script_data: ScriptData) -> WorkflowExecution:
        """执行视频制作工作流（跳过剧本生成步骤）"""
        execution_id = str(uuid.uuid4())
        execution = WorkflowExecution(
            execution_id=execution_id,
            script_id=script_data.script_id,
            status=TaskStatus.RUNNING,
            current_stage=WorkflowStage.SCRIPT_GENERATION,
            start_time=datetime.now()
        )
        
        self.executions[execution_id] = execution
        
        try:
            # 只执行媒体生成和后处理步骤
            video_production_steps = [
                "generate_images",
                "generate_videos", 
                "synthesize_audio",
                "compose_video",
                "quality_review",
                "finalize_output"
            ]
            
            for step_id in video_production_steps:
                step = self.workflow_steps.get(step_id)
                if not step:
                    self.logger.warning(f"Step {step_id} not registered, skipping")
                    continue
                try:
                    result = await self._execute_step(step, script_data, execution)
                    execution.step_results[step_id] = result
                    execution.completed_steps.append(step_id)
                    
                    # 记录成本
                    if isinstance(result, dict) and 'cost' in result:
                        execution.total_cost += float(result.get('cost') or 0.0)
                
                except Exception as e:
                    execution.failed_steps.append(step_id)
                    execution.status = TaskStatus.FAILED
                    execution.error_message = str(e)
                    break
            
            if execution.status != TaskStatus.FAILED:
                execution.status = TaskStatus.COMPLETED
                
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {str(e)}")
            execution.status = TaskStatus.FAILED
            execution.error_message = str(e)
        
        finally:
            execution.end_time = datetime.now()
        
        return execution

    async def execute_workflow(self, script_data: ScriptData) -> WorkflowExecution:
        """执行工作流"""
        execution_id = str(uuid.uuid4())
        execution = WorkflowExecution(
            execution_id=execution_id,
            script_id=script_data.script_id,
            status=TaskStatus.RUNNING,
            current_stage=None,
            start_time=datetime.now()
        )
        
        self.executions[execution_id] = execution
        
        try:
            # 检查预算（临时禁用用于测试）
            # total_estimated_cost = sum(step.estimated_cost for step in self.workflow_steps.values())
            # can_afford, reason = self.cost_controller.can_afford(total_estimated_cost)
            # 
            # if not can_afford:
            #     execution.status = TaskStatus.FAILED
            #     execution.error_message = f"Budget insufficient: {reason}"
            #     return execution
            
            # 按依赖关系排序步骤
            ordered_steps = self._topological_sort()
            
            # 执行步骤
            for step_id in ordered_steps:
                step = self.workflow_steps[step_id]
                execution.current_stage = step.stage
                
                try:
                    # 检查依赖是否完成
                    if not all(dep in execution.completed_steps for dep in step.dependencies):
                        raise Exception(f"Dependencies not met for step {step_id}")
                    
                    # 执行步骤
                    result = await self._execute_step(step, script_data, execution)
                    execution.step_results[step_id] = result
                    execution.completed_steps.append(step_id)
                    
                    # 记录成本
                    if isinstance(result, dict) and 'cost' in result:
                        execution.total_cost += float(result.get('cost') or 0.0)
                    
                except Exception as e:
                    execution.failed_steps.append(step_id)
                    execution.status = TaskStatus.FAILED
                    execution.error_message = str(e)
                    break
            
            if execution.status != TaskStatus.FAILED:
                execution.status = TaskStatus.COMPLETED
            
        except Exception as e:
            execution.status = TaskStatus.FAILED
            execution.error_message = str(e)
        
        finally:
            execution.end_time = datetime.now()
            execution.current_stage = None
        
        return execution
    
    async def _execute_step(self, step: WorkflowStep, script_data: ScriptData, execution: WorkflowExecution) -> Any:
        """执行单个步骤，带超时与重试"""
        handler = self.step_handlers.get(step.step_id, step.handler)
        attempt = 0
        last_err: Optional[BaseException] = None
        while attempt <= max(0, step.retry_count):
            attempt += 1
            start = datetime.now()
            self.logger.info(f"[START] step={step.step_id} name={step.name} attempt={attempt}/{step.retry_count + 1}")
            try:
                result = await asyncio.wait_for(handler(script_data, execution), timeout=step.timeout)
                duration = (datetime.now() - start).total_seconds()
                self.logger.info(f"[END] step={step.step_id} status={result.get('status', 'unknown')} duration={duration:.2f}s")
                return result
            except asyncio.TimeoutError as e:
                duration = (datetime.now() - start).total_seconds()
                self.logger.error(f"[TIMEOUT] step={step.step_id} after {duration:.2f}s (limit={step.timeout}s)")
                last_err = e
            except Exception as e:
                duration = (datetime.now() - start).total_seconds()
                self.logger.error(f"[ERROR] step={step.step_id} err={e} duration={duration:.2f}s")
                last_err = e
            # 还有重试机会则等待退避
            if attempt <= max(0, step.retry_count):
                backoff = min(2 ** (attempt - 1), 8)
                await asyncio.sleep(backoff)
        raise Exception(f"Step {step.step_id} failed after {attempt} attempts: {last_err}")
    
    def _topological_sort(self) -> List[str]:
        """拓扑排序工作流步骤"""
        # 简单的拓扑排序实现
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(step_id: str):
            if step_id in temp_visited:
                raise Exception(f"Circular dependency detected involving {step_id}")
            if step_id in visited:
                return
            
            temp_visited.add(step_id)
            
            step = self.workflow_steps[step_id]
            for dep in step.dependencies:
                if dep in self.workflow_steps:
                    visit(dep)
            
            temp_visited.remove(step_id)
            visited.add(step_id)
            result.append(step_id)
        
        for step_id in self.workflow_steps:
            if step_id not in visited:
                visit(step_id)
        
        return result
    
    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """获取执行状态"""
        return self.executions.get(execution_id)
    
    def cancel_execution(self, execution_id: str) -> bool:
        """取消执行"""
        if execution_id in self.executions:
            execution = self.executions[execution_id]
            execution.status = TaskStatus.CANCELLED
            execution.end_time = datetime.now()
            
            # 取消活动任务
            if execution_id in self.active_tasks:
                self.active_tasks[execution_id].cancel()
                del self.active_tasks[execution_id]
            
            return True
        return False
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """获取工作流统计信息"""
        total_executions = len(self.executions)
        completed = sum(1 for e in self.executions.values() if e.status == TaskStatus.COMPLETED)
        failed = sum(1 for e in self.executions.values() if e.status == TaskStatus.FAILED)
        running = sum(1 for e in self.executions.values() if e.status == TaskStatus.RUNNING)
        
        total_cost = sum(e.total_cost for e in self.executions.values())
        avg_cost = total_cost / total_executions if total_executions > 0 else 0
        
        # 计算平均执行时间
        completed_executions = [
            e for e in self.executions.values() 
            if e.status == TaskStatus.COMPLETED and e.start_time and e.end_time
        ]
        
        avg_duration = 0
        if completed_executions:
            total_duration = sum(
                (e.end_time - e.start_time).total_seconds() / 60  # 转换为分钟
                for e in completed_executions
            )
            avg_duration = total_duration / len(completed_executions)
        
        return {
            "total_executions": total_executions,
            "completed": completed,
            "failed": failed,
            "running": running,
            "success_rate": completed / total_executions if total_executions > 0 else 0,
            "total_cost_usd": total_cost,
            "average_cost_usd": avg_cost,
            "average_duration_minutes": avg_duration
        }
    
    # 核心链路处理器实现
    async def _generate_outline(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """生成剧本大纲"""
        try:
            # 构建大纲生成提示
            prompt = f"""
            请为以下剧本生成一个详细大纲：
            
            标题：{script_data.title or "历史短剧"}
            主题：{script_data.theme or "历史短剧"}
            时代：{getattr(script_data, 'era', '明朝')}
            目标时长：{getattr(script_data, 'total_duration', 180) or 180}秒
            类型：{getattr(script_data, 'genre', '历史剧情')}
            目标观众：{getattr(script_data, 'target_audience', '成人')}
            语气：{getattr(script_data, 'tone', '严肃')}
            关键元素：{getattr(script_data, 'key_elements', [])}
            约束条件：{getattr(script_data, 'constraints', [])}
            
            请包含以下内容：
            1. 剧情摘要
            2. 主要角色（3-5个）
            3. 场景列表（5-8个）
            
            请以JSON格式返回，包含以下字段：
            - title: 剧本标题
            - theme: 主题
            - dynasty: 时代
            - duration: 时长（秒）
            - genre: 类型
            - characters: 角色列表，每个角色包含name和description
            - scenes: 场景列表，每个场景包含title, location, time_period, description, duration
            - plot_summary: 剧情摘要
            - key_conflicts: 主要冲突列表
            - emotional_arc: 情感发展线
            """
            
            # 使用text_adapter生成大纲
            response = await self.text_adapter.generate(prompt)
            
            # 解析响应
            import json
            try:
                outline_data = json.loads(response.text)
            except json.JSONDecodeError:
                # 如果返回的不是JSON格式，尝试提取JSON部分
                import re
                json_match = re.search(r'```json\n(.*?)\n```', response.text, re.DOTALL)
                if json_match:
                    outline_data = json.loads(json_match.group(1))
                else:
                    raise ValueError("无法解析模型返回的大纲数据")
            
            # 更新script_data
            script_data.title = outline_data.get("title", script_data.title or "历史短剧")
            script_data.summary = outline_data.get("plot_summary", "")
            
            # 处理角色数据
            characters = []
            for char_data in outline_data.get("characters", []):
                if isinstance(char_data, dict):
                    personality = char_data.get("personality", "")
                    # 确保personality是列表类型
                    if isinstance(personality, str):
                        personality = [personality] if personality else []
                    elif not isinstance(personality, list):
                        personality = []
                    
                    characters.append(CharacterData(
                        name=char_data.get("name", "未知角色"),
                        description=char_data.get("description", ""),
                        personality=personality,
                        background=char_data.get("background", ""),
                        appearance="",  # 添加必需的appearance字段
                        era=getattr(script_data, 'era', '明朝'),  # 添加必需的era字段
                        social_status=""  # 添加必需的social_status字段
                    ))
            script_data.characters = characters
            
            # 处理场景数据
            scenes = []
            for i, scene_data in enumerate(outline_data.get("scenes", [])):
                if isinstance(scene_data, dict):
                    scenes.append(SceneData(
                        scene_id=str(i + 1),  # 确保scene_id是字符串类型
                        title=scene_data.get("title", f"场景{i+1}"),
                        location=scene_data.get("location", "未知地点"),
                        time_period=scene_data.get("time_period", "未知时间"),
                        description=scene_data.get("description", ""),
                        duration=scene_data.get("duration", 30),
                        background_description="",
                        atmosphere="",
                        lighting="",
                        weather=None,  # 添加可选的weather字段
                        camera_angle="medium"  # 添加必需的camera_angle字段
                    ))
            script_data.scenes = scenes
            
            return {
                "status": "completed",
                "outline_generated": True,
                "characters_count": len(characters),
                "scenes_count": len(scenes),
                "cost": response.cost_usd if hasattr(response, 'cost_usd') else 0.0
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "cost": 0.0
            }
    
    async def _generate_scenes(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """生成详细场景"""
        try:
            generated_scenes = []
            total_cost = 0.0
            
            for i, scene in enumerate(script_data.scenes):
                # 构建场景生成提示
                prompt = f"""
                请为以下场景生成详细描述：
                
                场景标题：{scene.title}
                地点：{scene.location}
                时间：{scene.time_period}
                时长：{scene.duration}秒
                剧本主题：{script_data.theme}
                剧本摘要：{script_data.summary or ""}
                历史时期：{getattr(script_data, 'era', '明朝')}
                
                角色信息：
                {chr(10).join([f"- {char.name}: {char.description}" for char in script_data.characters])}
                
                请包含以下内容：
                1. 环境描述（主要场景设置）
                2. 背景描述（环境、氛围、光线等）
                3. 氛围描述（情感基调、气氛等）
                4. 光线描述（自然光、人工光等）
                
                请以JSON格式返回，包含以下字段：
                - main_setting: 主要场景设置
                - background_description: 背景描述
                - atmosphere: 氛围描述
                - lighting: 光线描述
                - key_elements: 关键元素列表
                - visual_effects: 视觉效果建议
                - duration: 场景时长（秒）
                """
                
                # 使用text_adapter生成场景详情
                response = await self.text_adapter.generate(prompt)
                
                # 解析响应
                import json
                try:
                    scene_data = json.loads(response.text)
                except json.JSONDecodeError:
                    # 如果返回的不是JSON格式，尝试提取JSON部分
                    import re
                    json_match = re.search(r'```json\n(.*?)\n```', response.text, re.DOTALL)
                    if json_match:
                        scene_data = json.loads(json_match.group(1))
                    else:
                        raise ValueError("无法解析模型返回的场景数据")
                
                # 更新场景数据
                if scene_data.get("main_setting"):
                    scene.description = scene_data.get("main_setting")
                scene.background_description = scene_data.get("background_description", "")
                scene.atmosphere = scene_data.get("atmosphere", "")
                scene.lighting = scene_data.get("lighting", "")
                if scene_data.get("duration"):
                    scene.duration = scene_data.get("duration")
                
                # 累计成本
                if hasattr(response, 'cost_usd'):
                    total_cost += response.cost_usd
            
            return {
                "status": "completed",
                "scenes_generated": len(script_data.scenes),
                "total_duration": sum(scene.duration for scene in script_data.scenes),
                "cost": total_cost
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "cost": 0.0
            }
    
    async def _generate_dialogues(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """生成角色对白"""
        try:
            generated_dialogues = []
            total_cost = 0.0
            
            for i, scene in enumerate(script_data.scenes):
                # 构建对话生成提示
                prompt = f"""
                请为以下场景生成角色对白：
                
                场景标题：{scene.title}
                地点：{scene.location}
                时间：{scene.time_period}
                时长：{scene.duration}秒
                场景描述：{scene.description}
                背景描述：{scene.background_description}
                氛围：{scene.atmosphere}
                
                剧本主题：{script_data.theme}
                剧本摘要：{script_data.summary or ""}
                历史时期：{getattr(script_data, 'era', '明朝')}
                
                角色信息：
                {chr(10).join([f"- {char.name}: {char.description}" for char in script_data.characters])}
                
                请生成符合场景和角色特点的对白，以JSON格式返回，包含以下字段：
                - dialogue_lines: 对话行列表，每行包含character（角色名）、content（对话内容）、emotion（情感）、action（动作描述）
                - scene_summary: 场景对话总结
                - emotional_progression: 情感发展线
                """
                
                # 使用text_adapter生成对话
                response = await self.text_adapter.generate(prompt)
                
                # 解析响应
                import json
                try:
                    dialogue_data = json.loads(response.text)
                except json.JSONDecodeError:
                    # 如果返回的不是JSON格式，尝试提取JSON部分
                    import re
                    json_match = re.search(r'```json\n(.*?)\n```', response.text, re.DOTALL)
                    if json_match:
                        dialogue_data = json.loads(json_match.group(1))
                    else:
                        raise ValueError("无法解析模型返回的对话数据")
                
                # 处理对话数据
                dialogue_lines = []
                for line_data in dialogue_data.get("dialogue_lines", []):
                    if isinstance(line_data, dict):
                        dialogue_lines.append(DialogueLine(
                            character=line_data.get("character", "未知角色"),
                            content=line_data.get("content", ""),
                            emotion=line_data.get("emotion", "neutral"),  # 修改默认值为"neutral"
                            action=line_data.get("action", "")
                        ))
                
                # 更新场景对话
                scene.dialogue_lines = dialogue_lines
                
                # 累计成本
                if hasattr(response, 'cost_usd'):
                    total_cost += response.cost_usd
            
            return {
                "status": "completed",
                "dialogues_generated": len(script_data.scenes),
                "total_lines": sum(len(scene.dialogue_lines) for scene in script_data.scenes),
                "cost": total_cost
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "cost": 0.0
            }
    
    async def _generate_images(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """生成图像"""
        try:
            from adapters.image.base_image import ImageGenerationRequest
            
            generated_images = []
            total_cost = 0.0
            
            # 为每个场景生成图像
            for i, scene in enumerate(script_data.scenes):
                # 构建图像生成提示
                prompt = f"{scene.background_description}, {scene.atmosphere}, {scene.lighting}, 历史剧风格, 电影级质量"
                
                # 调用图像适配器（传递prompt字符串和参数）
                result = await self.image_adapter.generate(
                    prompt=prompt,
                    negative_prompt="low quality, blurry, distorted",
                    width=1024,
                    height=1024,
                    steps=4,
                    guidance_scale=7.5,
                    style="cinematic"
                )
                
                if result.success and result.data:
                    # 保存图像并获取路径
                    output_dir = Path(self.config_manager.get_env_settings().output_dir)
                    project_dir = output_dir / script_data.script_id
                    project_dir.mkdir(parents=True, exist_ok=True)
                    
                    image_paths = result.data.save_images(
                        output_dir=str(project_dir),
                        prefix=f"scene_{i+1}"
                    )
                    
                    generated_images.append({
                        "scene_id": scene.scene_id,
                        "image_path": image_paths[0] if image_paths else None,
                        "prompt": prompt
                    })
                    total_cost += result.cost_usd if hasattr(result, 'cost_usd') else 0.0
                else:
                    # 图像生成失败，抛出错误
                    error_msg = f"Image generation failed for scene {scene.scene_id}: {result.error_message}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)
                
            return {
                "status": "completed",
                "images_generated": len(generated_images),
                "images": generated_images,
                "cost": total_cost
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "cost": 0.0
            }
    
    async def _generate_videos(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """生成视频"""
        try:
            from adapters.video.base_video import VideoGenerationRequest, VideoQuality
            
            generated_videos = []
            total_cost = 0.0
            
            # 获取图像生成结果
            image_results = execution.step_results.get('generate_images', {})
            images = image_results.get('images', [])
            
            # 为每个场景生成视频
            for i, scene in enumerate(script_data.scenes):
                # 找到对应的图像
                scene_image = None
                for img in images:
                    if img.get('scene_id') == scene.scene_id:
                        scene_image = img.get('image_path')
                        break
                
                # 构建视频生成提示
                prompt = f"{scene.background_description}, {scene.atmosphere}, 历史剧风格, 电影级画质"
                
                # 创建视频生成请求
                scene_duration = max(getattr(scene, 'duration', 5.0), 1.0)  # 确保最小1秒
                video_request = VideoGenerationRequest(
                    prompt=prompt,
                    image_prompt=scene_image,  # 使用图像提示而不是image_path
                    duration=min(scene_duration, 8.0),  # Kling最大8秒
                    quality=VideoQuality.HIGH,
                    fps=24
                )
                
                # 调用视频适配器
                try:
                    video_response = await self.video_adapter.generate(video_request)
                    
                    # video_response 应该是 VideoGenerationResponse 对象
                    if video_response and hasattr(video_response, 'video_url'):
                        generated_videos.append({
                            "scene_id": scene.scene_id,
                            "video_path": video_response.video_url or f"video_scene_{i+1}.mp4",
                            "duration": video_response.duration,
                            "prompt": prompt
                        })
                        # 暂时使用固定成本，因为响应可能没有cost属性
                        total_cost += 0.1  # 估算成本
                    else:
                        # 视频生成失败，记录警告但不抛出错误
                        self.logger.warning(f"Video generation failed for scene {scene.scene_id}, continuing without video")
                        
                except Exception as video_error:
                    # 视频生成失败，记录错误但继续处理
                    self.logger.error(f"Video generation error for scene {scene.scene_id}: {str(video_error)}")
                
            return {
                "status": "completed",
                "videos_generated": len(generated_videos),
                "videos": generated_videos,
                "cost": total_cost
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "cost": 0.0
            }
    
    
    async def _synthesize_audio(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """合成音频"""
        try:
            from adapters.voice.base_voice import VoiceSynthesisRequest, VoiceGender
            
            generated_audio = []
            total_cost = 0.0
            
            # 为每个角色的对话生成音频
            for character in script_data.characters:
                # 从所有场景中收集该角色的对话
                character_dialogues = []
                for scene in script_data.scenes:
                    for dialogue_line in scene.dialogue_lines:
                        if dialogue_line.character == character.name:
                            character_dialogues.append(dialogue_line)
                
                for dialogue in character_dialogues:
                    # 根据配置文件和角色性别选择语音
                    voice_gender = VoiceGender.MALE if "男" in character.appearance else VoiceGender.FEMALE
                    
                    # 从配置文件获取语音设置
                    voice_config = getattr(self.config_manager.app_config, 'voice_synthesis', None)
                    if voice_config:
                        # 如果配置了特定的语音ID，优先使用
                        if hasattr(voice_config, 'voice_id'):
                            voice_id = voice_config.voice_id
                        else:
                            # 根据性别选择默认语音
                            voice_id = "zh-CN-XiaoxiaoNeural" if voice_gender == VoiceGender.FEMALE else "zh-CN-YunxiNeural"
                        
                        # 获取语速和音调设置
                        speed = getattr(voice_config, 'speed', 1.0)
                        pitch = getattr(voice_config, 'pitch', 1.0)
                        language = getattr(voice_config, 'language', "zh-CN")
                    else:
                        # 使用默认设置
                        voice_id = "zh-CN-XiaoxiaoNeural" if voice_gender == VoiceGender.FEMALE else "zh-CN-YunxiNeural"
                        speed = 1.0
                        pitch = 1.0
                        language = "zh-CN"
                        
                    self.logger.debug(f"Using voice {voice_id} for character {character.name} (gender: {voice_gender})")
                    
                    # 创建语音合成请求
                    voice_request = VoiceSynthesisRequest(
                        text=dialogue.content,
                        voice_id=voice_id,
                        speed=speed,
                        pitch=pitch,
                        language=language
                    )
                    
                    # 调用语音适配器
                    try:
                        voice_response = await self.voice_adapter.generate(voice_request)
                        
                        # voice_response 应该是 VoiceSynthesisResponse 对象
                        if voice_response and hasattr(voice_response, 'audio_data'):
                            # 保存音频文件
                            output_dir = Path(self.config_manager.get_env_settings().output_dir)
                            project_dir = output_dir / script_data.script_id
                            project_dir.mkdir(parents=True, exist_ok=True)
                            
                            audio_filename = f"dialogue_{character.name}_{len(generated_audio)}.wav"
                            audio_path = project_dir / audio_filename
                            
                            if voice_response.audio_data:
                                with open(audio_path, "wb") as f:
                                    f.write(voice_response.audio_data)
                            
                            generated_audio.append({
                                "character": character.name,
                                "dialogue_id": f"{character.name}_{len(generated_audio)}",
                                "audio_path": str(audio_path),
                                "duration": getattr(voice_response, 'duration', 3.0),
                                "text": dialogue.content
                            })
                            total_cost += float(getattr(voice_response, 'cost', 0.0))
                    except Exception as voice_error:
                        self.logger.warning(f"Voice synthesis failed for {character.name}: {str(voice_error)}")
            
            return {
                "status": "completed",
                "audio_files_generated": len(generated_audio),
                "audio_files": generated_audio,
                "cost": total_cost
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "cost": 0.0
            }
    
    async def _compose_video(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """视频合成"""
        try:
            from pathlib import Path
            
            # 创建输出目录
            output_dir = Path(self.config_manager.get_env_settings().output_dir)
            project_dir = output_dir / script_data.script_id
            project_dir.mkdir(parents=True, exist_ok=True)
            
            # 获取生成的媒体资源
            video_results = execution.step_results.get('generate_videos', {})
            audio_results = execution.step_results.get('synthesize_audio', {})
            
            videos = video_results.get('videos', [])
            audio_files = audio_results.get('audio_files', [])
            
            # 简单的视频合成逻辑（这里可以集成FFmpeg或其他视频处理库）
            final_video_path = project_dir / f"{script_data.script_id}_final.mp4"
            
            # 合成实际的视频文件
            if videos:
                # 如果有生成的视频片段，合成最终视频
                await self._compose_final_video(videos, audio_files, final_video_path)
            else:
                # 没有视频片段，抛出错误
                error_msg = "No video segments available for composition"
                self.logger.error(error_msg)
                raise Exception(error_msg)
            
            await asyncio.sleep(1)
            
            # 创建合成信息文件
            composition_info = {
                "project_id": script_data.script_id,
                "title": script_data.title,
                "total_scenes": len(videos),
                "total_audio_files": len(audio_files),
                "final_video_path": str(final_video_path),
                "composition_timestamp": datetime.now().isoformat()
            }
            
            import json
            with open(project_dir / "composition_info.json", 'w', encoding='utf-8') as f:
                json.dump(composition_info, f, ensure_ascii=False, indent=2)
            
            return {
                "status": "completed",
                "final_video_path": str(final_video_path),
                "project_directory": str(project_dir),
                "scenes_composed": len(videos),
                "audio_tracks": len(audio_files)
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
    
    async def _compose_final_video(self, videos, audio_files, final_video_path):
        """合成最终视频文件"""
        try:
            import subprocess
            from pathlib import Path
            
            # 如果有视频片段，尝试使用FFmpeg合成
            if videos and len(videos) > 0:
                # 创建临时文件列表
                temp_list_file = final_video_path.parent / "video_list.txt"
                
                with open(temp_list_file, 'w') as f:
                    for video in videos:
                        video_path = video.get('video_path')
                        if video_path and Path(video_path).exists():
                            f.write(f"file '{video_path}'\n")
                
                # 使用FFmpeg合成视频
                ffmpeg_cmd = [
                    'ffmpeg', '-y',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', str(temp_list_file),
                    '-c', 'copy',
                    str(final_video_path)
                ]
                
                try:
                    result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=60)
                    if result.returncode == 0:
                        self.logger.info(f"Successfully composed final video: {final_video_path}")
                        # 清理临时文件
                        temp_list_file.unlink(missing_ok=True)
                        return
                    else:
                        self.logger.warning(f"FFmpeg composition failed: {result.stderr}")
                        
                except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                    self.logger.warning(f"FFmpeg not available for composition: {e}")
                
                # 清理临时文件
                temp_list_file.unlink(missing_ok=True)
            
            # 如果FFmpeg失败或不可用，复制第一个视频文件作为最终输出
            if videos and len(videos) > 0:
                first_video_path = videos[0].get('video_path')
                if first_video_path and Path(first_video_path).exists():
                    import shutil
                    shutil.copy2(first_video_path, final_video_path)
                    self.logger.info(f"Copied first video as final output: {final_video_path}")
                    return
            
            # 如果都失败了，抛出错误
            raise Exception("No valid video files to compose")
            
        except Exception as e:
            self.logger.error(f"Video composition failed: {e}")
            raise e
    
    async def _quality_review(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """质量审核（占位符）"""
        await asyncio.sleep(1)
        return {
            "status": "completed",
            "quality_score": 0.85,
            "passed": True
        }
    
    async def _finalize_output(self, script_data: ScriptData, execution: WorkflowExecution) -> Dict[str, Any]:
        """最终输出"""
        try:
            from pathlib import Path
            import json
            
            # 获取项目目录
            output_dir = Path(self.config_manager.get_env_settings().output_dir)
            project_dir = output_dir / script_data.script_id
            
            # 创建完整的项目元数据
            metadata = {
                "project_info": {
                    "script_id": script_data.script_id,
                    "title": script_data.title,
                    "theme": script_data.theme,
                    "era": script_data.era,
                    "total_duration": script_data.total_duration,
                    "created_at": script_data.created_at.isoformat() if hasattr(script_data, 'created_at') else datetime.now().isoformat()
                },
                "production_summary": {
                    "execution_id": execution.execution_id,
                    "total_cost": execution.total_cost,
                    "completed_steps": execution.completed_steps,
                    "failed_steps": execution.failed_steps,
                    "start_time": execution.start_time.isoformat() if execution.start_time else None,
                    "end_time": datetime.now().isoformat()
                },
                "characters": [{
                    "name": char.name,
                    "title": char.title,
                    "description": char.description,
                    "era": char.era
                } for char in script_data.characters],
                "scenes": [{
                    "scene_id": scene.scene_id,
                    "title": scene.title,
                    "location": scene.location,
                    "duration": scene.duration
                } for scene in script_data.scenes],
                "step_results": execution.step_results
            }
            
            # 保存元数据
            metadata_path = project_dir / f"{script_data.script_id}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # 创建README文件
            readme_content = f"""# {script_data.title}

## 项目信息
- 主题: {script_data.theme}
- 时代: {script_data.era}
- 总时长: {script_data.total_duration}秒
- 执行ID: {execution.execution_id}

## 制作统计
- 角色数量: {len(script_data.characters)}
- 场景数量: {len(script_data.scenes)}
- 总成本: ${execution.total_cost:.4f}
- 完成步骤: {len(execution.completed_steps)}/9

## 文件结构
- 最终视频: {script_data.script_id}_final.mp4
- 项目元数据: {script_data.script_id}_metadata.json
- 合成信息: composition_info.json
"""
            
            readme_path = project_dir / "README.md"
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            return {
                "status": "completed",
                "project_directory": str(project_dir),
                "metadata_path": str(metadata_path),
                "readme_path": str(readme_path),
                "final_video_path": str(project_dir / f"{script_data.script_id}_final.mp4"),
                "files_created": [
                    str(metadata_path.name),
                    str(readme_path.name),
                    "composition_info.json"
                ]
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }