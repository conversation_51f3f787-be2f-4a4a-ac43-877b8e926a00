"""数据模型定义

定义系统中使用的核心数据结构，包括剧本、场景、角色、媒体提示等。
"""

from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from pathlib import Path


class MediaType(str, Enum):
    """媒体类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class QualityLevel(str, Enum):
    """质量等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"


class CharacterData(BaseModel):
    """角色数据模型"""
    name: str = Field(..., description="角色姓名")
    title: Optional[str] = Field(None, description="角色称号")
    description: str = Field(..., description="角色描述")
    personality: List[str] = Field(default_factory=list, description="性格特征")
    appearance: str = Field(..., description="外貌描述")
    voice_style: str = Field(default="default", description="语音风格")
    era: str = Field(..., description="所属时代")
    social_status: str = Field(..., description="社会地位")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "李世民",
                "title": "唐太宗",
                "description": "唐朝第二位皇帝，贞观之治的开创者",
                "personality": ["英明", "果断", "仁慈"],
                "appearance": "中年男子，身材魁梧，面容威严，身着龙袍",
                "voice_style": "威严",
                "era": "唐朝",
                "social_status": "皇帝"
            }
        }
    )


class DialogueLine(BaseModel):
    """对话行数据模型"""
    character: str = Field(..., description="角色名")
    content: str = Field(..., description="对话内容")
    emotion: str = Field(default="neutral", description="情感")
    action: str = Field(default="", description="动作描述")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "character": "李世民",
                "content": "众爱卿，今日朝会，有何要事奏报？",
                "emotion": "威严",
                "action": "威严地环视朝堂"
            }
        }
    )


class DialogueData(BaseModel):
    """对话数据模型"""
    speaker: str = Field(..., description="说话人")
    content: str = Field(..., description="对话内容")
    emotion: str = Field(default="neutral", description="情感")
    voice_style: Optional[str] = Field(None, description="语音风格")
    emphasis: List[str] = Field(default_factory=list, description="重点词汇")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "speaker": "李世民",
                "content": "众爱卿，今日朝会，有何要事奏报？",
                "emotion": "威严",
                "voice_style": "imperial",
                "emphasis": ["众爱卿", "要事"]
            }
        }
    )


class SceneData(BaseModel):
    """场景数据模型"""
    scene_id: str = Field(..., description="场景ID")
    title: str = Field(..., description="场景标题")
    location: str = Field(..., description="场景地点")
    time_period: str = Field(..., description="时间段")
    weather: Optional[str] = Field(None, description="天气")
    atmosphere: str = Field(..., description="氛围描述")
    background_description: str = Field(..., description="背景描述")
    lighting: str = Field(default="natural", description="光照条件")
    camera_angle: str = Field(default="medium", description="镜头角度")
    duration: float = Field(default=5.0, ge=1.0, le=30.0, description="场景时长（秒）")
    dialogue_lines: List[DialogueLine] = Field(default_factory=list, description="对话行列表")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "scene_id": "scene_001",
                "title": "太极殿朝会",
                "location": "太极殿",
                "time_period": "清晨",
                "weather": "晴朗",
                "atmosphere": "庄严肃穆",
                "background_description": "金碧辉煌的太极殿内，文武百官整齐列队",
                "lighting": "宫殿内的烛光和晨光",
                "camera_angle": "wide",
                "duration": 8.0,
                "dialogue_lines": []
            }
        }
    )


class MediaCue(BaseModel):
    """媒体提示数据模型"""
    cue_id: str = Field(..., description="提示ID")
    media_type: MediaType = Field(..., description="媒体类型")
    prompt: str = Field(..., description="生成提示")
    style_params: Dict[str, Any] = Field(default_factory=dict, description="风格参数")
    quality_level: QualityLevel = Field(default=QualityLevel.HIGH, description="质量等级")
    priority: int = Field(default=1, ge=1, le=10, description="优先级")
    dependencies: List[str] = Field(default_factory=list, description="依赖的其他媒体")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "cue_id": "img_001",
                "media_type": "image",
                "prompt": "唐朝太极殿内，李世民身着龙袍坐在龙椅上，威严庄重",
                "style_params": {
                    "style": "cinematic",
                    "lighting": "dramatic",
                    "color_tone": "warm"
                },
                "quality_level": "high",
                "priority": 1,
                "dependencies": [],
                "metadata": {
                    "character": "李世民",
                    "scene": "太极殿",
                    "era": "唐朝"
                }
            }
        }
    )


class ScriptData(BaseModel):
    """剧本数据模型"""
    script_id: str = Field(..., description="剧本ID")
    title: str = Field(..., description="剧本标题")
    theme: str = Field(..., description="主题")
    era: str = Field(..., description="历史时代")
    summary: str = Field(..., description="剧本摘要")
    characters: List[CharacterData] = Field(..., description="角色列表")
    scenes: List[SceneData] = Field(..., description="场景列表")
    dialogues: List[DialogueData] = Field(..., description="对话列表")
    media_cues: List[MediaCue] = Field(..., description="媒体提示列表")
    total_duration: float = Field(default=0.0, description="总时长（秒）")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    # 最终润色相关属性
    final_summary: str = Field(default="", description="最终润色摘要")
    character_development_notes: str = Field(default="", description="角色发展笔记")
    scene_flow_notes: str = Field(default="", description="场景流程笔记")
    dialogue_improvements: str = Field(default="", description="对话改进建议")
    overall_quality_rating: int = Field(default=5, ge=1, le=10, description="整体质量评分")
    production_recommendations: List[str] = Field(default_factory=list, description="制作建议")
    
    @model_validator(mode="after")
    def calculate_total_duration(self):
        """根据 scenes 自动计算总时长"""
        try:
            if getattr(self, 'scenes', None):
                self.total_duration = sum(scene.duration for scene in self.scenes)
        except Exception:
            pass
        return self
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "script_id": "script_001",
                "title": "贞观之治",
                "theme": "唐太宗治国理政",
                "era": "唐朝",
                "summary": "讲述唐太宗李世民在贞观年间的治国故事",
                "characters": [],
                "scenes": [],
                "dialogues": [],
                "media_cues": [],
                "total_duration": 60.0,
                "final_summary": "最终润色后的剧本摘要",
                "character_development_notes": "角色发展笔记",
                "scene_flow_notes": "场景流程笔记",
                "dialogue_improvements": "对话改进建议",
                "overall_quality_rating": 8,
                "production_recommendations": ["建议1", "建议2"]
            }
        }
    )


class ProductionTask(BaseModel):
    """生产任务模型"""
    task_id: str = Field(..., description="任务ID")
    script_id: str = Field(..., description="关联的剧本ID")
    task_type: str = Field(..., description="任务类型")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    priority: int = Field(default=1, ge=1, le=10, description="优先级")
    input_data: Dict[str, Any] = Field(default_factory=dict, description="输入数据")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="进度")
    estimated_cost: float = Field(default=0.0, ge=0.0, description="预估成本")
    actual_cost: float = Field(default=0.0, ge=0.0, description="实际成本")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "task_id": "task_001",
                "script_id": "script_001",
                "task_type": "image_generation",
                "status": "pending",
                "priority": 1,
                "input_data": {
                    "prompt": "唐朝皇帝坐在龙椅上",
                    "style": "cinematic"
                },
                "estimated_cost": 0.05,
                "progress": 0.0
            }
        }
    )


class QualityMetrics(BaseModel):
    """质量指标模型"""
    overall_score: float = Field(default=0.0, ge=0.0, le=1.0, description="总体评分")
    visual_quality: float = Field(default=0.0, ge=0.0, le=1.0, description="视觉质量")
    audio_quality: float = Field(default=0.0, ge=0.0, le=1.0, description="音频质量")
    narrative_coherence: float = Field(default=0.0, ge=0.0, le=1.0, description="叙事连贯性")
    historical_accuracy: float = Field(default=0.0, ge=0.0, le=1.0, description="历史准确性")
    production_time: float = Field(default=0.0, ge=0.0, description="制作时间（分钟）")
    cost_efficiency: float = Field(default=0.0, ge=0.0, description="成本效率")
    user_satisfaction: Optional[float] = Field(None, ge=0.0, le=1.0, description="用户满意度")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "overall_score": 0.85,
                "visual_quality": 0.9,
                "audio_quality": 0.8,
                "narrative_coherence": 0.85,
                "historical_accuracy": 0.9,
                "production_time": 45.0,
                "cost_efficiency": 0.8
            }
        }
    )


class MediaAsset(BaseModel):
    """媒体资产模型"""
    asset_id: str = Field(..., description="资产ID")
    media_type: MediaType = Field(..., description="媒体类型")
    file_path: str = Field(..., description="文件路径")
    file_size: int = Field(default=0, ge=0, description="文件大小（字节）")
    duration: Optional[float] = Field(None, ge=0.0, description="时长（秒）")
    resolution: Optional[str] = Field(None, description="分辨率")
    format: str = Field(..., description="文件格式")
    quality_score: float = Field(default=0.0, ge=0.0, le=1.0, description="质量评分")
    generation_cost: float = Field(default=0.0, ge=0.0, description="生成成本")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    @field_validator('file_path')
    def validate_file_path(cls, v):
        """验证文件路径"""
        path = Path(v)
        if not path.exists():
            raise ValueError(f"File does not exist: {v}")
        return str(path.absolute())
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "asset_id": "asset_001",
                "media_type": "image",
                "file_path": "./output/images/scene_001.png",
                "file_size": 1024000,
                "resolution": "1024x1024",
                "format": "png",
                "quality_score": 0.9,
                "generation_cost": 0.05,
                "metadata": {
                    "prompt": "唐朝皇帝",
                    "style": "cinematic"
                }
            }
        }
    )


class ProjectData(BaseModel):
    """项目数据模型"""
    project_id: str = Field(..., description="项目ID")
    name: str = Field(..., description="项目名称")
    description: str = Field(..., description="项目描述")
    scripts: List[ScriptData] = Field(default_factory=list, description="剧本列表")
    tasks: List[ProductionTask] = Field(default_factory=list, description="任务列表")
    assets: List[MediaAsset] = Field(default_factory=list, description="资产列表")
    quality_metrics: Optional[QualityMetrics] = Field(None, description="质量指标")
    total_cost: float = Field(default=0.0, ge=0.0, description="总成本")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="项目状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "project_id": "proj_001",
                "name": "历史短剧系列第一季",
                "description": "制作一系列历史短剧视频",
                "scripts": [],
                "tasks": [],
                "assets": [],
                "total_cost": 0.0,
                "status": "pending"
            }
        }
    )