"""
LLM提供者注册模块
负责注册和管理各种LLM提供者
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class LLMProviderRegistry:
    """LLM提供者注册表"""
    
    _providers = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class):
        """注册提供者"""
        cls._providers[name] = provider_class
        logger.info(f"Registered LLM provider: {name}")
    
    @classmethod
    def create_provider(cls, name: str, **kwargs):
        """创建提供者实例"""
        if name not in cls._providers:
            raise ValueError(f"Unknown LLM provider: {name}")
        
        provider_class = cls._providers[name]
        return provider_class(**kwargs)
    
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """获取可用的提供者列表"""
        return list(cls._providers.keys())


def register_providers():
    """注册所有LLM提供者"""
    try:
        from .llm_caller_adapter import LLMCallerAdapter
        LLMProviderRegistry.register_provider("caller", LLMCallerAdapter)
        logger.info("Registered LLMCallerAdapter")
    except ImportError as e:
        logger.error(f"Failed to register LLMCallerAdapter: {e}")
    
    try:
        from .litellm_router_adapter import LiteLLMRouterAdapter
        LLMProviderRegistry.register_provider("router", LiteLLMRouterAdapter)
        logger.info("Registered LiteLLMRouterAdapter")
    except ImportError as e:
        logger.error(f"Failed to register LiteLLMRouterAdapter: {e}")


# 注册所有提供者
register_providers()