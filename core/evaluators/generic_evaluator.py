"""
通用评估器
使用组合模式实现，可以灵活配置各种评估任务
"""

import logging
from typing import Dict, Any, Optional, List

from ..llm_caller import LLMCaller
from .response_parser import ResponseParser
from .prompt_builder import PromptBuilder

# 配置日志
logger = logging.getLogger(__name__)

class GenericEvaluator:
    """通用评估器，使用组合模式实现"""
    
    def __init__(self, 
                 model_name: str = "gpt-4",
                 required_fields: List[str] = None,
                 score_fields: List[str] = None,
                 system_message: str = "你是一位专业的评论家。"):
        """
        初始化通用评估器
        
        Args:
            model_name: 使用的模型名称
            required_fields: 必要字段列表
            score_fields: 评分字段列表
            system_message: 系统消息
        """
        # 使用组合模式，组合各个组件
        self.llm_caller = LLMCaller(model_name)
        self.response_parser = ResponseParser(
            required_fields=required_fields or ["suggestions"],
            score_fields=score_fields or []
        )
        self.prompt_builder = PromptBuilder()
        self.system_message = system_message
    
    async def evaluate(self, 
                      content: str, 
                      criteria: Dict[str, str],
                      context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        通用评价方法
        
        Args:
            content: 要评估的内容
            criteria: 评价标准字典，键为评分字段名，值为评价标准描述
            context: 评估上下文
            
        Returns:
            评价结果字典
        """
        # 获取上下文信息
        context_str = ""
        if context:
            context_parts = []
            for key, value in context.items():
                context_parts.append(f"{key}: {value}")
            context_str = "\n".join(context_parts)
        
        # 构建评价提示词
        prompt = self.prompt_builder.build_general_evaluation_prompt(content, criteria, context_str)
        
        # 调用大模型API进行评价
        try:
            response = await self.llm_caller.call_llm_api(prompt, self.system_message)
            evaluation_result = self.response_parser.parse_evaluation_response(response)
            return evaluation_result
        except Exception as e:
            logger.error(f"AI评价失败: {str(e)}")
            # 返回默认评价结果
            return self._get_default_evaluation_result(criteria)
    
    def _get_default_evaluation_result(self, criteria: Dict[str, str]) -> Dict[str, Any]:
        """获取默认评价结果"""
        result = {"suggestions": ["AI评价失败，使用默认评分"]}
        
        # 添加默认评分
        for score_field in criteria.keys():
            result[score_field] = 5.0
        
        return result