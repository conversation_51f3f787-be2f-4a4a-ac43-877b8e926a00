"""
响应解析组件
负责解析AI返回的响应，处理JSON格式和错误情况
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional

# 配置日志
logger = logging.getLogger(__name__)

class ResponseParser:
    """响应解析组件"""
    
    def __init__(self, required_fields: List[str] = None, score_fields: List[str] = None):
        """
        初始化响应解析器
        
        Args:
            required_fields: 必要字段列表
            score_fields: 评分字段列表
        """
        self.required_fields = required_fields or ["suggestions"]
        self.score_fields = score_fields or []
    
    def parse_evaluation_response(self, response: str) -> Dict[str, Any]:
        """
        解析评价响应
        
        Args:
            response: 模型返回的响应
            
        Returns:
            解析后的评价结果
        """
        try:
            # 检查响应是否为空
            if not response:
                logger.warning("收到空的评价响应")
                return self._get_default_evaluation_result()
            
            # 处理可能的Markdown代码块标记
            response = self._clean_markdown_code_blocks(response)
            
            # 尝试解析JSON响应
            result = json.loads(response)
            
            # 验证必要的字段
            for field in self.required_fields:
                if field not in result:
                    logger.warning(f"评价响应缺少必要字段: {field}")
                    result[field] = self._get_default_value_for_field(field)
            
            # 处理评分字段
            for score_field in self.score_fields:
                if score_field in result:
                    if isinstance(result[score_field], (int, float)):
                        result[score_field] = max(0.0, min(10.0, float(result[score_field])))
                    else:
                        result[score_field] = 0.0
                        logger.warning(f"无效的评分值: {result[score_field]}")
            
            # 处理suggestions字段
            if "suggestions" in result:
                result["suggestions"] = self._process_suggestions(result["suggestions"])
            
            return result
        except json.JSONDecodeError as e:
            logger.error(f"解析评价响应JSON失败: {str(e)}")
            logger.error(f"响应内容: {response}")
            
            # 尝试修复常见的JSON格式错误
            try:
                fixed_response = self._fix_json_errors(response)
                result = json.loads(fixed_response)
                
                # 验证必要的字段
                for field in self.required_fields:
                    if field not in result:
                        logger.warning(f"评价响应缺少必要字段: {field}")
                        result[field] = self._get_default_value_for_field(field)
                
                return result
            except:
                # 如果修复失败，返回默认评价结果
                return self._get_default_evaluation_result()
        except Exception as e:
            logger.error(f"处理评价响应失败: {str(e)}")
            # 返回默认评价结果
            return self._get_default_evaluation_result()
    
    def _clean_markdown_code_blocks(self, response: str) -> str:
        """清理Markdown代码块标记"""
        if not response:
            return ""
        
        if response.strip().startswith('```json'):
            # 移除开头的```json
            response = response.strip()[7:]
            # 移除结尾的```
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
        elif response.strip().startswith('```'):
            # 移除开头的```
            response = response.strip()[3:]
            # 移除结尾的```
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
        
        return response
    
    def _fix_json_errors(self, response: str) -> str:
        """修复常见的JSON格式错误"""
        fixed_response = response
        
        # 修复suggestions数组中缺少的右方括号
        if '"suggestions"' in fixed_response and fixed_response.count('[') > fixed_response.count(']'):
            # 找到suggestions数组的开始位置
            suggestions_start = fixed_response.find('"suggestions"')
            array_start = fixed_response.find('[', suggestions_start)
            if array_start != -1:
                # 在末尾添加缺少的右方括号
                fixed_response += ']'
        
        # 修复suggestions数组中的键值对格式
        if '"suggestions"' in fixed_response:
            # 匹配suggestions数组中的键值对格式，如 "key": "value"
            pattern = r'"([^"]+)":\s*"([^"]+)"'
            def replace_suggestion_item(match):
                return f'"{match.group(2)}"'
            
            # 只在suggestions数组中替换
            suggestions_start = fixed_response.find('"suggestions"')
            if suggestions_start != -1:
                # 找到suggestions数组的开始和结束
                array_start = fixed_response.find('[', suggestions_start)
                if array_start != -1:
                    # 找到匹配的括号
                    bracket_count = 0
                    array_end = array_start
                    for i, char in enumerate(fixed_response[array_start:]):
                        if char == '[':
                            bracket_count += 1
                        elif char == ']':
                            bracket_count -= 1
                            if bracket_count == 0:
                                array_end = array_start + i
                                break
                    
                    if array_end > array_start:
                        # 提取suggestions数组内容
                        before_array = fixed_response[:array_start+1]
                        array_content = fixed_response[array_start+1:array_end]
                        after_array = fixed_response[array_end:]
                        
                        # 修复数组内容中的键值对格式
                        fixed_array_content = re.sub(pattern, replace_suggestion_item, array_content)
                        
                        # 重新组合响应
                        fixed_response = before_array + fixed_array_content + after_array
        
        return fixed_response
    
    def _process_suggestions(self, suggestions: Any) -> List[str]:
        """处理suggestions字段，确保返回字符串列表"""
        if not isinstance(suggestions, list):
            # 如果suggestions是字典，转换为列表
            if isinstance(suggestions, dict):
                return list(suggestions.values())
            else:
                return []
        
        # 处理suggestions数组中的各种格式
        processed_suggestions = []
        for item in suggestions:
            if isinstance(item, dict):
                # 如果是字典，提取所有值
                processed_suggestions.extend(item.values())
            elif isinstance(item, str):
                # 如果是字符串，直接添加
                processed_suggestions.append(item)
            else:
                # 其他类型转换为字符串
                processed_suggestions.append(str(item))
        
        return processed_suggestions
    
    def _get_default_value_for_field(self, field: str) -> Any:
        """获取字段的默认值"""
        if field.endswith("_score"):
            return 0.0
        elif field == "suggestions":
            return []
        else:
            return None
    
    def _get_default_evaluation_result(self) -> Dict[str, Any]:
        """获取默认评价结果"""
        result = {"suggestions": ["评价响应处理失败，使用默认评分"]}
        
        # 添加默认评分
        for score_field in self.score_fields:
            result[score_field] = 5.0
        
        return result