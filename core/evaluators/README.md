"""
评估器模块
使用组合模式实现的AI评估器组件库

## 组件架构

本模块采用组合模式而非继承模式，将评估器功能拆分为多个独立的组件：
- LLMCaller: 负责与大语言模型的通信
- ResponseParser: 负责解析AI返回的响应
- PromptBuilder: 负责构建各种评估任务的提示词

## 主要组件

### LLMCaller
负责处理与大语言模型的通信，支持多种模型（GLM、OpenAI、DeepSeek、Anthropic等）。

### ResponseParser
负责解析AI返回的响应，处理JSON格式和错误情况，确保返回结构化的评价结果。

### PromptBuilder
负责构建各种评估任务的提示词，支持历史故事评价和通用评价。

### HistoricalStoryEvaluator
专门用于历史故事评价的评估器，组合了上述组件。

### GenericEvaluator
通用评估器，可以灵活配置各种评估任务。

## 使用示例

### 使用HistoricalStoryEvaluator

```python
from core.evaluators import HistoricalStoryEvaluator

# 创建评估器
evaluator = HistoricalStoryEvaluator(model_name="gpt-4")

# 评价历史故事
story = "这是一个关于唐朝的故事..."
context = {
    "dynasty": "唐朝",
    "context": "这是一个描述唐朝宫廷生活的故事"
}

result = await evaluator.evaluate(story, context)
print(result)
# 输出: {
#     "readability_score": 8.5,
#     "authenticity_score": 7.2,
#     "engagement_score": 9.0,
#     "suggestions": ["建议1", "建议2", "建议3"]
# }
```

### 使用GenericEvaluator

```python
from core.evaluators import GenericEvaluator

# 创建评估器
evaluator = GenericEvaluator(
    model_name="gpt-4",
    required_fields=["clarity_score", "creativity_score", "suggestions"],
    score_fields=["clarity_score", "creativity_score"],
    system_message="你是一位专业的内容评价师。"
)

# 定义评价标准
criteria = {
    "clarity_score": "评估内容的清晰度和易理解程度",
    "creativity_score": "评估内容的创新性和独特性"
}

# 评价内容
content = "这是一段需要评价的内容..."
context = {
    "type": "文章",
    "audience": "普通读者"
}

result = await evaluator.evaluate(content, criteria, context)
print(result)
# 输出: {
#     "clarity_score": 8.0,
#     "creativity_score": 7.5,
#     "suggestions": ["建议1", "建议2", "建议3"]
# }
```

### 直接使用组件

```python
from core.evaluators import LLMCaller, ResponseParser, PromptBuilder

# 创建组件
llm_caller = LLMCaller(model_name="gpt-4")
response_parser = ResponseParser(
    required_fields=["quality_score", "suggestions"],
    score_fields=["quality_score"]
)
prompt_builder = PromptBuilder()

# 构建提示词
prompt = prompt_builder.build_general_evaluation_prompt(
    content="这是要评价的内容",
    criteria={"quality_score": "评估内容质量"},
    context="这是一篇文章"
)

# 调用API
response = await llm_caller.call_llm_api(prompt)

# 解析响应
result = response_parser.parse_evaluation_response(response)
print(result)
```

## 扩展评估器

要创建新的评估器，可以组合现有组件：

```python
from core.evaluators import LLMCaller, ResponseParser, PromptBuilder

class ImageQualityEvaluator:
    """图像质量评估器"""
    
    def __init__(self, model_name="gpt-4"):
        self.llm_caller = LLMCaller(model_name)
        self.response_parser = ResponseParser(
            required_fields=["composition_score", "color_score", "clarity_score", "suggestions"],
            score_fields=["composition_score", "color_score", "clarity_score"]
        )
        self.prompt_builder = PromptBuilder()
    
    async def evaluate(self, image_description, context=None):
        # 构建评价标准
        criteria = {
            "composition_score": "评估图像的构图和布局",
            "color_score": "评估图像的色彩运用",
            "clarity_score": "评估图像的清晰度和细节表现"
        }
        
        # 构建提示词
        prompt = self.prompt_builder.build_general_evaluation_prompt(
            content=image_description,
            criteria=criteria,
            context=context
        )
        
        # 调用API并解析响应
        response = await self.llm_caller.call_llm_api(
            prompt, 
            system_message="你是一位专业的图像评价师。"
        )
        return self.response_parser.parse_evaluation_response(response)
```

## 优势

相比继承模式，组合模式具有以下优势：

1. **低耦合性**：各组件独立，可以单独修改和测试
2. **高灵活性**：可以灵活组合不同组件创建新的评估器
3. **易于扩展**：添加新功能只需创建新组件，无需修改现有代码
4. **代码复用**：组件可以在不同评估器中复用
5. **职责单一**：每个组件只负责一项功能，代码更清晰
"""