"""
历史故事AI评价系统
使用大语言模型API对中文历史故事文本进行专业评价
"""

import logging
from typing import Dict, Any, Optional

from ..llm_caller import LLMCaller
from .response_parser import ResponseParser
from .prompt_builder import PromptBuilder

# 配置日志
logger = logging.getLogger(__name__)

class HistoricalStoryEvaluator:
    """历史故事文本AI评价器"""
    
    def __init__(self, model_name: str = "gpt-4"):
        """
        初始化评价器
        
        Args:
            model_name: 使用的模型名称
        """
        # 使用组合模式，组合各个组件
        self.llm_caller = LLMCaller(model_name)
        self.response_parser = ResponseParser(
            required_fields=["readability_score", "authenticity_score", "engagement_score", "suggestions"],
            score_fields=["readability_score", "authenticity_score", "engagement_score"]
        )
        self.prompt_builder = PromptBuilder()
    
    async def evaluate(self, content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        评价历史故事文本质量
        
        Args:
            content: 历史故事文本
            context: 评价上下文，应包含dynasty和context信息
            
        Returns:
            评价结果字典
        """
        # 获取朝代和上下文信息
        dynasty = context.get("dynasty", "未知") if context else "未知"
        story_context = context.get("context", "") if context else ""
        
        # 构建评价提示词
        prompt = self.prompt_builder.build_historical_story_prompt(content, dynasty, story_context)
        
        # 调用大模型API进行评价
        try:
            response = await self.llm_caller.call_llm_api(
                prompt, 
                system_message="你是一位专业的历史文学评论家。"
            )
            evaluation_result = self.response_parser.parse_evaluation_response(response)
            return evaluation_result
        except Exception as e:
            logger.error(f"AI评价失败: {str(e)}")
            # 返回默认评价结果
            return self._get_default_evaluation_result()
    
    def _get_default_evaluation_result(self) -> Dict[str, Any]:
        """获取默认评价结果"""
        return {
            "readability_score": 5.0,
            "authenticity_score": 5.0,
            "engagement_score": 5.0,
            "suggestions": ["AI评价失败，使用默认评分"]
        }