"""
提示词构建组件
负责构建各种评估任务的提示词
"""

import logging
from typing import Dict, Any, Optional

# 配置日志
logger = logging.getLogger(__name__)

class PromptBuilder:
    """提示词构建组件"""
    
    def __init__(self):
        """初始化提示词构建器"""
        pass
    
    def build_historical_story_prompt(self, story_text: str, dynasty: str, context: str = "") -> str:
        """
        构建历史故事评价提示词
        
        Args:
            story_text: 故事文本
            dynasty: 历史朝代
            context: 故事上下文
            
        Returns:
            评价提示词
        """
        return f"""
你是一位专业的历史文学评论家，请对以下{dynasty}朝代的历史故事文本进行质量评价。

## 历史朝代
{dynasty}

## 故事上下文
{context}

## 故事文本
{story_text}

## 评价要求
请从以下三个维度对文本进行评价，每个维度评分范围为0-10分：

1. **可读性评分 (readability_score)**:
   - 评估文本的清晰度、流畅度和易理解程度
   - 考虑句式结构、段落组织、语言表达的清晰度
   - 评分标准：0分(极难理解) - 10分(非常清晰流畅)

2. **历史真实性评分 (authenticity_score)**:
   - 评估文本是否符合{dynasty}朝代的历史背景、语言风格和文化特点
   - 考虑历史用语、礼仪规范、社会习俗的准确性
   - 评分标准：0分(完全不符合历史) - 10分(高度符合历史)

3. **吸引力评分 (engagement_score)**:
   - 评估文本的趣味性、感染力和吸引力
   - 考虑情节设计、人物塑造、情感表达的生动性
   - 评分标准：0分(毫无吸引力) - 10分(极具吸引力)

## 输出格式
请以JSON格式返回评价结果，包含以下字段：
```json
{{
  "readability_score": 可读性评分,
  "authenticity_score": 历史真实性评分,
  "engagement_score": 吸引力评分,
  "suggestions": [
    "改进建议1",
    "改进建议2",
    "改进建议3"
  ]
}}
```

请确保返回的是有效的JSON格式，不要包含其他解释性文本。
"""
    
    def build_general_evaluation_prompt(self, content: str, criteria: Dict[str, str], context: str = "") -> str:
        """
        构建通用评价提示词
        
        Args:
            content: 要评估的内容
            criteria: 评价标准字典，键为评分字段名，值为评价标准描述
            context: 评估上下文
            
        Returns:
            评价提示词
        """
        criteria_text = ""
        for i, (field, description) in enumerate(criteria.items(), 1):
            criteria_text += f"""
{i}. **{field}**:
   - {description}
   - 评分标准：0分(极差) - 10分(极好)
"""
        
        # 构建JSON输出格式
        json_fields = []
        for field in criteria.keys():
            json_fields.append(f'  "{field}": {field}评分')
        
        json_fields.append('  "suggestions": [')
        json_fields.append('    "改进建议1",')
        json_fields.append('    "改进建议2",')
        json_fields.append('    "改进建议3"')
        json_fields.append('  ]')
        
        json_output = ",\n".join(json_fields)
        
        return f"""
你是一位专业的评论家，请对以下内容进行质量评价。

## 评估上下文
{context}

## 评估内容
{content}

## 评价要求
请从以下维度对文本进行评价，每个维度评分范围为0-10分：
{criteria_text}

## 输出格式
请以JSON格式返回评价结果，包含以下字段：
```json
{{
{json_output}
}}
```

请确保返回的是有效的JSON格式，不要包含其他解释性文本。
"""