"""成本控制模块

负责监控和管理API调用成本，确保在预算范围内运行。
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger

from .config import ConfigManager


class ServiceType(str, Enum):
    """服务类型枚举"""
    TEXT_GENERATION = "text_generation"
    IMAGE_GENERATION = "image_generation"
    VIDEO_GENERATION = "video_generation"
    VOICE_SYNTHESIS = "voice_synthesis"


@dataclass
class CostRecord:
    """成本记录"""
    timestamp: datetime
    service_type: ServiceType
    service_name: str
    operation: str
    cost_usd: float
    tokens_used: Optional[int] = None
    request_id: Optional[str] = None
    metadata: Optional[Dict] = None


@dataclass
class BudgetStatus:
    """预算状态"""
    daily_used: float
    daily_limit: float
    monthly_used: float
    monthly_limit: float
    daily_remaining: float
    monthly_remaining: float
    daily_usage_percent: float
    monthly_usage_percent: float
    is_daily_exceeded: bool
    is_monthly_exceeded: bool
    is_daily_warning: bool
    is_monthly_warning: bool


class CostController:
    """成本控制器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_config()
        self.cost_records: List[CostRecord] = []
        self.cost_file = Path("data/cost_records.json")
        self.cost_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 服务成本配置（美元）
        self.service_costs = {
            # 文本生成成本（每1K tokens）
            "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
            "gpt-4o": {"input": 0.005, "output": 0.015},
            "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
            "claude-3-sonnet": {"input": 0.003, "output": 0.015},
            "qwen2.5-72b-instruct": {"input": 0.0008, "output": 0.002},
            "glm-4-plus": {"input": 0.001, "output": 0.003},
            
            # 图像生成成本（每张图片）
            "flux-pro": 0.055,
            "flux-dev": 0.025,
            "flux-schnell": 0.003,
            "sdxl-lightning": 0.004,
            "dall-e-3": 0.04,
            "midjourney": 0.08,
            
            # 视频生成成本（每秒）
            "kling-pro": 0.125,
            "kling-standard": 0.075,
            "runway-gen3": 0.95,
            "svd": 0.024,
            "pika": 0.35,
            
            # 语音合成成本（每1K字符）
            "elevenlabs-turbo": 0.0003,
            "elevenlabs-multilingual": 0.0011,
            "azure-neural": 0.000016,
            "cosyvoice": 0.0,  # 本地模型，无API成本
        }
        
        self._load_cost_records()
    
    def _load_cost_records(self):
        """加载成本记录"""
        if self.cost_file.exists():
            try:
                with open(self.cost_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.cost_records = [
                        CostRecord(
                            timestamp=datetime.fromisoformat(record['timestamp']),
                            service_type=ServiceType(record['service_type']),
                            service_name=record['service_name'],
                            operation=record['operation'],
                            cost_usd=record['cost_usd'],
                            tokens_used=record.get('tokens_used'),
                            request_id=record.get('request_id'),
                            metadata=record.get('metadata')
                        )
                        for record in data
                    ]
            except Exception as e:
                logger.error(f"加载成本记录失败: {e}")
                self.cost_records = []
    
    def _save_cost_records(self):
        """保存成本记录"""
        try:
            data = []
            for record in self.cost_records:
                record_dict = asdict(record)
                record_dict['timestamp'] = record.timestamp.isoformat()
                data.append(record_dict)
            
            with open(self.cost_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存成本记录失败: {e}")
    
    def estimate_text_cost(self, model: str, input_tokens: int, output_tokens: int) -> float:
        """估算文本生成成本"""
        if model not in self.service_costs:
            return 0.0
        
        costs = self.service_costs[model]
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        return input_cost + output_cost
    
    def estimate_image_cost(self, service: str, count: int = 1) -> float:
        """估算图像生成成本"""
        if service not in self.service_costs:
            return 0.0
        return self.service_costs[service] * count
    
    def estimate_video_cost(self, service: str, duration: float) -> float:
        """估算视频生成成本"""
        if service not in self.service_costs:
            return 0.0
        return self.service_costs[service] * duration
    
    def estimate_voice_cost(self, service: str, character_count: int) -> float:
        """估算语音合成成本"""
        if service not in self.service_costs:
            return 0.0
        return (character_count / 1000) * self.service_costs[service]
    
    async def record_cost(self, cost_usd: float, operation: str,
                         service_type: ServiceType = ServiceType.TEXT_GENERATION,
                         service_name: str = "unknown",
                         tokens_used: Optional[int] = None,
                         request_id: Optional[str] = None,
                         metadata: Optional[Dict] = None):
        """记录成本（异步版本）"""
        logger.info(f"💰 记录成本: ${cost_usd:.4f} - {service_type.value}/{service_name} - {operation}")
        if tokens_used:
            logger.info(f"   使用令牌: {tokens_used}")
        
        record = CostRecord(
            timestamp=datetime.now(),
            service_type=service_type,
            service_name=service_name,
            operation=operation,
            cost_usd=cost_usd,
            tokens_used=tokens_used,
            request_id=request_id,
            metadata=metadata
        )
        
        self.cost_records.append(record)
        self._save_cost_records()
    
    def record_cost_sync(self, 
                   service_type: ServiceType,
                   service_name: str,
                   operation: str,
                   cost_usd: float,
                   tokens_used: Optional[int] = None,
                   request_id: Optional[str] = None,
                   metadata: Optional[Dict] = None):
        """记录成本（同步版本）"""
        logger.info(f"💰 记录成本: ${cost_usd:.4f} - {service_type.value}/{service_name} - {operation}")
        if tokens_used:
            logger.info(f"   使用令牌: {tokens_used}")
        
        record = CostRecord(
            timestamp=datetime.now(),
            service_type=service_type,
            service_name=service_name,
            operation=operation,
            cost_usd=cost_usd,
            tokens_used=tokens_used,
            request_id=request_id,
            metadata=metadata
        )
        
        self.cost_records.append(record)
        self._save_cost_records()
    
    def get_budget_status(self) -> BudgetStatus:
        """获取预算状态"""
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 计算今日和本月使用量
        daily_used = sum(
            record.cost_usd for record in self.cost_records
            if record.timestamp >= today_start
        )
        
        monthly_used = sum(
            record.cost_usd for record in self.cost_records
            if record.timestamp >= month_start
        )
        
        daily_limit = self.config.budget.daily_limit_usd
        monthly_limit = self.config.budget.monthly_limit_usd
        alert_threshold = self.config.budget.alert_threshold
        
        daily_remaining = max(0, daily_limit - daily_used)
        monthly_remaining = max(0, monthly_limit - monthly_used)
        
        daily_usage_percent = daily_used / daily_limit if daily_limit > 0 else 0
        monthly_usage_percent = monthly_used / monthly_limit if monthly_limit > 0 else 0
        
        return BudgetStatus(
            daily_used=daily_used,
            daily_limit=daily_limit,
            monthly_used=monthly_used,
            monthly_limit=monthly_limit,
            daily_remaining=daily_remaining,
            monthly_remaining=monthly_remaining,
            daily_usage_percent=daily_usage_percent,
            monthly_usage_percent=monthly_usage_percent,
            is_daily_exceeded=daily_used >= daily_limit,
            is_monthly_exceeded=monthly_used >= monthly_limit,
            is_daily_warning=daily_usage_percent >= alert_threshold,
            is_monthly_warning=monthly_usage_percent >= alert_threshold
        )
    
    async def check_budget(self, estimated_cost: float) -> bool:
        """检查预算是否充足（异步版本）"""
        can_afford, reason = self.can_afford(estimated_cost)
        if not can_afford:
            logger.warning(f"⚠️ 预算检查失败: {reason} (需要: ${estimated_cost:.4f})")
        return can_afford
    
    def can_afford(self, estimated_cost: float) -> Tuple[bool, str]:
        """检查是否可以承担指定成本"""
        status = self.get_budget_status()
        
        if status.is_monthly_exceeded:
            logger.error(f"❌ 月度预算已超支: ${status.monthly_used:.4f}/${status.monthly_limit:.2f}")
            return False, "Monthly budget exceeded"
        
        if status.is_daily_exceeded:
            logger.error(f"❌ 日度预算已超支: ${status.daily_used:.4f}/${status.daily_limit:.2f}")
            return False, "Daily budget exceeded"
        
        if estimated_cost > status.daily_remaining:
            logger.warning(f"⚠️ 日度预算不足: 需要 ${estimated_cost:.4f}, 剩余 ${status.daily_remaining:.4f}")
            return False, f"Insufficient daily budget (need ${estimated_cost:.4f}, have ${status.daily_remaining:.4f})"
        
        if estimated_cost > status.monthly_remaining:
            logger.warning(f"⚠️ 月度预算不足: 需要 ${estimated_cost:.4f}, 剩余 ${status.monthly_remaining:.4f}")
            return False, f"Insufficient monthly budget (need ${estimated_cost:.4f}, have ${status.monthly_remaining:.4f})"
        
        logger.info(f"✅ 预算检查通过: 需要 ${estimated_cost:.4f}")
        return True, "OK"
    
    def get_cost_breakdown(self, days: int = 30) -> Dict[str, Dict[str, float]]:
        """获取成本分解"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_records = [
            record for record in self.cost_records
            if record.timestamp >= cutoff_date
        ]
        
        breakdown = {
            "by_service_type": {},
            "by_service_name": {},
            "by_date": {}
        }
        
        for record in recent_records:
            # 按服务类型分组
            service_type = record.service_type.value
            if service_type not in breakdown["by_service_type"]:
                breakdown["by_service_type"][service_type] = 0
            breakdown["by_service_type"][service_type] += record.cost_usd
            
            # 按服务名称分组
            service_name = record.service_name
            if service_name not in breakdown["by_service_name"]:
                breakdown["by_service_name"][service_name] = 0
            breakdown["by_service_name"][service_name] += record.cost_usd
            
            # 按日期分组
            date_str = record.timestamp.strftime("%Y-%m-%d")
            if date_str not in breakdown["by_date"]:
                breakdown["by_date"][date_str] = 0
            breakdown["by_date"][date_str] += record.cost_usd
        
        return breakdown
    
    def get_usage_statistics(self) -> Dict[str, any]:
        """获取使用统计"""
        status = self.get_budget_status()
        breakdown = self.get_cost_breakdown()
        
        total_requests = len(self.cost_records)
        total_cost = sum(record.cost_usd for record in self.cost_records)
        avg_cost_per_request = total_cost / total_requests if total_requests > 0 else 0
        
        return {
            "budget_status": asdict(status),
            "cost_breakdown": breakdown,
            "total_requests": total_requests,
            "total_cost_usd": total_cost,
            "average_cost_per_request": avg_cost_per_request,
            "most_expensive_service": max(
                breakdown["by_service_name"].items(),
                key=lambda x: x[1],
                default=("None", 0)
            )[0] if breakdown["by_service_name"] else "None"
        }
    
    def optimize_service_selection(self, service_type: ServiceType, requirements: Dict) -> List[str]:
        """根据成本优化服务选择"""
        status = self.get_budget_status()
        
        # 从配置文件获取模型推荐设置
        recommendations_config = getattr(self.config, 'model_recommendations', None)
        
        if service_type == ServiceType.TEXT_GENERATION:
            # 根据预算选择文本生成模型
            if status.daily_remaining > 1.0:  # 充足预算
                return recommendations_config.text_generation_high_budget if recommendations_config else ["gpt-4o", "claude-3-sonnet", "gpt-4o-mini"]
            elif status.daily_remaining > 0.1:  # 中等预算
                return recommendations_config.text_generation_medium_budget if recommendations_config else ["gpt-4o-mini", "claude-3-haiku", "qwen2.5-72b-instruct"]
            else:  # 紧张预算
                return recommendations_config.text_generation_low_budget if recommendations_config else ["qwen2.5-72b-instruct", "glm-4-plus"]
        
        elif service_type == ServiceType.IMAGE_GENERATION:
            if status.daily_remaining > 0.5:
                return recommendations_config.image_generation_high_budget if recommendations_config else ["flux-pro", "dall-e-3", "flux-dev"]
            elif status.daily_remaining > 0.1:
                return recommendations_config.image_generation_medium_budget if recommendations_config else ["flux-dev", "sdxl-lightning", "flux-schnell"]
            else:
                return recommendations_config.image_generation_low_budget if recommendations_config else ["flux-schnell", "sdxl-lightning"]
        
        elif service_type == ServiceType.VIDEO_GENERATION:
            if status.daily_remaining > 2.0:
                return recommendations_config.video_generation_high_budget if recommendations_config else ["runway-gen3", "kling-pro"]
            elif status.daily_remaining > 0.5:
                return recommendations_config.video_generation_medium_budget if recommendations_config else ["kling-pro", "kling-standard", "pika"]
            else:
                return recommendations_config.video_generation_low_budget if recommendations_config else ["kling-standard", "svd"]
        
        elif service_type == ServiceType.VOICE_SYNTHESIS:
            if status.daily_remaining > 0.1:
                return recommendations_config.voice_synthesis_high_budget if recommendations_config else ["elevenlabs-multilingual", "elevenlabs-turbo", "azure-neural"]
            else:
                return recommendations_config.voice_synthesis_low_budget if recommendations_config else ["cosyvoice", "azure-neural"]
        
        return []
    
    def generate_cost_report(self) -> str:
        """生成成本报告"""
        stats = self.get_usage_statistics()
        status = stats["budget_status"]
        
        report = f"""
# 成本控制报告

## 预算状态
- 日预算使用: ${status['daily_used']:.4f} / ${status['daily_limit']:.2f} ({status['daily_usage_percent']:.1%})
- 月预算使用: ${status['monthly_used']:.4f} / ${status['monthly_limit']:.2f} ({status['monthly_usage_percent']:.1%})
- 日剩余预算: ${status['daily_remaining']:.4f}
- 月剩余预算: ${status['monthly_remaining']:.4f}

## 使用统计
- 总请求数: {stats['total_requests']}
- 总成本: ${stats['total_cost_usd']:.4f}
- 平均每请求成本: ${stats['average_cost_per_request']:.6f}
- 最昂贵服务: {stats['most_expensive_service']}

## 成本分解（按服务类型）
"""
        
        for service_type, cost in stats["cost_breakdown"]["by_service_type"].items():
            report += f"- {service_type}: ${cost:.4f}\n"
        
        report += "\n## 成本分解（按服务名称）\n"
        for service_name, cost in sorted(
            stats["cost_breakdown"]["by_service_name"].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]:  # 显示前10个最昂贵的服务
            report += f"- {service_name}: ${cost:.4f}\n"
        
        return report