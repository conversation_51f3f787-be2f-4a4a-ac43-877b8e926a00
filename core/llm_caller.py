"""
LLM API调用组件
负责处理与大语言模型的通信
"""

import logging
import json
from typing import Dict, Any, Optional

# 配置日志
logger = logging.getLogger(__name__)

class LLMCaller:
    """LLM API调用组件"""
    
    def __init__(self, model_name: str = "gpt-4"):
        """
        初始化LLM调用器
        
        Args:
            model_name: 使用的模型名称
        """
        self.model_name = model_name
    
    async def call_llm_api(self, prompt: str, system_message: str = None) -> str:
        """
        调用大模型API
        
        Args:
            prompt: 提示词
            system_message: 系统消息
            
        Returns:
            模型响应
        """
        try:
            logger.info(f"🔄 AI评价API调用开始...")
            logger.info(f"   模型: {self.model_name}")
            
            # 获取API配置
            api_config = self._get_api_config()
            
            # 设置系统消息
            if system_message:
                api_config["system_message"] = system_message
            
            # 统一使用litellm调用API
            response = await self._call_model_api(prompt, api_config)
            
            logger.info(f"✅ AI评价API调用完成")
            return response
                
        except Exception as e:
            logger.error(f"AI评价API调用失败: {str(e)}")
            raise
    
    def _get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        from .config import config_manager
        
        # 根据模型名称确定服务类型
        if self.model_name.startswith('glm-'):
            service = "glm"
        elif self.model_name.startswith('deepseek-'):
            service = "deepseek"
        elif self.model_name.startswith('claude-'):
            service = "anthropic"
        else:
            service = "openai"
        
        # 获取API密钥
        api_key = config_manager.get_api_key(service)
        if not api_key:
            raise ValueError(f"{service.upper()}_API_KEY未设置")
        
        return {
            "service": service,
            "api_key": api_key,
            "model": self.model_name,
            "temperature": 0.7,
            "max_tokens": 2000,
            "system_message": "你是一位专业的评论家。"
        }
    
    async def _call_model_api(self, prompt: str, api_config: Dict[str, Any]) -> str:
        """使用litellm统一调用各种模型API"""
        from litellm import acompletion
        
        # 准备调用参数
        model_name = api_config["model"]
        
        # 转换模型名称为litellm支持的格式
        if model_name.startswith('glm-'):
            model_name = f"openai/{model_name}"  # litellm使用openai/前缀表示智谱AI模型
        
        completion_params = {
            "model": model_name,
            "messages": [
                {"role": "system", "content": api_config["system_message"]},
                {"role": "user", "content": prompt}
            ],
            "temperature": api_config["temperature"],
            "max_tokens": api_config["max_tokens"],
            "api_key": api_config["api_key"]
        }
        
        # 对于GLM模型，需要设置API基础URL
        if api_config["model"].startswith('glm-'):
            completion_params["api_base"] = "https://open.bigmodel.cn/api/paas/v4/"
        
        # 调用API
        response = await acompletion(**completion_params)
        content = response.choices[0].message.content
        
        # 检查返回内容是否为空
        if not content:
            logger.warning("LLM API 返回了空内容")
            return ""
        
        return content