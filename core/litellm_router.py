"""LiteLLM路由管理器

提供多模型路由、负载均衡和成本优化功能。
"""

import os
import yaml
import asyncio
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timedelta

try:
    import litellm
    from litellm import Router
    LITELLM_AVAILABLE = True
except ImportError:
    LITELLM_AVAILABLE = False
    logging.warning("LiteLLM not available. Install with: pip install litellm")

from .config import ConfigManager
from .cost_control import CostController


class RoutingStrategy(Enum):
    """路由策略枚举"""
    COST_BASED = "cost_based"
    LATENCY_BASED = "latency_based"
    ROUND_ROBIN = "round_robin"
    WEIGHTED = "weighted"
    QUALITY_BASED = "quality_based"


@dataclass
class ModelGroup:
    """模型组配置"""
    name: str
    primary_models: List[str]
    fallback_models: List[str]
    max_cost_per_request: float
    strategy: RoutingStrategy = RoutingStrategy.COST_BASED


@dataclass
class RouteRequest:
    """路由请求"""
    messages: List[Dict[str, str]]
    model_group: str = "quick_tasks"
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    stream: bool = False
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class RouteResponse:
    """路由响应"""
    content: str
    model_used: str
    cost: float
    latency: float
    tokens_used: int
    metadata: Dict[str, Any]


class LiteLLMRouter:
    """LiteLLM路由管理器"""
    
    def __init__(self, config_manager: ConfigManager, cost_controller: CostController):
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config_path = os.path.join(
            config_manager.get_config_dir(),
            "litellm_config.yaml"
        )
        self.config = self._load_config()
        
        # 初始化路由器
        self.router = None
        self.model_groups = {}
        self._initialize_router()
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "total_cost": 0.0,
            "model_usage": {},
            "error_count": 0,
            "last_reset": datetime.now()
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载LiteLLM配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            self.logger.error(f"LiteLLM config file not found: {self.config_path}")
            return self._get_default_config()
        except Exception as e:
            self.logger.error(f"Failed to load LiteLLM config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        # 从配置文件获取文本生成设置
        text_config = getattr(self.config_manager.app_config, 'text_generation', None)
        
        if text_config:
            primary_model = text_config.primary_model
            fallback_model = getattr(text_config, 'fallback_model', primary_model)
            self.logger.info(f"Using configured models for LiteLLM: primary={primary_model}, fallback={fallback_model}")
        else:
            primary_model = "gpt-4o-mini"  # 默认模型
            fallback_model = "gpt-4o-mini"
            self.logger.warning("No text generation config found, using default models")
        
        return {
            "model_list": [
                {
                    "model_name": primary_model,
                    "litellm_params": {
                        "model": primary_model,
                        "api_key": self._get_api_key_for_model(primary_model)
                    },
                    "model_info": {
                        "mode": "chat",
                        "input_cost_per_token": self._get_model_cost(primary_model, "input"),
                        "output_cost_per_token": self._get_model_cost(primary_model, "output")
                    }
                }
            ],
            "router_settings": {
                "routing_strategy": "cost_based",
                "fallback": {"enabled": True, "fallback_models": [fallback_model]}
            },
            "model_groups": {
                "quick_tasks": {
                    "primary_models": [primary_model],
                    "fallback_models": [fallback_model],
                    "max_cost_per_request": 0.01
                }
            }
        }
    
    def _get_api_key_for_model(self, model_name: str) -> str:
        """根据模型名称获取对应的API密钥"""
        model_name_lower = model_name.lower()
        
        if "gpt" in model_name_lower or "openai" in model_name_lower:
            return self.config_manager.get_api_key("openai") or ""
        elif "claude" in model_name_lower:
            return self.config_manager.get_api_key("anthropic") or ""
        elif "glm" in model_name_lower:
            return self.config_manager.get_api_key("glm") or ""
        elif "deepseek" in model_name_lower:
            return self.config_manager.get_api_key("deepseek") or ""
        elif "qwen" in model_name_lower:
            return self.config_manager.get_api_key("dashscope") or ""
        else:
            return self.config_manager.get_api_key("openai") or ""  # 默认使用OpenAI
    
    def _get_model_cost(self, model_name: str, cost_type: str) -> float:
        """获取模型成本信息"""
        # 从成本控制器获取模型成本信息
        model_costs = {
            "gpt-4o-mini": {"input": 0.00000015, "output": 0.0000006},
            "gpt-4o": {"input": 0.000005, "output": 0.000015},
            "claude-3-haiku": {"input": 0.00000025, "output": 0.00000125},
            "claude-3-sonnet": {"input": 0.000003, "output": 0.000015},
            "glm-4-flash": {"input": 0.0000001, "output": 0.0000001},
            "deepseek-chat": {"input": 0.00000014, "output": 0.00000028}
        }
        
        return model_costs.get(model_name, {}).get(cost_type, 0.00000015)  # 默认成本
    
    def _initialize_router(self):
        """初始化LiteLLM路由器"""
        if not LITELLM_AVAILABLE:
            self.logger.warning("LiteLLM not available, using fallback mode")
            return
        
        try:
            # 设置LiteLLM配置（保持现有的调试设置）
            # litellm.set_verbose = False  # 注释掉，避免覆盖调试设置
            
            # 创建路由器
            model_list = self.config.get("model_list", [])
            if model_list:
                self.router = Router(
                    model_list=model_list,
                    routing_strategy=self.config.get("router_settings", {}).get("routing_strategy", "simple-shuffle"),
                    set_verbose=False
                )
                self.logger.info(f"LiteLLM router initialized with {len(model_list)} models")
            
            # 加载模型组
            self._load_model_groups()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LiteLLM router: {e}")
            self.router = None
    
    def _load_model_groups(self):
        """加载模型组配置"""
        groups_config = self.config.get("model_groups", {})
        
        for group_name, group_config in groups_config.items():
            self.model_groups[group_name] = ModelGroup(
                name=group_name,
                primary_models=group_config.get("primary_models", []),
                fallback_models=group_config.get("fallback_models", []),
                max_cost_per_request=group_config.get("max_cost_per_request", 0.10),
                strategy=RoutingStrategy(group_config.get("strategy", "cost_based"))
            )
        
        self.logger.info(f"Loaded {len(self.model_groups)} model groups")
    
    async def route_request(self, request: RouteRequest) -> RouteResponse:
        """路由请求到最适合的模型"""
        start_time = datetime.now()
        
        try:
            # 获取模型组
            model_group = self.model_groups.get(request.model_group)
            if not model_group:
                raise ValueError(f"Unknown model group: {request.model_group}")
            
            # 检查成本限制
            estimated_cost = await self._estimate_request_cost(request, model_group)
            if estimated_cost > model_group.max_cost_per_request:
                raise ValueError(f"Estimated cost {estimated_cost} exceeds limit {model_group.max_cost_per_request}")
            
            # 选择模型
            selected_model = await self._select_model(request, model_group)
            
            # 执行请求
            response = await self._execute_request(request, selected_model)
            
            # 计算实际成本和延迟
            end_time = datetime.now()
            latency = (end_time - start_time).total_seconds()
            actual_cost = self._calculate_actual_cost(response, selected_model)
            
            # 更新统计信息
            self._update_stats(selected_model, actual_cost, latency)
            
            # 记录成本
            await self.cost_controller.record_cost(
                service="litellm",
                cost=actual_cost,
                metadata={
                    "model": selected_model,
                    "model_group": request.model_group,
                    "tokens": response.get("usage", {}).get("total_tokens", 0)
                }
            )
            
            return RouteResponse(
                content=response["choices"][0]["message"]["content"],
                model_used=selected_model,
                cost=actual_cost,
                latency=latency,
                tokens_used=response.get("usage", {}).get("total_tokens", 0),
                metadata={
                    "model_group": request.model_group,
                    "estimated_cost": estimated_cost,
                    "usage": response.get("usage", {})
                }
            )
            
        except Exception as e:
            self.logger.error(f"Route request failed: {e}")
            self.stats["error_count"] += 1
            
            # 尝试使用fallback模型
            if model_group and model_group.fallback_models:
                try:
                    fallback_model = model_group.fallback_models[0]
                    response = await self._execute_request(request, fallback_model)
                    
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()
                    actual_cost = self._calculate_actual_cost(response, fallback_model)
                    
                    self._update_stats(fallback_model, actual_cost, latency)
                    
                    return RouteResponse(
                        content=response["choices"][0]["message"]["content"],
                        model_used=fallback_model,
                        cost=actual_cost,
                        latency=latency,
                        tokens_used=response.get("usage", {}).get("total_tokens", 0),
                        metadata={"fallback": True, "original_error": str(e)}
                    )
                except Exception as fallback_error:
                    self.logger.error(f"Fallback also failed: {fallback_error}")
            
            raise e
    
    async def _select_model(self, request: RouteRequest, model_group: ModelGroup) -> str:
        """选择最适合的模型"""
        available_models = model_group.primary_models
        
        if model_group.strategy == RoutingStrategy.COST_BASED:
            # 按成本排序，选择最便宜的可用模型
            model_costs = []
            for model in available_models:
                cost = await self._estimate_model_cost(request, model)
                model_costs.append((model, cost))
            
            model_costs.sort(key=lambda x: x[1])
            return model_costs[0][0] if model_costs else available_models[0]
        
        elif model_group.strategy == RoutingStrategy.ROUND_ROBIN:
            # 轮询选择
            index = self.stats["total_requests"] % len(available_models)
            return available_models[index]
        
        elif model_group.strategy == RoutingStrategy.WEIGHTED:
            # 加权选择
            weights = self.config.get("router_settings", {}).get("load_balancing", {}).get("weights", {})
            weighted_models = [(model, weights.get(model, 1.0)) for model in available_models]
            
            # 简单的加权随机选择
            import random
            total_weight = sum(weight for _, weight in weighted_models)
            r = random.uniform(0, total_weight)
            
            cumulative = 0
            for model, weight in weighted_models:
                cumulative += weight
                if r <= cumulative:
                    return model
            
            return available_models[0]
        
        else:
            # 默认选择第一个模型
            return available_models[0]
    
    async def _execute_request(self, request: RouteRequest, model: str) -> Dict[str, Any]:
        """执行模型请求"""
        if self.router and LITELLM_AVAILABLE:
            # 使用LiteLLM路由器
            response = await self.router.acompletion(
                model=model,
                messages=request.messages,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                stream=request.stream
            )
            return response
        else:
            # Fallback模式 - 直接使用litellm
            if LITELLM_AVAILABLE:
                response = await litellm.acompletion(
                    model=model,
                    messages=request.messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
                return response
            else:
                # 模拟响应（用于测试）
                return {
                    "choices": [{
                        "message": {
                            "content": "This is a simulated response (LiteLLM not available)"
                        }
                    }],
                    "usage": {
                        "total_tokens": 50,
                        "prompt_tokens": 30,
                        "completion_tokens": 20
                    }
                }
    
    async def _estimate_request_cost(self, request: RouteRequest, model_group: ModelGroup) -> float:
        """估算请求成本"""
        # 估算token数量
        estimated_tokens = self._estimate_tokens(request.messages)
        if request.max_tokens:
            estimated_tokens += request.max_tokens
        else:
            estimated_tokens += 500  # 默认输出token估算
        
        # 使用组内最便宜的模型进行估算
        min_cost = float('inf')
        for model in model_group.primary_models:
            cost = await self._estimate_model_cost(request, model)
            min_cost = min(min_cost, cost)
        
        return min_cost if min_cost != float('inf') else 0.01
    
    async def _estimate_model_cost(self, request: RouteRequest, model: str) -> float:
        """估算特定模型的成本"""
        # 查找模型定价信息
        model_info = None
        for model_config in self.config.get("model_list", []):
            if model_config.get("model_name") == model:
                model_info = model_config.get("model_info", {})
                break
        
        if not model_info:
            return 0.01  # 默认成本
        
        # 估算token数量
        input_tokens = self._estimate_tokens(request.messages)
        output_tokens = request.max_tokens or 500
        
        # 计算成本
        input_cost = input_tokens * model_info.get("input_cost_per_token", 0.000001)
        output_cost = output_tokens * model_info.get("output_cost_per_token", 0.000002)
        
        return input_cost + output_cost
    
    def _estimate_tokens(self, messages: List[Dict[str, str]]) -> int:
        """估算消息的token数量"""
        total_chars = sum(len(msg.get("content", "")) for msg in messages)
        # 粗略估算：4个字符约等于1个token
        return max(total_chars // 4, 10)
    
    def _calculate_actual_cost(self, response: Dict[str, Any], model: str) -> float:
        """计算实际成本"""
        usage = response.get("usage", {})
        prompt_tokens = usage.get("prompt_tokens", 0)
        completion_tokens = usage.get("completion_tokens", 0)
        
        # 查找模型定价
        model_info = None
        for model_config in self.config.get("model_list", []):
            if model_config.get("model_name") == model:
                model_info = model_config.get("model_info", {})
                break
        
        if not model_info:
            return 0.01  # 默认成本
        
        input_cost = prompt_tokens * model_info.get("input_cost_per_token", 0.000001)
        output_cost = completion_tokens * model_info.get("output_cost_per_token", 0.000002)
        
        return input_cost + output_cost
    
    def _update_stats(self, model: str, cost: float, latency: float):
        """更新统计信息"""
        self.stats["total_requests"] += 1
        self.stats["total_cost"] += cost
        
        if model not in self.stats["model_usage"]:
            self.stats["model_usage"][model] = {
                "count": 0,
                "total_cost": 0.0,
                "avg_latency": 0.0
            }
        
        model_stats = self.stats["model_usage"][model]
        model_stats["count"] += 1
        model_stats["total_cost"] += cost
        
        # 更新平均延迟
        old_avg = model_stats["avg_latency"]
        count = model_stats["count"]
        model_stats["avg_latency"] = (old_avg * (count - 1) + latency) / count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_requests": 0,
            "total_cost": 0.0,
            "model_usage": {},
            "error_count": 0,
            "last_reset": datetime.now()
        }
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return [model["model_name"] for model in self.config.get("model_list", [])]
    
    def get_model_groups(self) -> Dict[str, ModelGroup]:
        """获取模型组配置"""
        return self.model_groups.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        status = {
            "litellm_available": LITELLM_AVAILABLE,
            "router_initialized": self.router is not None,
            "config_loaded": bool(self.config),
            "model_groups_count": len(self.model_groups),
            "available_models_count": len(self.get_available_models()),
            "stats": self.get_stats()
        }
        
        # 测试一个简单的请求
        try:
            test_request = RouteRequest(
                messages=[{"role": "user", "content": "Hello"}],
                model_group="quick_tasks",
                max_tokens=10
            )
            
            test_response = await self.route_request(test_request)
            status["test_request_success"] = True
            status["test_response_model"] = test_response.model_used
            
        except Exception as e:
            status["test_request_success"] = False
            status["test_error"] = str(e)
        
        return status