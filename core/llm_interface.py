"""
LLM调用接口
使用组合模式定义统一的LLM调用接口，使不同的LLM调用实现可以互换使用
"""

from typing import Dict, Any, List, Optional, Protocol, runtime_checkable
import logging

logger = logging.getLogger(__name__)


@runtime_checkable
class LLMProviderProtocol(Protocol):
    """LLM提供者协议，定义统一的接口"""
    
    async def call_llm(
        self, 
        prompt: str, 
        system_message: str = None, 
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        **kwargs
    ) -> str:
        """
        调用大模型API
        
        Args:
            prompt: 提示词
            system_message: 系统消息
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        ...
    
    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表
        
        Returns:
            可用模型名称列表
        """
        ...
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取提供者信息
        
        Returns:
            提供者信息字典
        """
        ...


class LLMRequest:
    """LLM请求封装类"""
    
    def __init__(
        self,
        prompt: str,
        system_message: str = None,
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        **kwargs
    ):
        """
        初始化LLM请求
        
        Args:
            prompt: 提示词
            system_message: 系统消息
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
        """
        self.prompt = prompt
        self.system_message = system_message
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.kwargs = kwargs


class LLMResponse:
    """LLM响应封装类"""
    
    def __init__(
        self,
        content: str,
        model_used: str,
        cost: float = 0.0,
        latency: float = 0.0,
        tokens_used: int = 0,
        metadata: Dict[str, Any] = None
    ):
        """
        初始化LLM响应
        
        Args:
            content: 响应内容
            model_used: 使用的模型
            cost: 成本
            latency: 延迟（秒）
            tokens_used: 使用的token数
            metadata: 元数据
        """
        self.content = content
        self.model_used = model_used
        self.cost = cost
        self.latency = latency
        self.tokens_used = tokens_used
        self.metadata = metadata or {}





class LLMServiceManager:
    """LLM服务管理器"""
    
    def __init__(self, config_manager):
        """
        初始化LLM服务管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self._provider: Optional[LLMProviderProtocol] = None
        self.logger = logging.getLogger(__name__)
    
    def initialize_provider(self, provider_type: str = None, **kwargs) -> LLMProviderProtocol:
        """
        初始化LLM提供者
        
        Args:
            provider_type: 提供者类型，如果为None则从配置中读取
            **kwargs: 其他参数
            
        Returns:
            LLM提供者实例
        """
        if provider_type is None:
            # 从配置中获取提供者类型
            text_config = getattr(self.config_manager.app_config, 'text_generation', None)
            if text_config and hasattr(text_config, 'llm_caller_type'):
                provider_type = text_config.llm_caller_type
            else:
                provider_type = "caller"  # 默认使用caller
        
        self.logger.info(f"Initializing LLM provider: {provider_type}")
        
        # 创建提供者实例
        from .llm_provider_registry import LLMProviderRegistry
        self._provider = LLMProviderRegistry.create_provider(provider_type, **kwargs)
        
        return self._provider
    
    async def call_llm(self, request: LLMRequest) -> LLMResponse:
        """
        调用LLM
        
        Args:
            request: LLM请求
            
        Returns:
            LLM响应
        """
        if not self._provider:
            self.initialize_provider()
        
        try:
            self.logger.info(f"Calling LLM with provider: {self._provider.__class__.__name__}")
            
            # 调用提供者
            content = await self._provider.call_llm(
                prompt=request.prompt,
                system_message=request.system_message,
                model=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                **request.kwargs
            )
            
            # 创建响应
            response = LLMResponse(
                content=content,
                model_used=request.model or "default",
                metadata={"provider": self._provider.__class__.__name__}
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"LLM call failed: {e}")
            raise
    
    def get_current_provider_info(self) -> Dict[str, Any]:
        """获取当前提供者信息"""
        if not self._provider:
            return {"status": "not_initialized"}
        
        return self._provider.get_provider_info()
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        if not self._provider:
            return []
        
        return self._provider.get_available_models()
    
    def switch_provider(self, provider_type: str, **kwargs) -> LLMProviderProtocol:
        """
        切换提供者
        
        Args:
            provider_type: 新的提供者类型
            **kwargs: 其他参数
            
        Returns:
            新的提供者实例
        """
        self.logger.info(f"Switching LLM provider from {self._provider.__class__.__name__ if self._provider else 'None'} to {provider_type}")
        return self.initialize_provider(provider_type, **kwargs)