"""工作流管理器

提供高级接口来管理整个历史短剧制作流程，包括：
- 项目创建和管理
- 工作流执行控制
- 进度监控和状态管理
- 错误处理和恢复
- 资源管理和优化
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict

from .workflow import WorkflowEngine, WorkflowExecution, WorkflowStage
from .models import ScriptData, TaskStatus, ProjectData, CharacterData, SceneData
from .config import ConfigManager
from .cost_control import CostController


@dataclass
class ProjectConfig:
    """项目配置"""
    project_id: str
    name: str
    description: str
    theme: str
    genre: str
    historical_period: str
    target_duration: float  # 秒
    character_count: int
    scene_count: int
    style_requirements: Dict[str, Any]
    constraints: List[str]
    output_format: str = "mp4"
    quality_level: str = "high"
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class ProjectStatus:
    """项目状态"""
    project_id: str
    execution_id: Optional[str]
    status: TaskStatus
    current_stage: Optional[WorkflowStage]
    progress_percentage: float
    completed_steps: List[str]
    failed_steps: List[str]
    total_cost: float
    estimated_remaining_cost: float
    start_time: Optional[datetime]
    estimated_completion_time: Optional[datetime]
    error_message: Optional[str]
    last_updated: datetime


class WorkflowManager:
    """工作流管理器"""
    
    def __init__(self, config_manager: ConfigManager, cost_controller: CostController):
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.workflow_engine = WorkflowEngine(config_manager, cost_controller)
        
        # 项目管理
        self.projects: Dict[str, ProjectConfig] = {}
        self.project_executions: Dict[str, str] = {}  # project_id -> execution_id
        
        # 状态监控
        self.status_callbacks: List[Callable[[ProjectStatus], None]] = []
        self.progress_callbacks: List[Callable[[str, float], None]] = []
        
        # 输出目录
        self.output_dir = Path(config_manager.get_config().get('output', {}).get('directory', 'output'))
        self.output_dir.mkdir(exist_ok=True)
    
    def create_project(self, config: ProjectConfig) -> str:
        """创建新项目"""
        self.projects[config.project_id] = config
        
        # 创建项目目录
        project_dir = self.output_dir / config.project_id
        project_dir.mkdir(exist_ok=True)
        
        # 保存项目配置
        config_path = project_dir / 'project_config.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(config), f, ensure_ascii=False, indent=2, default=str)
        
        return config.project_id
    
    def load_project(self, project_id: str) -> Optional[ProjectConfig]:
        """加载项目配置"""
        if project_id in self.projects:
            return self.projects[project_id]
        
        # 从文件加载
        project_dir = self.output_dir / project_id
        config_path = project_dir / 'project_config.json'
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                config = ProjectConfig(**data)
                self.projects[project_id] = config
                return config
        
        return None
    
    def list_projects(self) -> List[ProjectConfig]:
        """列出所有项目"""
        # 加载磁盘上的项目
        for project_dir in self.output_dir.iterdir():
            if project_dir.is_dir():
                project_id = project_dir.name
                if project_id not in self.projects:
                    self.load_project(project_id)
        
        return list(self.projects.values())
    
    async def start_production(self, project_id: str) -> str:
        """开始制作流程"""
        project_config = self.load_project(project_id)
        if not project_config:
            raise ValueError(f"项目 {project_id} 不存在")
        
        # 检查是否已有执行中的任务
        if project_id in self.project_executions:
            execution_id = self.project_executions[project_id]
            execution = self.workflow_engine.get_execution_status(execution_id)
            if execution and execution.status == TaskStatus.RUNNING:
                raise ValueError(f"项目 {project_id} 已在执行中")
        
        # 创建脚本数据
        script_data = self._create_script_data(project_config)
        
        # 启动工作流
        execution = await self.workflow_engine.execute_workflow(script_data)
        self.project_executions[project_id] = execution.execution_id
        
        # 启动状态监控
        asyncio.create_task(self._monitor_execution(project_id, execution.execution_id))
        
        return execution.execution_id
    
    def _create_script_data(self, config: ProjectConfig) -> ScriptData:
        """根据项目配置创建脚本数据"""
        return ScriptData(
            script_id=f"{config.project_id}_script",
            title=config.name,
            synopsis=config.description,
            theme=config.theme,
            genre=config.genre,
            duration=config.target_duration,
            characters=[],  # 将由OutlineChain生成
            scenes=[],      # 将由OutlineChain生成
            dialogues=[],   # 将由DialogueChain生成
            media_cues=[],  # 将在后续步骤中生成
            historical_period=config.historical_period,
            style_requirements=config.style_requirements,
            constraints=config.constraints
        )
    
    async def _monitor_execution(self, project_id: str, execution_id: str):
        """监控执行状态"""
        while True:
            execution = self.workflow_engine.get_execution_status(execution_id)
            if not execution:
                break
            
            # 计算进度
            total_steps = len(self.workflow_engine.workflow_steps)
            completed_steps = len(execution.completed_steps)
            progress = (completed_steps / total_steps) * 100 if total_steps > 0 else 0
            
            # 创建状态对象
            status = ProjectStatus(
                project_id=project_id,
                execution_id=execution_id,
                status=execution.status,
                current_stage=execution.current_stage,
                progress_percentage=progress,
                completed_steps=execution.completed_steps,
                failed_steps=execution.failed_steps,
                total_cost=execution.total_cost,
                estimated_remaining_cost=self._estimate_remaining_cost(execution),
                start_time=execution.start_time,
                estimated_completion_time=self._estimate_completion_time(execution),
                error_message=execution.error_message,
                last_updated=datetime.now()
            )
            
            # 调用回调函数
            for callback in self.status_callbacks:
                try:
                    callback(status)
                except Exception as e:
                    logging.getLogger(__name__).error(f"状态回调错误: {e}")
            
            for callback in self.progress_callbacks:
                try:
                    callback(project_id, progress)
                except Exception as e:
                    logging.getLogger(__name__).error(f"进度回调错误: {e}")
            
            # 保存状态到文件
            await self._save_execution_status(project_id, status)
            
            # 检查是否完成
            if execution.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                break
            
            # 等待下次检查
            await asyncio.sleep(5)
    
    def _estimate_remaining_cost(self, execution: WorkflowExecution) -> float:
        """估算剩余成本"""
        remaining_steps = [
            step for step_id, step in self.workflow_engine.workflow_steps.items()
            if step_id not in execution.completed_steps
        ]
        return sum(step.estimated_cost for step in remaining_steps)
    
    def _estimate_completion_time(self, execution: WorkflowExecution) -> Optional[datetime]:
        """估算完成时间"""
        if not execution.start_time:
            return None
        
        remaining_steps = [
            step for step_id, step in self.workflow_engine.workflow_steps.items()
            if step_id not in execution.completed_steps
        ]
        
        remaining_minutes = sum(step.estimated_duration for step in remaining_steps)
        
        from datetime import timedelta
        return datetime.now() + timedelta(minutes=remaining_minutes)
    
    async def _save_execution_status(self, project_id: str, status: ProjectStatus):
        """保存执行状态到文件"""
        project_dir = self.output_dir / project_id
        status_path = project_dir / 'execution_status.json'
        
        with open(status_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(status), f, ensure_ascii=False, indent=2, default=str)
    
    def get_project_status(self, project_id: str) -> Optional[ProjectStatus]:
        """获取项目状态"""
        # 从内存获取
        if project_id in self.project_executions:
            execution_id = self.project_executions[project_id]
            execution = self.workflow_engine.get_execution_status(execution_id)
            if execution:
                total_steps = len(self.workflow_engine.workflow_steps)
                completed_steps = len(execution.completed_steps)
                progress = (completed_steps / total_steps) * 100 if total_steps > 0 else 0
                
                return ProjectStatus(
                    project_id=project_id,
                    execution_id=execution_id,
                    status=execution.status,
                    current_stage=execution.current_stage,
                    progress_percentage=progress,
                    completed_steps=execution.completed_steps,
                    failed_steps=execution.failed_steps,
                    total_cost=execution.total_cost,
                    estimated_remaining_cost=self._estimate_remaining_cost(execution),
                    start_time=execution.start_time,
                    estimated_completion_time=self._estimate_completion_time(execution),
                    error_message=execution.error_message,
                    last_updated=datetime.now()
                )
        
        # 从文件加载
        project_dir = self.output_dir / project_id
        status_path = project_dir / 'execution_status.json'
        
        if status_path.exists():
            with open(status_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return ProjectStatus(**data)
        
        return None
    
    def cancel_production(self, project_id: str) -> bool:
        """取消制作流程"""
        if project_id in self.project_executions:
            execution_id = self.project_executions[project_id]
            return self.workflow_engine.cancel_execution(execution_id)
        return False
    
    def pause_production(self, project_id: str) -> bool:
        """暂停制作流程（预留接口）"""
        # TODO: 实现暂停功能
        return False
    
    def resume_production(self, project_id: str) -> bool:
        """恢复制作流程（预留接口）"""
        # TODO: 实现恢复功能
        return False
    
    def add_status_callback(self, callback: Callable[[ProjectStatus], None]):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
    
    def add_progress_callback(self, callback: Callable[[str, float], None]):
        """添加进度变化回调"""
        self.progress_callbacks.append(callback)
    
    def get_production_statistics(self) -> Dict[str, Any]:
        """获取制作统计信息"""
        workflow_stats = self.workflow_engine.get_workflow_statistics()
        
        project_stats = {
            "total_projects": len(self.projects),
            "active_projects": len(self.project_executions),
            "projects_by_status": self._get_projects_by_status()
        }
        
        return {
            "workflow_statistics": workflow_stats,
            "project_statistics": project_stats,
            "system_info": {
                "output_directory": str(self.output_dir),
                "available_steps": list(self.workflow_engine.workflow_steps.keys())
            }
        }
    
    def _get_projects_by_status(self) -> Dict[str, int]:
        """按状态统计项目数量"""
        status_counts = {
            "completed": 0,
            "running": 0,
            "failed": 0,
            "cancelled": 0,
            "pending": 0
        }
        
        for project_id in self.projects:
            status = self.get_project_status(project_id)
            if status:
                if status.status == TaskStatus.COMPLETED:
                    status_counts["completed"] += 1
                elif status.status == TaskStatus.RUNNING:
                    status_counts["running"] += 1
                elif status.status == TaskStatus.FAILED:
                    status_counts["failed"] += 1
                elif status.status == TaskStatus.CANCELLED:
                    status_counts["cancelled"] += 1
                else:
                    status_counts["pending"] += 1
            else:
                status_counts["pending"] += 1
        
        return status_counts
    
    def export_project_report(self, project_id: str) -> Dict[str, Any]:
        """导出项目报告"""
        project_config = self.load_project(project_id)
        project_status = self.get_project_status(project_id)
        
        if not project_config:
            raise ValueError(f"项目 {project_id} 不存在")
        
        report = {
            "project_info": asdict(project_config),
            "execution_status": asdict(project_status) if project_status else None,
            "generated_at": datetime.now().isoformat()
        }
        
        # 添加执行详情
        if project_status and project_status.execution_id:
            execution = self.workflow_engine.get_execution_status(project_status.execution_id)
            if execution:
                report["execution_details"] = {
                    "step_results": execution.step_results,
                    "completed_steps": execution.completed_steps,
                    "failed_steps": execution.failed_steps,
                    "total_cost": execution.total_cost
                }
        
        return report
    
    def cleanup_completed_projects(self, days_old: int = 30):
        """清理完成的旧项目"""
        from datetime import timedelta
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        for project_id, config in list(self.projects.items()):
            if config.created_at < cutoff_date:
                status = self.get_project_status(project_id)
                if status and status.status == TaskStatus.COMPLETED:
                    # 可以选择删除或归档
                    print(f"项目 {project_id} 可以清理（创建于 {config.created_at}）")