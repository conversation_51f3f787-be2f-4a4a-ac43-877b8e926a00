#!/usr/bin/env python3
"""
日志使用示例

演示如何在producer项目中使用loguru日志系统
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from infra.logger import get_logger, PerformanceLogger
from core.chains.outline_chain import OutlineChain
from core.chains.scene_chain import SceneChain
from core.chains.dialogue_chain import DialogueChain
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import (
    ProjectData, OutlineData, SceneData, CharacterData,
    OutlineRequest, SceneRequest, DialogueRequest
)


async def main():
    """主函数 - 演示日志使用"""
    # 获取日志记录器
    logger = get_logger("demo")
    
    # 记录应用启动
    logger.info("🚀 启动日志演示应用")
    
    # 初始化配置和成本控制
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    # 使用性能日志记录器
    perf_logger = PerformanceLogger()
    
    # 创建示例项目数据
    project = ProjectData(
        project_id="demo_project",
        title="日志演示项目",
        description="这是一个演示loguru日志系统的示例项目",
        genre="历史",
        dynasty="明朝",
        target_duration=300,  # 5分钟
        estimated_cost=1.0
    )
    
    logger.info(f"📋 创建项目: {project.title} (ID: {project.project_id})")
    
    # 示例1: 大纲生成
    logger.info("📝 示例1: 大纲生成")
    with perf_logger.measure("outline_generation"):
        outline_chain = OutlineChain(config_manager, cost_controller)
        
        outline_request = OutlineRequest(
            project_data=project,
            style_requirements={
                "narrative_style": "史诗风格",
                "pacing": "中等节奏",
                "tone": "庄重"
            },
            constraints=[
                "必须包含3个主要场景",
                "总时长控制在5分钟内"
            ]
        )
        
        try:
            outline = await outline_chain.generate_outline(outline_request)
            logger.info(f"✅ 大纲生成完成，场景数: {len(outline.scenes)}")
        except Exception as e:
            logger.error(f"❌ 大纲生成失败: {str(e)}")
    
    # 示例2: 场景生成
    logger.info("🎬 示例2: 场景生成")
    with perf_logger.measure("scene_generation"):
        scene_chain = SceneChain(config_manager, cost_controller)
        
        # 创建示例场景数据
        scene_data = SceneData(
            scene_id="demo_scene",
            title="演示场景",
            description="这是一个演示场景生成的示例",
            duration=60,  # 1分钟
            location="紫禁城",
            time_period="明朝",
            characters=["皇帝", "大臣"]
        )
        
        scene_request = SceneRequest(
            scene_data=scene_data,
            characters=[],
            visual_style="写实风格",
            audio_requirements={
                "background_music": "古典音乐",
                "sound_effects": "宫廷音效"
            },
            constraints=[
                "镜头数量不超过5个",
                "包含特写镜头"
            ]
        )
        
        try:
            scene = await scene_chain.generate_scene(scene_request)
            logger.info(f"✅ 场景生成完成，镜头数: {len(scene.shots)}")
        except Exception as e:
            logger.error(f"❌ 场景生成失败: {str(e)}")
    
    # 示例3: 对白生成
    logger.info("💬 示例3: 对白生成")
    with perf_logger.measure("dialogue_generation"):
        dialogue_chain = DialogueChain(config_manager, cost_controller)
        
        # 创建示例角色数据
        characters = [
            CharacterData(
                character_id="emperor",
                name="皇帝",
                description="明朝皇帝，威严而睿智",
                personality_traits=["威严", "睿智", "果断"],
                background="明朝皇帝，统治多年，经验丰富"
            ),
            CharacterData(
                character_id="minister",
                name="大臣",
                description="忠诚的大臣，学识渊博",
                personality_traits=["忠诚", "学识渊博", "谨慎"],
                background="朝廷重臣，为皇帝出谋划策"
            )
        ]
        
        dialogue_request = DialogueRequest(
            scene_data=scene_data,
            characters=characters,
            plot_context="皇帝与大臣讨论国家大事",
            emotional_context="庄重而严肃",
            dynasty="明朝",
            duration=30,  # 30秒
            style_requirements={
                "language_style": "古文风格",
                "formality_level": "高"
            },
            constraints=[
                "使用明朝时期的语言风格",
                "对白要体现人物身份和性格"
            ]
        )
        
        try:
            dialogue = await dialogue_chain.generate_dialogue(dialogue_request)
            logger.info(f"✅ 对白生成完成，对白行数: {len(dialogue.dialogue_lines)}")
        except Exception as e:
            logger.error(f"❌ 对白生成失败: {str(e)}")
    
    # 输出性能统计
    perf_stats = perf_logger.get_statistics()
    logger.info("📊 性能统计:")
    for operation, stats in perf_stats.items():
        logger.info(f"   {operation}: 平均 {stats['avg_time']:.2f}秒, "
                   f"最小 {stats['min_time']:.2f}秒, "
                   f"最大 {stats['max_time']:.2f}秒")
    
    # 记录应用结束
    logger.info("🏁 日志演示应用结束")


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())