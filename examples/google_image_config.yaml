# Google图像生成适配器配置示例

# 环境变量配置 (.env)
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here  # 二选一即可

# producer配置文件更新 (config/config.yaml)
adapters:
  image:
    # Google图像生成 - 推荐配置
    google:
      service_name: "google_image"
      model: "gemini-2.0-flash"  # 免费模型
      enabled: true
      api_url: "https://generativelanguage.googleapis.com/v1beta"
      custom_params:
        default_size: "1024x1024"
        max_images_per_request: 4
        timeout: 60
        retry_attempts: 3
        
    # 高质量付费选项
    google_premium:
      service_name: "google_image"
      model: "imagen-4.0-fast"  # 高质量付费模型
      enabled: false  # 按需启用
      api_url: "https://generativelanguage.googleapis.com/v1beta"
      custom_params:
        default_size: "2048x2048"
        max_images_per_request: 4
        
  # 回退方案
  image_fallback:
    flux:
      service_name: "flux_image"
      model: "flux-schnell"
      enabled: true
      # ... flux配置

# 成本控制配置
cost_control:
  image_generation:
    daily_budget: 5.0  # 如果使用付费模型
    preferred_provider: "google"  # 优先Google
    fallback_providers: ["flux", "sdxl"]
    
  model_costs:
    google_image:
      "gemini-2.0-flash": 0.0      # 完全免费
      "imagen-4.0-fast": 0.02      # $0.02/图片
      "imagen-4.0-standard": 0.04  # $0.04/图片
      "imagen-4.0-ultra": 0.06     # $0.06/图片
      "imagen-3.0": 0.03           # $0.03/图片

# 工作流配置更新
workflows:
  script_generation:
    steps:
      - name: "outline_generation"
        adapter: "text/deepseek"
        
      - name: "scene_generation" 
        adapter: "text/deepseek"
        
      - name: "image_generation"
        adapter: "image/google"  # 使用Google图像生成
        config:
          model: "gemini-2.0-flash"
          num_images_per_scene: 2
          style: "realistic"
          aspect_ratio: "16:9"
          
      - name: "dialogue_generation"
        adapter: "text/deepseek"

# 媒体生成配置
media_generation:
  image:
    default_provider: "google"
    providers:
      google:
        models:
          free: "gemini-2.0-flash"
          premium: "imagen-4.0-fast"
        settings:
          default_resolution: "1024x1024"
          batch_size: 4
          style_presets:
            historical: "ancient Chinese historical scene, realistic, detailed"
            character: "Chinese historical figure portrait, traditional clothing"
            landscape: "Chinese landscape painting style, mountains and rivers"
            
  # 智能路由规则
  routing:
    # 免费优先策略
    cost_optimized:
      image: 
        primary: "google/gemini-2.0-flash"
        fallback: "flux/flux-schnell"
        
    # 质量优先策略  
    quality_first:
      image:
        primary: "google/imagen-4.0-ultra"
        fallback: "google/imagen-4.0-fast"
        
    # 平衡策略
    balanced:
      image:
        primary: "google/gemini-2.0-flash"  # 日常使用免费
        premium: "google/imagen-4.0-fast"   # 重要场景付费
        fallback: "flux/flux-schnell"       # 本地备用

# 批量处理配置
batch_processing:
  image_generation:
    provider: "google"
    model: "gemini-2.0-flash"
    concurrent_requests: 5  # 并发请求数
    rate_limit:
      requests_per_minute: 60
      images_per_hour: 1000  # 免费层级限制
    retry_policy:
      max_attempts: 3
      backoff_factor: 2
      
# 输出配置
output:
  images:
    formats: ["png", "jpeg"]
    quality: 95
    save_metadata: true
    directory_structure: "outputs/images/{date}/{scene_id}/"
    naming_pattern: "{model}_{scene_id}_{index}.{format}"
    
  # 自动备份配置
  backup:
    enabled: true
    providers: ["local", "cloud"]
    retention_days: 30