#!/usr/bin/env python3
"""
文本生成测试运行脚本

简化的运行入口，专门用于测试文本生成功能。
避免复杂的图像、视频生成，快速验证文本链路。

使用方法:
    python run_text_test.py
    
或者:
    uv run run_text_test.py
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置专用配置文件
os.environ['PRODUCER_CONFIG_FILE'] = str(project_root / 'config' / 'text_test_config.yaml')

from tests.test_text_chains import main as run_text_test


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查关键环境变量
    glm_key = os.getenv('GLM_API_KEY')
    dashscope_key = os.getenv('DASHSCOPE_API_KEY')
    google_key = os.getenv('GOOGLE_AI_API_KEY')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    available_apis = []
    if glm_key:
        available_apis.append('GLM')
    if dashscope_key:
        available_apis.append('DASHSCOPE')
    if google_key:
        available_apis.append('GOOGLE_AI')
    if openai_key:
        available_apis.append('OPENAI')
    
    if not available_apis:
        print("❌ 没有找到任何可用的API密钥！")
        print("")
        print("请设置以下任一API密钥:")
        print("  - GLM_API_KEY (智谱AI)")
        print("  - DASHSCOPE_API_KEY (阿里云通义千问)")
        print("  - GOOGLE_AI_API_KEY (Google AI)")
        print("  - OPENAI_API_KEY (OpenAI)")
        print("")
        print("设置方法:")
        print("  export GLM_API_KEY=your-api-key")
        print("  或者创建 .env 文件并添加相应配置")
        return False
    else:
        print(f"✅ 找到可用的API: {', '.join(available_apis)}")
    
    print("✅ 环境配置检查通过")
    return True


def print_usage_info():
    """打印使用说明"""
    print("\n" + "=" * 60)
    print("📖 文本生成测试说明")
    print("=" * 60)
    print("本测试专门验证文本生成功能，包括:")
    print("  1. 📝 大纲生成 - 根据主题生成剧本大纲")
    print("  2. 🎬 场景生成 - 将大纲扩展为详细场景")
    print("  3. 💬 对话生成 - 为场景生成角色对话")
    print("\n优势:")
    print("  ✅ 测试速度快（避免图像/视频生成）")
    print("  ✅ 成本低（仅文本生成）")
    print("  ✅ 易于调试（专注文本链路）")
    print("  ✅ 结果可读（直接查看文本输出）")
    print("\n测试结果将保存在: output/text_test_results/")
    print("=" * 60)


def main():
    """主函数"""
    print("🚀 文本生成链路测试启动器")
    print("专注测试文本生成功能，避免复杂的多媒体处理")
    
    # 打印使用说明
    print_usage_info()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请配置必要的API密钥后重试")
        return False
    
    print("\n🎯 开始执行文本生成测试...")
    
    try:
        # 运行测试
        result = asyncio.run(run_text_test())
        
        if result:
            print("\n🎉 文本生成测试全部通过！")
            print("\n📋 下一步建议:")
            print("  1. 查看 output/text_test_results/ 中的测试结果")
            print("  2. 如果文本生成正常，可以继续测试图像生成")
            print("  3. 使用 test_workflow.py 进行完整流程测试")
        else:
            print("\n⚠️  部分测试失败，请检查日志和错误信息")
            print("\n🔧 调试建议:")
            print("  1. 检查API密钥是否正确")
            print("  2. 确认网络连接正常")
            print("  3. 查看详细错误日志")
        
        return result
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {str(e)}")
        print("\n🔧 请检查:")
        print("  1. 项目依赖是否正确安装")
        print("  2. 配置文件是否存在")
        print("  3. API密钥是否有效")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)