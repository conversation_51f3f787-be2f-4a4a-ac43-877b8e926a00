from setuptools import setup

setup(
    name="producer",
    version="0.1.0",
    packages=[
        "producer",
        "producer.core",
        "producer.adapters",
        "producer.adapters.text",
        "producer.adapters.image", 
        "producer.adapters.voice",
        "producer.adapters.video",
        "producer.tests",
        "producer.tests.adapters",
    ],
    package_dir={
        "producer": ".",
        "producer.core": "core",
        "producer.adapters": "adapters",
        "producer.adapters.text": "adapters/text",
        "producer.adapters.image": "adapters/image",
        "producer.adapters.voice": "adapters/voice",
        "producer.adapters.video": "adapters/video",
        "producer.tests": "tests",
        "producer.tests.adapters": "tests/adapters",
    },
    include_package_data=True,
    python_requires=">=3.10",
)