# Producer项目日志系统使用指南

## 概述

Producer项目使用loguru作为日志系统，提供了结构化、高性能的日志记录功能。本指南将介绍如何在项目中使用日志系统。

## 功能特点

- **结构化日志输出**: 支持JSON格式和彩色控制台输出
- **日志轮转和归档**: 自动按大小和时间轮转日志文件
- **异常捕获和堆栈跟踪**: 自动捕获异常并记录详细堆栈信息
- **性能监控**: 提供性能日志记录器，监控关键操作耗时
- **多环境配置**: 支持开发、测试和生产环境的不同日志级别
- **上下文信息**: 自动记录请求ID、用户ID等上下文信息

## 快速开始

### 1. 导入日志记录器

```python
from infra.logger import get_logger, PerformanceLogger

# 获取普通日志记录器
logger = get_logger(__name__)

# 获取性能日志记录器
perf_logger = PerformanceLogger()
```

### 2. 基本日志记录

```python
# 记录不同级别的日志
logger.debug("这是调试信息")
logger.info("这是一般信息")
logger.warning("这是警告信息")
logger.error("这是错误信息")
logger.critical("这是严重错误信息")
```

### 3. 结构化日志记录

```python
# 记录结构化信息
logger.info("用户登录", {
    "user_id": "12345",
    "username": "johndoe",
    "ip_address": "***********"
})

# 使用extra参数添加上下文
logger.info("处理请求", extra={
    "request_id": "req_123456",
    "user_id": "user_789012"
})
```

### 4. 性能监控

```python
# 使用上下文管理器监控操作耗时
with perf_logger.measure("operation_name"):
    # 执行需要监控的代码
    result = some_operation()

# 手动记录性能数据
perf_logger.record("operation_name", 1.23)  # 操作名称，耗时（秒）
```

### 5. 异常处理

```python
try:
    # 可能抛出异常的代码
    result = risky_operation()
except Exception as e:
    # 自动记录异常和堆栈跟踪
    logger.exception("操作失败", extra={"operation": "risky_operation"})
```

## 日志级别

loguru支持以下日志级别（从低到高）：

1. `TRACE` (5): 最详细的日志信息
2. `DEBUG` (10): 调试信息
3. `INFO` (20): 一般信息
4. `SUCCESS` (25): 成功信息
5. `WARNING` (30): 警告信息
6. `ERROR` (40): 错误信息
7. `CRITICAL` (50): 严重错误信息

## 日志配置

日志系统在`infra/logger.py`中配置，支持以下配置项：

- **日志级别**: 控制记录的日志最低级别
- **输出格式**: 控制日志输出格式
- **日志轮转**: 控制日志文件轮转策略
- **过滤规则**: 控制哪些日志需要记录

### 环境变量配置

可以通过环境变量配置日志系统：

```bash
# 设置日志级别
export LOG_LEVEL=INFO

# 设置日志文件路径
export LOG_FILE_PATH=logs/app.log

# 设置日志轮转大小
export LOG_ROTATION_SIZE=10MB

# 设置日志保留时间
export LOG_RETENTION=30 days
```

## 最佳实践

### 1. 使用适当的日志级别

- **DEBUG**: 详细的调试信息，仅在开发环境使用
- **INFO**: 一般信息，如应用启动、操作完成等
- **WARNING**: 警告信息，如潜在问题、非最佳实践等
- **ERROR**: 错误信息，如操作失败、异常等
- **CRITICAL**: 严重错误，如应用无法继续运行等

### 2. 记录结构化信息

```python
# 好的做法
logger.info("用户登录成功", {
    "user_id": user.id,
    "username": user.username,
    "login_method": "password"
})

# 不好的做法
logger.info(f"用户 {user.username} (ID: {user.id}) 使用 {login_method} 登录成功")
```

### 3. 使用上下文信息

```python
# 在请求处理开始时设置上下文
logger.contextualize(request_id=request.id, user_id=request.user.id)

# 在后续日志中自动包含上下文信息
logger.info("处理请求")  # 自动包含request_id和user_id
```

### 4. 避免敏感信息

```python
# 好的做法
logger.info("用户登录", {
    "user_id": user.id,
    "username": user.username
    # 不记录密码等敏感信息
})

# 不好的做法
logger.info("用户登录", {
    "user_id": user.id,
    "username": user.username,
    "password": user.password  # 敏感信息！
})
```

### 5. 使用性能监控

```python
# 监控关键操作
with perf_logger.measure("database_query"):
    result = db.query("SELECT * FROM users")

# 监控API调用
with perf_logger.measure("external_api_call"):
    response = requests.get("https://api.example.com/data")
```

## 示例代码

请参考`logger_example.py`文件，它包含了使用日志系统的完整示例。

## 故障排除

### 1. 日志文件未创建

检查日志文件目录是否存在，以及应用是否有写入权限：

```bash
# 创建日志目录
mkdir -p logs

# 检查权限
ls -la logs/
```

### 2. 日志级别不生效

确保环境变量设置正确，或者检查代码中的日志级别配置：

```python
# 检查当前日志级别
logger.level
```

### 3. 性能问题

如果日志记录影响性能，可以考虑以下优化：

1. 降低日志级别
2. 减少日志记录频率
3. 使用异步日志记录
4. 优化日志格式

## 更多资源

- [loguru官方文档](https://loguru.readthedocs.io/)
- [Python日志最佳实践](https://docs.python.org/3/howto/logging.html)
- [结构化日志最佳实践](https://www.elastic.co/guide/en/ecs/current/index.html)