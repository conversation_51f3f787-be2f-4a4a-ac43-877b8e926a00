# Producer 系统配置文件详细说明

## 🤔 为什么以前没有配置也能用？

### 📋 **答案：硬编码默认值机制**

在 `GoogleImageAdapter` 的代码中有这样的逻辑：

```python
# 设置默认模型（优先免费模型）
if not self.model_name or self.model_name not in self.SUPPORTED_MODELS:
    self.model_name = "gemini-2.0-flash-exp"  # 使用实验版本
```

**也就是说：**
1. ✅ **以前能用**：因为代码中硬编码了默认模型 `gemini-2.0-flash-exp`
2. ✅ **现在更好**：通过配置文件可以灵活控制模型选择、参数设置
3. ✅ **向后兼容**：即使没有配置文件，仍会使用默认值

## 🎯 配置文件的核心价值

### 1. **灵活性** - 无需修改代码就能切换模型
```yaml
# 可以轻松切换不同模型
google:
  model: "gemini-2.0-flash-exp"      # 免费实验版
  # model: "imagen-4.0-standard"     # 高质量付费版
```

### 2. **成本控制** - 精确控制每个环节的开销
```yaml
google:
  model: "gemini-2.0-flash-exp"      # 完全免费
  num_images: 1                      # 控制生成数量
  aspect_ratio: "1:1"                # 避免高分辨率额外费用
```

### 3. **环境适配** - 开发/测试/生产环境不同配置
```yaml
# 开发环境：使用免费模型
google:
  model: "gemini-2.0-flash-exp"

# 生产环境：使用高质量模型
google:
  model: "imagen-4.0-ultra"
```

---

# 📚 配置文件完整说明文档

## 🏗️ 系统架构配置

### `system` - 系统基础配置
```yaml
system:
  name: "历史短剧视频制作系统"        # 系统名称
  version: "1.0.0"                     # 版本号
  mode: "hybrid_optimized"             # 运行模式
  debug: false                         # 调试模式
  log_level: "INFO"                    # 日志级别
```

**运行模式说明：**
- `hybrid_optimized`: 混合优化模式（本地+云端）
- `zero_cost`: 零成本模式（仅使用免费服务）
- `performance`: 性能优先模式（使用最佳服务）

**日志级别：**
- `DEBUG`: 详细调试信息
- `INFO`: 一般信息（推荐）
- `WARNING`: 警告信息
- `ERROR`: 仅错误信息

---

## 📁 输出配置

### `output` - 文件输出设置
```yaml
output:
  base_dir: "./output"                 # 主输出目录
  temp_dir: "./temp"                   # 临时文件目录
  video_format: "mp4"                  # 视频格式
  audio_format: "wav"                  # 音频格式
  image_format: "png"                  # 图像格式
  cleanup_temp: true                   # 是否清理临时文件
```

**支持的格式：**
- **视频**: mp4, avi, mov, mkv
- **音频**: wav, mp3, aac, flac
- **图像**: png, jpg, jpeg, webp

---

## 💰 预算控制配置

### `budget` - 成本控制设置
```yaml
budget:
  daily_limit_usd: 10.0               # 日消费限额（美元）
  monthly_limit_usd: 100.0            # 月消费限额（美元）
  cost_tracking: true                 # 是否启用成本追踪
  alert_threshold: 0.8                # 警告阈值（80%时提醒）
```

**成本控制机制：**
- 🔴 **硬限制**: 达到限额自动停止服务
- 🟡 **软提醒**: 达到阈值发送警告
- 📊 **实时监控**: 每次API调用记录成本
- 📈 **统计报表**: 生成日/月成本报告

---

## 📝 文本生成配置

### `text_generation` - 文本AI配置
```yaml
text_generation:
  primary_model: "glm-4-flash"        # 主力模型
  fallback_model: "deepseek-chat"     # 备用模型
  max_tokens: 4000                    # 最大输出长度
  temperature: 0.7                    # 创意度（0-1）
  timeout: 30                         # 超时时间（秒）
```

**模型选择策略：**
```yaml
# 💰 成本优先方案
primary_model: "glm-4-flash"          # ¥0.1/1M tokens
fallback_model: "deepseek-chat"       # ¥1.9/1M tokens

# 🎯 质量优先方案  
primary_model: "claude-3.5-sonnet"    # 创意写作最佳
fallback_model: "gpt-4o"              # 通用能力强

# ⚡ 速度优先方案
primary_model: "gpt-4o-mini"          # 响应快
fallback_model: "glm-4-flash"         # 备用快速
```

**参数说明：**
- `temperature`: 0=严格按逻辑，1=最大创意
- `max_tokens`: 控制输出长度，影响成本
- `timeout`: 防止长时间等待

---

## 🎨 图像生成配置

### `image_generation` - 图像AI配置
```yaml
image_generation:
  primary_service: "google"            # 主服务
  fallback_service: "flux"             # 备用服务
  resolution: "1024x1024"              # 默认分辨率
  quality: "high"                      # 质量设置
  
  # Google Imagen 专项配置
  google:
    model: "gemini-2.0-flash-exp"      # 模型选择
    aspect_ratio: "1:1"                # 宽高比
    num_images: 1                      # 生成数量
    person_generation: "allow_adult"   # 人物生成策略
```

### 🔍 **Google 模型详细对比**

| 模型名称 | 价格 | 分辨率 | 质量 | 速度 | 推荐用途 |
|----------|------|--------|------|------|----------|
| `gemini-2.0-flash-exp` | 🆓 免费 | 1024×1024 | ⭐⭐⭐⭐ | ⚡⚡⚡ | 日常开发、测试 |
| `gemini-2.0-flash` | 🆓 免费 | 1024×1024 | ⭐⭐⭐⭐ | ⚡⚡⚡ | 生产环境 |
| `imagen-4.0-fast` | $0.02/张 | 2048×2048 | ⭐⭐⭐⭐⭐ | ⚡⚡ | 快速原型 |
| `imagen-4.0-standard` | $0.04/张 | 2048×2048 | ⭐⭐⭐⭐⭐ | ⚡ | 标准质量需求 |
| `imagen-4.0-ultra` | $0.06/张 | 2048×2048 | ⭐⭐⭐⭐⭐ | 🐌 | 最高质量要求 |

### 🎯 **配置推荐方案**

#### 方案1：🆓 完全免费（推荐）
```yaml
google:
  model: "gemini-2.0-flash-exp"       # Google免费模型
  num_images: 1                       # 减少数量
  aspect_ratio: "1:1"                 # 标准比例
```

#### 方案2：⚡ 快速原型
```yaml
google:
  model: "imagen-4.0-fast"            # 快速付费模型
  num_images: 2                       # 多选择性
  aspect_ratio: "16:9"                # 视频比例
```

#### 方案3：🏆 商业品质
```yaml
google:
  model: "imagen-4.0-ultra"           # 最高质量
  num_images: 4                       # 最大数量
  aspect_ratio: "3:4"                 # 竖屏比例
```

### 🔧 **高级配置选项**

```yaml
google:
  # 基础配置
  model: "gemini-2.0-flash-exp"
  
  # 图像参数
  aspect_ratio: "1:1"                 # 1:1, 3:4, 4:3, 9:16, 16:9
  num_images: 1                       # 1-4张
  
  # 内容策略
  person_generation: "allow_adult"    # 人物生成策略
  # 选项：
  # - "dont_allow": 禁止生成人物
  # - "allow_adult": 仅生成成人
  # - "allow_all": 生成所有人物
  
  # 高级参数
  safety_settings:
    hate_speech: "BLOCK_MEDIUM_AND_ABOVE"
    harassment: "BLOCK_MEDIUM_AND_ABOVE"
    
  generation_config:
    max_retry_attempts: 3
    timeout_seconds: 60
```

---

## 🎬 视频生成配置

### `video_generation` - 视频AI配置
```yaml
video_generation:
  primary_service: "kling"             # 可灵（推荐）
  fallback_service: "runway"           # RunwayML
  resolution: "1080p"                  # 分辨率
  fps: 24                              # 帧率
  duration: 5                          # 默认时长（秒）
```

**分辨率选项：**
- `720p`: 1280×720（快速）
- `1080p`: 1920×1080（标准）
- `4K`: 3840×2160（高质量）

---

## 🔊 语音合成配置

### `voice_synthesis` - 语音AI配置
```yaml
voice_synthesis:
  primary_service: "edge_tts"          # 免费服务
  fallback_service: "cosyvoice"        # 高质量备用
  language: "zh-CN"                    # 语言
  voice_name: "zh-CN-XiaoxiaoNeural"   # 音色
  speed: 1.0                           # 语速
  pitch: 0                             # 音调
```

**推荐配置策略：**
```yaml
# 🆓 零成本方案
primary_service: "edge_tts"            # 微软免费TTS
voice_name: "zh-CN-XiaoxiaoNeural"     # 中文女声

# 🎭 高质量方案
primary_service: "elevenlabs"          # 顶级质量
voice_name: "Rachel"                   # 英文女声

# 🏠 本地化方案
primary_service: "cosyvoice"           # 阿里开源
model_path: "./models/cosyvoice"       # 本地模型
```

---

## ⚡ 性能与并发配置

### `performance` - 性能优化设置
```yaml
performance:
  max_concurrent_tasks: 3              # 最大并发任务数
  batch_size: 5                        # 批处理大小
  cache_enabled: true                  # 启用缓存
  cache_ttl: 3600                      # 缓存过期时间（秒）
  
  # 内存管理
  memory_limit_mb: 2048                # 内存限制
  temp_cleanup_interval: 300           # 临时文件清理间隔
  
  # 网络配置
  request_timeout: 30                  # 请求超时
  max_retries: 3                       # 最大重试次数
  retry_delay: 1.0                     # 重试延迟
```

---

## 🔌 扩展配置

### `extensions` - 扩展功能设置
```yaml
extensions:
  # 插件配置
  plugins:
    enabled: true
    auto_load: true
    plugin_dir: "./plugins"
    
  # 工作流扩展
  workflows:
    custom_steps: true
    step_timeout: 300
    parallel_execution: true
    
  # API扩展
  api_server:
    enabled: false
    host: "0.0.0.0"
    port: 8080
    cors_enabled: true
```

---

## 📊 监控与日志配置

### `monitoring` - 监控配置
```yaml
monitoring:
  # 日志配置
  logging:
    level: "INFO"
    file: "./logs/producer.log"
    rotation: "daily"
    retention: 30                      # 保留天数
    
  # 指标收集
  metrics:
    enabled: true
    export_interval: 60                # 导出间隔（秒）
    include_system_metrics: true
    
  # 告警配置
  alerts:
    cost_threshold: 0.8                # 成本告警阈值
    error_rate_threshold: 0.05         # 错误率告警阈值
    webhook_url: ""                    # 告警回调地址
```

---

## 🔐 安全与权限配置

### `security` - 安全设置
```yaml
security:
  # API密钥管理
  api_keys:
    encryption: true                   # 密钥加密
    rotation_enabled: false            # 自动轮换
    
  # 内容安全
  content_filter:
    enabled: true                      # 启用内容过滤
    strict_mode: false                 # 严格模式
    custom_rules: []                   # 自定义规则
    
  # 访问控制
  access_control:
    rate_limiting: true                # 限流
    ip_whitelist: []                   # IP白名单
    user_authentication: false        # 用户认证
```

---

## 🎯 场景化配置模板

### 📋 **模板1：个人开发者（免费为主）**
```yaml
# 个人开发者配置 - 零成本方案
system:
  mode: "zero_cost"

budget:
  daily_limit_usd: 1.0                 # 严格成本控制
  monthly_limit_usd: 10.0

text_generation:
  primary_model: "glm-4-flash"         # 免费额度
  fallback_model: "deepseek-chat"      # 低成本

image_generation:
  primary_service: "google"
  google:
    model: "gemini-2.0-flash-exp"      # 完全免费

voice_synthesis:
  primary_service: "edge_tts"          # 完全免费
```

### 📋 **模板2：小团队（成本平衡）**
```yaml
# 小团队配置 - 成本效益平衡
system:
  mode: "hybrid_optimized"

budget:
  daily_limit_usd: 10.0
  monthly_limit_usd: 200.0

text_generation:
  primary_model: "deepseek-chat"       # 高性价比
  fallback_model: "claude-3.5-sonnet" # 质量保障

image_generation:
  primary_service: "google"
  google:
    model: "imagen-4.0-fast"           # 付费但便宜
```

### 📋 **模板3：商业项目（质量优先）**
```yaml
# 商业项目配置 - 质量优先
system:
  mode: "performance"

budget:
  daily_limit_usd: 100.0
  monthly_limit_usd: 3000.0

text_generation:
  primary_model: "claude-3.5-sonnet"   # 顶级创意
  fallback_model: "gpt-4o"             # 通用优秀

image_generation:
  primary_service: "google"
  google:
    model: "imagen-4.0-ultra"          # 最高质量
    num_images: 4                      # 更多选择

voice_synthesis:
  primary_service: "elevenlabs"        # 专业音质
```

---

## 🔧 配置文件管理最佳实践

### 1. **环境分离**
```bash
# 不同环境使用不同配置
config/
├── config.yaml              # 基础配置
├── config.dev.yaml          # 开发环境
├── config.test.yaml         # 测试环境
└── config.prod.yaml         # 生产环境
```

### 2. **版本控制**
```bash
# 配置文件版本管理
git add config/config.yaml
git commit -m "feat: 新增Google Imagen配置"
```

### 3. **配置验证**
```bash
# 验证配置文件有效性
uv run python -c "
import yaml
with open('config/config.yaml') as f:
    config = yaml.safe_load(f)
    print('✅ 配置文件格式正确')
"
```

### 4. **配置备份**
```bash
# 定期备份配置文件
cp config/config.yaml config/config.yaml.backup.$(date +%Y%m%d)
```

---

## 📋 常见问题与解决方案

### ❓ **Q: 为什么要使用配置文件而不是硬编码？**
**A:** 
- ✅ **灵活性**: 无需修改代码即可调整参数
- ✅ **环境适配**: 开发/测试/生产使用不同配置
- ✅ **成本控制**: 精确控制每个服务的使用策略
- ✅ **团队协作**: 配置标准化，减少沟通成本

### ❓ **Q: 配置文件的优先级是什么？**
**A:** 优先级（高→低）：
1. 命令行参数 `--config custom.yaml`
2. 环境变量 `PRODUCER_CONFIG_PATH`
3. 项目配置 `config/config.yaml`
4. 代码默认值（硬编码）

### ❓ **Q: 如何在不同环境使用不同配置？**
**A:** 
```bash
# 开发环境
export PRODUCER_CONFIG_PATH=config/config.dev.yaml

# 生产环境  
export PRODUCER_CONFIG_PATH=config/config.prod.yaml

# 或使用命令行参数
uv run python -m producer.cli --config config.prod.yaml produce ...
```

### ❓ **Q: 配置修改后需要重启服务吗？**
**A:** 
- 🔄 **需要重启**: 系统级配置（system, budget）
- ⚡ **热更新**: 模型配置（可通过API动态切换）
- 📝 **建议**: 重要配置修改后重启服务确保稳定性

---

## 🎊 总结

配置文件的核心价值在于：

1. 🎯 **灵活控制**: 无需修改代码就能调整系统行为
2. 💰 **成本优化**: 精确控制每个环节的开销
3. 🔧 **环境适配**: 开发/测试/生产环境独立配置
4. 👥 **团队协作**: 配置标准化，降低维护成本
5. 📈 **持续优化**: 基于使用情况调整最优配置

**记住**：即使没有配置文件，系统仍会使用合理的默认值正常工作，配置文件只是让系统更好地满足您的特定需求！

---

**更新时间**: 2025年8月  
**文档版本**: v2.0  
**兼容版本**: Producer v1.0+