# Google Imagen 图像生成配置指南

## 📋 概述

Google Imagen 是 Google 推出的高保真图像生成模型，能够根据文本提示生成逼真且高品质的图片。本指南将详细说明如何在 Producer 系统中配置和使用 Imagen 模型。

## 🚀 支持的模型

Producer 系统支持以下 Google 图像生成模型：

### 免费模型（推荐）
- **gemini-2.0-flash-exp** - Gemini 2.0 Flash 实验版（完全免费）
- **gemini-2.0-flash** - Gemini 2.0 Flash 正式版（完全免费）
- **gemini-2.0-flash-preview-image-generation** - 专用图像生成版本（完全免费）

### 付费模型（高质量）
- **imagen-4.0-fast** - Imagen 4 快速版（$0.02/张）
- **imagen-4.0-standard** - Imagen 4 标准版（$0.04/张）
- **imagen-4.0-ultra** - Imagen 4 超级版（$0.06/张）
- **imagen-3.0** - Imagen 3（$0.03/张）

## 🔧 配置步骤

### 1. 获取 Google AI API 密钥

1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 登录您的 Google 账户
3. 点击 "Get API Key" 按钮
4. 创建新的 API 密钥
5. 复制生成的 API 密钥

### 2. 配置环境变量

在项目根目录创建或编辑 `.env` 文件：

```bash
# Google AI API 密钥
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# 也可以使用 GEMINI_API_KEY（兼容）
# GEMINI_API_KEY=your_google_ai_api_key_here
```

### 3. 配置系统设置

编辑 `config/config.yaml` 文件，设置图像生成服务：

```yaml
# 图像生成配置
image_generation:
  primary_service: "google"        # 使用 Google 作为主要服务
  fallback_service: "flux"         # 备用服务
  resolution: "1024x1024"          # 默认分辨率
  quality: "high"                  # 图像质量
  
  # Google 特定配置
  google:
    model: "gemini-2.0-flash-exp"  # 推荐免费模型
    # model: "imagen-4.0-standard" # 如需高质量可选择付费模型
    aspect_ratio: "1:1"            # 宽高比 1:1, 3:4, 4:3, 9:16, 16:9
    num_images: 1                  # 生成图片数量（1-4）
    person_generation: "allow_adult" # 人物生成策略
```

### 4. 验证配置

运行配置验证测试：

```bash
# 验证 Google API 配置
uv run python tests/api_tests/test_google_config.py

# 测试图像生成功能
uv run python tests/api_tests/test_google_image_api.py
```

## 🎨 使用示例

### 1. 命令行使用

```bash
# 使用默认配置生成视频（包含图像生成）
uv run python -m producer.cli produce \
  --title "唐朝盛世" \
  --theme "历史文化" \
  --era "唐朝" \
  --duration 180

# 指定使用 Google 图像生成
uv run python -m producer.cli produce \
  --title "宋朝科技" \
  --theme "科技发展" \
  --era "宋朝" \
  --duration 200 \
  --config config/config.yaml
```

### 2. 编程接口使用

```python
from adapters.image.google_adapter import GoogleImageAdapter
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController

# 初始化组件
config_manager = ConfigManager()
cost_controller = CostController(config_manager)

# 配置适配器
adapter_config = AdapterConfig(
    service_type="image",
    model="gemini-2.0-flash-exp",  # 使用免费模型
    max_retries=3,
    timeout=30
)

# 创建 Google 图像适配器
google_adapter = GoogleImageAdapter(
    config=adapter_config,
    config_manager=config_manager,
    cost_controller=cost_controller
)

# 生成图像
result = await google_adapter.generate(
    prompt="一位唐朝诗人在月夜下作诗，古典中国画风格",
    num_images=1,
    aspect_ratio="1:1"
)

if result.success:
    print(f"图像生成成功，保存路径：{result.data.output_path}")
    print(f"生成时间：{result.data.generation_time:.2f}秒")
    print(f"成本：${result.data.cost_usd:.4f}")
else:
    print(f"生成失败：{result.error_message}")
```

## 📊 模型特性对比

| 模型 | 价格 | 分辨率 | 速度 | 质量 | 免费额度 | 推荐场景 |
|------|------|--------|------|------|----------|----------|
| gemini-2.0-flash-exp | 免费 | 1024x1024 | 快 | 高 | 无限制 | 日常开发测试 |
| gemini-2.0-flash | 免费 | 1024x1024 | 快 | 高 | 无限制 | 生产环境 |
| imagen-4.0-fast | $0.02/张 | 2048x2048 | 快 | 极高 | 无 | 快速原型 |
| imagen-4.0-standard | $0.04/张 | 2048x2048 | 中 | 极高 | 无 | 标准质量需求 |
| imagen-4.0-ultra | $0.06/张 | 2048x2048 | 慢 | 顶级 | 无 | 最高质量需求 |

## 🔍 提示词优化技巧

### 1. 基础结构
```
[主体] + [背景环境] + [艺术风格] + [技术参数]
```

### 2. 示例提示词

**历史人物场景**：
```
一位穿着明朝服饰的文官在紫禁城大殿内批阅奏章，庄严肃穆的宫殿背景，中国古典绘画风格，细腻的人物刻画，明暗对比强烈，4K高清
```

**古代建筑**：
```
唐朝长安城的大雁塔在夕阳下的剪影，远山如黛，古典中国建筑摄影风格，温暖的金色调，景深效果，超高清画质
```

**战争场面**：
```
三国时期的骑兵冲锋场面，战马奔腾，旌旗飞舞，史诗级战争绘画风格，动态构图，强烈的视觉冲击力
```

### 3. 风格修饰词
- **中国古典**: `中国古典绘画风格`, `水墨画风`, `工笔画风格`
- **历史写实**: `历史写实风格`, `纪录片级别`, `电影级质感`
- **艺术化**: `概念艺术风格`, `插画风格`, `数字艺术`

## ⚠️ 注意事项

### 1. API 限制
- Imagen 模型仅支持英文提示词
- 单次最多生成 4 张图片
- 生成的图片包含 SynthID 水印

### 2. 成本控制
- 免费的 Gemini 模型无使用限制
- 付费 Imagen 模型按图片计费
- 建议设置日/月预算限制

### 3. 质量优化
- 提示词长度建议在 25-200 字符之间
- 避免版权敏感内容
- 可以指定艺术家风格但不要过于具体

## 🧪 测试命令

```bash
# 运行完整测试套件
pytest tests/ -k "google" -v

# 测试特定适配器
pytest tests/adapters/test_google_image_adapter.py -v

# 测试 API 连接
pytest tests/api_tests/test_google_image_api.py -v

# 配置验证测试
pytest tests/api_tests/test_google_config.py -v
```

## 🔧 故障排除

### 常见问题

1. **API 密钥无效**
   ```
   错误：Google AI API key not found
   解决：检查 .env 文件中的 GOOGLE_AI_API_KEY 配置
   ```

2. **配额超限**
   ```
   错误：Quota exceeded
   解决：检查 Google AI Studio 中的配额使用情况
   ```

3. **网络连接问题**
   ```
   错误：Connection timeout
   解决：检查网络连接，可能需要代理配置
   ```

### 调试模式

启用详细日志：

```yaml
# config/config.yaml
system:
  debug: true
  log_level: "DEBUG"
```

## 📝 最佳实践

1. **成本优化**：优先使用免费的 Gemini 模型
2. **质量平衡**：根据用途选择合适的模型档次
3. **提示词管理**：建立标准化的提示词模板
4. **批量处理**：合理安排生成任务，避免频繁调用
5. **缓存策略**：相同提示词的结果可以缓存复用

---

**更新时间**：2025年8月
**文档版本**：v1.0
**兼容版本**：Producer v1.0+