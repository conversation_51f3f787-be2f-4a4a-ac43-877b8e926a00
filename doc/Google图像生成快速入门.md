# Google 图像生成适配器 - 快速入门指南

## 🎉 重大利好：Google 免费图像生成

Google刚推出的Gemini 2.0 Flash图像生成功能**完全免费**，这是2025年AI图像生成领域的重大突破！

### ✨ 核心优势

- **🆓 完全免费**: Gemini 2.0 Flash 图像生成无任何费用
- **🚀 即开即用**: 无需本地GPU，云端处理
- **🎨 高质量**: 1024x1024分辨率，质量接近商业模型  
- **🔧 易集成**: 标准API接口，无缝接入现有项目
- **🌏 中文友好**: 对中文提示词理解准确

## 🛠️ 快速开始

### 1. 获取API密钥

1. 访问 [Google AI Studio](https://aistudio.google.com/apikey)
2. 登录Google账户
3. 创建新的API密钥
4. 复制密钥备用

### 2. 安装和配置

```bash
# 1. 设置环境变量
export GOOGLE_AI_API_KEY="your_api_key_here"

# 2. 安装依赖（如果还未安装）
pip install aiohttp pillow

# 3. 测试连接
python test_google_image_adapter.py
```

### 3. 基础使用

```python
import asyncio
from adapters.image.google_adapter import generate_image_with_google

async def main():
    # 免费生成图像
    response = await generate_image_with_google(
        prompt="古代中国山水画风格的风景",
        model="gemini-2.0-flash",  # 免费模型
        num_images=1,
        size="1024x1024"
    )
    
    # 保存图像
    saved_paths = response.save_images("./outputs", "test")
    print(f"图像已保存: {saved_paths}")

# 运行
asyncio.run(main())
```

## 📊 模型对比

| 模型 | 成本 | 质量 | 分辨率 | 速度 | 推荐场景 |
|------|------|------|--------|------|----------|
| **Gemini 2.0 Flash** | **免费** | ⭐⭐⭐⭐ | 1024x1024 | 快 | 日常使用 |
| Imagen 4 Fast | $0.02/图 | ⭐⭐⭐⭐⭐ | 2048x2048 | 很快 | 高质量需求 |
| Imagen 4 Ultra | $0.06/图 | ⭐⭐⭐⭐⭐+ | 2048x2048 | 慢 | 顶级质量 |
| DALL-E 3 | $0.04/图 | ⭐⭐⭐⭐⭐ | 1024x1024 | 中 | OpenAI生态 |
| Midjourney | $10+/月 | ⭐⭐⭐⭐⭐ | 多种 | 慢 | 艺术创作 |

## 🎯 最佳实践

### 免费策略 (推荐)
```python
# 主要使用免费模型
config = {
    "model": "gemini-2.0-flash",
    "size": "1024x1024",
    "num_images": 1
}
# 月成本: ¥0
```

### 混合策略 (高质量)
```python
# 日常免费 + 重要场景付费
def get_model(importance="normal"):
    if importance == "high":
        return "imagen-4.0-fast"  # 付费高质量
    else:
        return "gemini-2.0-flash"  # 免费日常
        
# 月成本: ¥10-50 (根据付费使用量)
```

## 💡 提示词技巧

### 历史题材优化
```python
# ✅ 好的提示词
prompt = "明代宫廷场景，皇帝身穿龙袍坐在金銮殿，宏伟建筑，传统中国绘画风格，细致入微，高品质"

# ❌ 普通提示词  
prompt = "古代皇帝"
```

### 风格控制
```python
styles = {
    "realistic": "photorealistic, highly detailed, professional photography",
    "traditional": "traditional Chinese painting, ink wash style, artistic",
    "anime": "anime style, cel-shading, vibrant colors",
    "historical": "historical accuracy, period-appropriate, documentary style"
}
```

## 🔧 高级配置

### 批量处理
```python
async def batch_generate(prompts, model="gemini-2.0-flash"):
    tasks = []
    for prompt in prompts:
        task = generate_image_with_google(
            prompt=prompt,
            model=model,
            num_images=1
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results
```

### 错误处理
```python
async def safe_generate(prompt, max_retries=3):
    for i in range(max_retries):
        try:
            return await generate_image_with_google(prompt)
        except Exception as e:
            if i == max_retries - 1:
                raise
            await asyncio.sleep(2 ** i)  # 指数退避
```

### 成本监控
```python
class CostTracker:
    def __init__(self):
        self.free_count = 0
        self.paid_cost = 0.0
    
    def track_generation(self, model, num_images):
        if model == "gemini-2.0-flash":
            self.free_count += num_images
        else:
            # 根据模型计算成本
            costs = {
                "imagen-4.0-fast": 0.02,
                "imagen-4.0-ultra": 0.06
            }
            self.paid_cost += costs.get(model, 0) * num_images
    
    def report(self):
        print(f"免费生成: {self.free_count} 张")
        print(f"付费成本: ${self.paid_cost:.2f}")
```

## 🚀 项目集成

### 1. 更新配置文件
```yaml
# config/config.yaml
adapters:
  image:
    google:
      service_name: "google_image"
      model: "gemini-2.0-flash"
      enabled: true
```

### 2. 更新工作流
```python
# 在剧本生成流程中集成
async def generate_scene_with_images(scene_data):
    # 生成剧本
    script = await generate_script(scene_data)
    
    # 提取图像提示
    image_prompts = extract_image_prompts(script)
    
    # 生成图像 (免费!)
    images = []
    for prompt in image_prompts:
        response = await generate_image_with_google(
            prompt=prompt,
            model="gemini-2.0-flash"
        )
        images.extend(response.images)
    
    return {
        "script": script,
        "images": images
    }
```

### 3. 部署建议
```bash
# Docker环境变量
ENV GOOGLE_AI_API_KEY=your_key
ENV IMAGE_OUTPUT_DIR=/app/outputs/images
ENV IMAGE_DEFAULT_MODEL=gemini-2.0-flash

# 无需GPU，降低硬件要求
# 之前: 需要RTX 4070+ (12GB VRAM)
# 现在: 普通CPU服务器即可
```

## 📈 成本对比分析

### 传统方案 vs Google方案

| 方案 | 初始投入 | 月运营成本 | 1000张图片成本 |
|------|----------|------------|----------------|
| **Google免费** | ¥0 | ¥0 | **¥0** |
| SDXL本地 | ¥15000 | ¥200 | ¥0 |
| DALL-E 3 | ¥0 | ¥0 | ¥300 |
| Midjourney | ¥0 | ¥140 | ¥140 |

**结论**: Google免费方案在任何规模下都是最优选择！

## 🎯 行动建议

1. **立即接入**: Google免费额度无限制，立即接入享受零成本图像生成
2. **替换现有方案**: 用Google免费模型替换付费图像生成服务
3. **扩大应用**: 成本降为零后，可以大胆尝试更多图像生成场景
4. **混合使用**: 日常用免费，关键场景用付费高质量模型

## 📞 技术支持

- **文档**: `doc/producer实现手册-LangChain版.md`
- **测试**: `python test_google_image_adapter.py`
- **配置**: `examples/google_image_config.yaml`
- **API文档**: https://ai.google.dev/gemini-api/docs/image-generation

---

**🎉 2025年AI图像生成进入免费时代，Google引领这场革命！立即行动，抢占先机！**