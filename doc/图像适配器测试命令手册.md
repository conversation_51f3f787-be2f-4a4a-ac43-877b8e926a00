# 图像适配器测试命令手册（pytest + uv）

本手册汇总与图像适配器相关的测试命令，并配套提供执行原理、参数说明、流程分解与最佳实践，帮助你快速、稳定、可解释地运行测试与定位问题。

**适用范围**：`tests/adapters/test_image_adapters.py` 中的 Flux 与 SDXL Lightning 适配器单元与集成测试。

**关联代码**：
- 适配器实现：
  - `adapters/image/flux_adapter.py`
  - `adapters/image/sdxl_lightning_adapter.py`
- 测试用例：
  - `tests/adapters/test_image_adapters.py`

---

## 一、基础命令

- 仅跑图像适配器测试（文件级）：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py
```

- 只跑 Flux 相关（TestFluxImageAdapter类）：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py -k "Flux"
```

- 只跑 SDXL Lightning 相关（TestSDXLLightningAdapter类）：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py -k "SDXL" 
```

- 运行特定测试类：
```bash
# 仅运行FluxImageAdapter测试类
uv run python -m pytest -q tests/adapters/test_image_adapters.py::TestFluxImageAdapter

# 仅运行SDXLLightningAdapter测试类 
uv run python -m pytest -q tests/adapters/test_image_adapters.py::TestSDXLLightningAdapter
```

### 执行原理
- `uv run`：在 uv 管理的虚拟环境中执行命令，确保依赖一致与隔离。
- `python -m pytest`：以模块方式运行 pytest，使用项目根下的 `pytest.ini` 配置。
- `-q`（quiet）：安静模式，仅输出最精简结果，适合常规回归。
- `-k <expr>`：按表达式过滤测试用例名（类名、函数名、节点名），实现快速子集测试。

### 参数说明
- `tests/adapters/test_image_adapters.py`：测试目标文件路径。
- `-q`：减少输出噪音，配合 CI 或频繁本地运行更高效。
- `-k Flux`：只选择名称包含 `Flux` 的测试（例如 `TestFluxImageAdapter` 类内的测试）。
- `-k SDXLLightning`：同理，仅选 SDXL Lightning 相关测试。

### 流程分解
1. uv 初始化 Python 解释器与依赖环境。
2. pytest 解析配置与收集测试（遵循 `pytest.ini`）。
3. 过滤器（`-k`）选取目标测试集合。
4. 执行单测，输出报告（`-q` 下为精简模式）。

### 最佳实践
- 回归优先使用 `-q` 提速；调试失败用例时去掉 `-q` 或加 `-vv` 查看详细断言差异。
- 使用 `-k` 精准选择目标，避免跑无关测试，提升迭代效率。
- 与环境变量配合（见后文"关键配置与环境要求"），确保真实调用或 Mock 行为符合预期。

---

## 二、常用进阶参数与组合

- 增强日志（详细输出）：
```bash
uv run python -m pytest -vv tests/adapters/test_image_adapters.py -k Flux
```

- 实时打印（不捕获 stdout/stderr）：
```bash
uv run python -m pytest -s tests/adapters/test_image_adapters.py -k Flux
```

- 首个失败即停止（快速定位首个问题）：
```bash
uv run python -m pytest -x tests/adapters/test_image_adapters.py -k Flux
```

- 失败后进入调试（交互式 pdb）：
```bash
uv run python -m pytest -x --pdb tests/adapters/test_image_adapters.py -k Flux
```

- 指定单个测试类或函数（节点选择）：
```bash
# 单个测试类
uv run python -m pytest -q tests/adapters/test_image_adapters.py::TestFluxImageAdapter

# 单个测试函数
uv run python -m pytest -q tests/adapters/test_image_adapters.py::TestFluxImageAdapter::test_generate_image_success
```

- 只重跑上次失败的用例（需要 cache 插件，pytest 内置）：
```bash
uv run python -m pytest --last-failed -q
```

- 失败重试（需要 pytest-rerunfailures 插件）：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py -k Flux --reruns 2 --reruns-delay 1
```

- 生成 JUnit XML 报告（CI 集成）：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py --junitxml=reports/image_adapters.junit.xml
```

- 并行执行测试（需要 pytest-xdist 插件）：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py -k Flux -n auto
```

---

## 三、关键配置与环境要求（技术背景）

### 测试配置结构
- **Fixtures配置**：测试使用 `tests/conftest.py` 中定义的fixtures
  - `image_adapter_config`: 图像适配器基础配置
  - `mock_config_manager`: 模拟配置管理器
  - `mock_cost_controller`: 模拟成本控制器
  - `mock_http_session`: 模拟HTTP会话
  - `sample_image_request`: 示例图像生成请求

### 环境变量与API配置
- 环境变量（`.env` 或系统环境）与配置管理：
  - 由 `core/config.py` 的 `ConfigManager` 统一加载与管理。
  - 图像适配器关键密钥：
    - `FLUX_API_KEY`（Black Forest Labs官方）
    - `STABILITY_API_KEY`（Stability AI）
  - `.env.template` 提供示例变量名，请复制为 `.env` 并填入真实值。

### API端点配置
- **Flux 适配器调用链**：
  - 提交任务：`https://api.bfl.ml/v1/flux-pro`（根据模型不同可能为 flux-dev 或 flux-schnell）
  - 轮询结果：`https://api.bfl.ml/v1/get_result?id=...`
  - 支持的模型：`flux-pro`、`flux-dev`、`flux-schnell`（分辨率与步数上限不同）
  - 成本范围：$0.003-$0.055 per image

- **SDXL Lightning 适配器**：
  - 端点：`https://api.stability.ai/v1/generation/<engine>/text-to-image`
  - 支持的引擎：`stable-diffusion-xl-1024-v1-0`、`sdxl-lightning`、`stable-diffusion-xl-beta-v2-2-2`
  - 成本范围：$0.02-$0.04 per image

### 测试隔离策略
- **测试隔离策略**：
  - 单元测试大量使用 `unittest.mock` 的 `patch/AsyncMock` 注入伪响应，避免真实扣费
  - 仅在显式需要时，才依赖真实 API Key 与网络连通性
  - Mock策略确保测试的可重复性和快速执行

### 测试覆盖范围
- **TestFluxImageAdapter** 包含的测试：
  - `test_validate_config`: 配置验证
  - `test_get_supported_models`: 支持的模型列表
  - `test_get_supported_resolutions`: 支持的分辨率
  - `test_estimate_cost`: 成本估算
  - `test_generate_image_success`: 成功生成图像
  - `test_generate_image_task_polling`: 任务轮询机制
  - `test_generate_image_task_failed`: 任务失败处理
  - `test_generate_image_api_error`: API错误处理
  - `test_generate_image_rate_limit`: 速率限制处理

- **TestSDXLLightningAdapter** 包含的测试：
  - `test_validate_config`: 配置验证
  - `test_get_supported_models`: 支持的模型列表
  - `test_get_supported_resolutions`: 支持的分辨率 
  - `test_estimate_cost`: 成本估算
  - `test_generate_image_success`: 成功生成图像
  - `test_generate_image_with_style_preset`: 样式预设处理
  - `test_generate_image_with_negative_prompt`: 负面提示词处理
  - `test_generate_image_content_filter`: 内容过滤处理
  - `test_generate_image_api_error`: API错误处理

### uv环境管理
  - 以 `uv run` 固定并隔离依赖，减少"本地能跑 / CI 失败"之类的环境差异。

---

## 四、典型工作流示例（从定位到修复）

- 快速验证 Flux 相关改动：
```bash
uv run python -m pytest -q tests/adapters/test_image_adapters.py -k Flux
```

- 若失败，用详细输出定位具体断点：
```bash
uv run python -m pytest -vv tests/adapters/test_image_adapters.py -k Flux
```

- 需要看打印（例如 HTTP 构造、解析路径）：
```bash
uv run python -m pytest -s tests/adapters/test_image_adapters.py -k Flux
```

- 修复后仅重跑失败用例，加速验证：
```bash
uv run python -m pytest --last-failed -q
```

---

## 五、最佳实践与经验

- 优先使用 `-k` 精准筛选，缩短回路。
- 本地调试尽量 Mock 外部接口，确认流程正确再上真实密钥做连通性测试，避免不必要花费。
- 将 `.env` 与 `config/config.yaml` 组合使用：
  - 密钥与可变参数放 `.env`，方便切换；
  - 业务默认放 `config.yaml`，保证测试可重复性。
- 为易回归的问题添加最小化用例，结合 `--last-failed` 快速复测。
- 在 CI 中输出 JUnit 报告，配合测试趋势与回归分析。
- 使用 `-n auto` 参数并行执行测试，大幅提升执行速度（需要安装 pytest-xdist）。

---

## 六、常见问题（FAQ）与故障排查

- **现象**：测试一开始就报 `No such option: --adapter`
  - **原因**：`producer/cli.py` 的 `test` 命令不支持 `--adapter` 参数。
  - **处理**：改用 pytest 子集筛选（`-k`）或使用本手册中的精确节点选择方法。

- **现象**：Flux 测试报密钥/认证错误
  - **检查**：`.env` 是否正确配置 `FLUX_API_KEY`。
  - **说明**：若仅跑单测（mock模式），无需真实 Key；若做连通性测试，需填入真实有效的 Key。
  - **验证**：`echo $FLUX_API_KEY` 检查环境变量是否正确加载。

- **现象**：SDXL测试报`STABILITY_API_KEY`相关错误
  - **检查**：确认Stability AI的API密钥是否正确配置。
  - **注意**：不同的SDXL引擎可能有不同的权限要求。

- 现象：网络相关超时
  - 建议：
    - 单元测试优先走 Mock，真实连通在网络稳定时执行；
    - 使用 `-x --pdb` 聚焦首个失败并现场排查。

- 现象：输出过多难以定位
  - 处理：常规回归用 `-q`；定位问题换用 `-vv` 或加 `-s`。

- 现象：测试执行速度慢
  - 建议：
    - 使用 `-n auto` 并行执行测试（需要安装 pytest-xdist）；
    - 仅运行相关测试类或方法，避免全文件运行；
    - 使用 `--last-failed` 仅重跑失败用例。

---

## 七、知识迁移：为何推荐这种测试组织

- 将"文件级"+"表达式筛选（-k）"结合：在单文件内按命名划分领域（Flux/SDXL），以命名筛选替代繁杂的自定义标记，门槛低、直观高效。
- uv 统一入口：不同机器、不同 CI 环境通过 `uv run` 统一依赖管理，减少"环境雪花"问题。
- Mock 优先：
  - 单元层验证协议与逻辑正确；
  - 真连通只在必要时做，降低成本与不确定性。

---

## 八、参考
- `pytest` 文档：https://docs.pytest.org/
- `uv` 项目：https://github.com/astral-sh/uv
- Black Forest Labs API（FLUX）：https://api.bfl.ml/
- Stability AI API（SDXL）：https://api.stability.ai/

---

## 📝 文档更新记录

### v2.0 (2024年8月更新)
- **修正CLI命令**: 更新了过滤器命令以匹配实际的测试类名称
- **完善测试覆盖范围**: 详细列出了每个测试类包含的具体测试方法
- **增强配置说明**: 添加了fixtures配置和环境变量的详细说明
- **优化故障排查**: 提供了更实用的调试建议和解决方案
- **补充API信息**: 更新了API端点和成本信息

### 重要提醒
- ✅ **测试类名称**: TestFluxImageAdapter 和 TestSDXLLightningAdapter
- ✅ **过滤器使用**: 使用 `-k "Flux"` 和 `-k "SDXL"` 进行精确过滤
- ✅ **环境变量**: 确保正确配置 FLUX_API_KEY 和 STABILITY_API_KEY
- ✅ **Mock优先**: 单元测试优先使用Mock，避免不必要的API消耗
