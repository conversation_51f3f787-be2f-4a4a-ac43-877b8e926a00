# 🎬 Producer历史短剧视频制作系统 - 操作手册

## 📋 目录
- [环境配置](#环境配置)
- [配置文件管理](#配置文件管理)
- [常用命令](#常用命令)
- [系统测试](#系统测试)
- [视频制作](#视频制作)
- [批量处理](#批量处理)
- [部署运维](#部署运维)
- [故障排除](#故障排除)
- [API密钥配置](#api密钥配置)
- [监控和维护](#监控和维护)
- [最佳实践](#最佳实践)
- [支持和帮助](#支持和帮助)

---

## 🔧 环境配置

### 1. Python环境设置
```bash
# 检查Python版本（需要3.11+）
python --version

# 创建虚拟环境（如果还没有）
uv venv --python $(which python3)

# 激活虚拟环境
source .venv/bin/activate

# 安装项目依赖
uv sync

# 安装所有可选依赖（包括开发工具）
uv sync --all-extras
```

### 2. 环境变量配置
```bash
# 复制环境变量模板（如果存在）
cp .env.template .env

# 如果没有模板文件，创建新的.env文件
touch .env

# 编辑环境变量文件
nano .env
# 或者使用其他编辑器
code .env
```

> **注意**：如果项目中没有 `.env.template` 文件，请参考 [API密钥配置详细指南](./API密钥配置详细指南.md) 来创建和配置 `.env` 文件。

### 3. 验证环境
```bash
# 检查Python路径
which python

# 检查已安装的包
uv pip list

# 验证项目结构
ls -la
```

---

## 📁 配置文件管理

### 1. 配置文件结构
```bash
config/
└── config.yaml              # 主配置文件

# 可选配置文件（根据需要创建）
config/
├── config.yaml              # 基础配置
├── config.dev.yaml          # 开发环境
├── config.test.yaml         # 测试环境
└── config.prod.yaml         # 生产环境
```

### 2. 为什么需要配置文件？

💡 **简单回答**：以前没有配置也能用，是因为代码里有默认值。现在有了配置文件，您可以：

- ✅ **灵活切换模型**：无需修改代码，切换免费/付费模型
- ✅ **成本控制**：精确设置预算限制和告警阈值
- ✅ **环境适配**：开发/测试/生产环境使用不同配置
- ✅ **参数调优**：调整图像分辨率、宽高比等参数

### 3. 快速配置指南

#### 方案一：🆓 完全免费（推荐）
```yaml
# config/config.yaml
image_generation:
  primary_service: "google"
  google:
    model: "gemini-2.0-flash-exp"    # Google免费模型
    num_images: 1                     # 减少数量
    aspect_ratio: "1:1"               # 标准比例
```

#### 方案二：⚡ 快速原型
```yaml
image_generation:
  google:
    model: "imagen-4.0-fast"         # 快速付费模型（$0.02/张）
    num_images: 2                     # 多选择性
    aspect_ratio: "16:9"              # 视频比例
```

#### 方案三：🏆 商业品质
```yaml
image_generation:
  google:
    model: "imagen-4.0-ultra"        # 最高质量（$0.06/张）
    num_images: 4                     # 最大数量
    aspect_ratio: "3:4"               # 竖屏比例
```

### 4. 配置文件操作

```bash
# 查看当前配置
cat config/config.yaml

# 编辑配置文件
nano config/config.yaml
# 或者
code config/config.yaml

# 验证配置文件格式
uv run python -c "import yaml; yaml.safe_load(open('config/config.yaml'))"

# 使用特定配置文件
uv run python -m producer.cli --config config/config.dev.yaml produce ...
```

### 5. 常见配置项

| 配置项 | 作用 | 推荐值 |
|---------|------|----------|
| `google.model` | 选择Google图像模型 | `gemini-2.0-flash-exp` (免费) |
| `google.aspect_ratio` | 设置图像宽高比 | `1:1`, `16:9`, `3:4` |
| `google.num_images` | 每次生成图片数量 | `1` (节约成本) |
| `budget.daily_limit_usd` | 日消费限额 | `10.0` |
| `text_generation.primary_model` | 主力文本模型 | `glm-4-flash` |

### 6. 配置优先级

配置的优先级（高→低）：
1. 🔴 **命令行参数** `--config custom.yaml`
2. 🟡 **环境变量** `PRODUCER_CONFIG_PATH`
3. 🟢 **项目配置** `config/config.yaml`
4. ⚪ **代码默认值**（硬编码）

```bash
# 示例：使用不同配置

# 使用开发环境配置
export PRODUCER_CONFIG_PATH=config/config.dev.yaml
uv run python -m producer.cli produce ...

# 或者直接指定
uv run python -m producer.cli --config config.prod.yaml produce ...
```

> 📝 **详细说明**：完整的配置文件说明请参考 [《配置文件详细说明》](./配置文件详细说明.md)

---

## ⚡ 常用命令

### 🚀 CLI运行方式

Producer提供了多种便捷的运行方式，你可以根据喜好选择：

#### 方式1：独立脚本（推荐）
```bash
# 最简单的运行方式，无需-m参数
uv run python producer_cli.py --help
uv run python producer_cli.py status
uv run python producer_cli.py version
```

#### 方式2：直接运行（无需uv）
```bash
# 如果已激活虚拟环境，可直接运行
python producer_cli.py --help
python producer_cli.py status
python producer_cli.py version
```

#### 方式3：模块方式（标准）
```bash
# Python标准的模块运行方式
uv run python -m producer.cli --help
uv run python -m producer.cli status
uv run python -m producer.cli version
```

> **重要提示**：独立脚本 `producer_cli.py` 实际上是对主CLI模块的简化封装，参数可能略有不同。建议使用方式3（模块方式）以获得完整功能。

### 🔍 运行方式对比

| 方式 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 独立脚本 | 简单直观，无需记忆-m参数 | 功能可能受限，参数不完整 | 快速测试 |
| 直接运行 | 最快速，无额外依赖 | 需要激活环境 | 开发调试 |
| 模块方式 | 功能完整，Python标准做法 | 命令稍长 | **生产环境推荐** |

### 系统管理命令
```bash
# 查看系统状态（推荐使用模块方式）
uv run python -m producer.cli status

# 查看版本信息
uv run python -m producer.cli version

# 查看帮助信息
uv run python -m producer.cli --help

# 快速方式（独立脚本）
uv run python producer_cli.py status
uv run python producer_cli.py version
uv run python producer_cli.py --help
```

### 测试命令
```bash
# 运行完整系统测试
uv run python test_workflow.py

# 运行CLI测试（推荐方式）
uv run python -m producer.cli test

# 运行CLI测试（独立脚本方式）
uv run python producer_cli.py test

# 运行特定适配器测试
uv run pytest tests/adapters/ -v

# 运行所有单元测试
uv run pytest

# 运行特定测试文件
uv run pytest tests/adapters/test_text_adapters.py -v
```

> 进一步阅读：图像适配器的更详细测试说明（执行原理、参数解释、流程分解、最佳实践与故障排查），请参见《[图像适配器测试命令手册](./图像适配器测试命令手册.md)》。

### 开发工具命令
```bash
# 代码格式化
uv run black .

# 类型检查
uv run mypy producer/

# 代码质量检查
uv run flake8 producer/

# 安全检查
uv run bandit -r producer/
```

---

## 🧪 系统测试

### 基础功能测试
```bash
# 1. 运行工作流测试
uv run python test_workflow.py

# 2. 测试各个适配器
uv run pytest tests/adapters/ -v

# 3. 测试CLI功能（推荐方式）
uv run python -m producer.cli test

# 4. 测试CLI功能（独立脚本方式）
uv run python producer_cli.py test

# 5. 运行模拟测试（不消耗API额度）
uv run python test_workflow_mock.py
```

### 预期输出示例
```
==================================================
Producer 工作流程测试
==================================================

开始测试各个链路...
✅ 大纲生成成功: 测试剧本
   角色数量: 3
   场景数量: 5

开始测试基本工作流程...
✅ 工作流程测试成功!

==================================================
测试总结:
链路测试: ✅ 通过
工作流测试: ✅ 通过
==================================================
```

---

## 🎭 视频制作

### 制作单个视频

#### 推荐方式（模块方式）
```bash
# 基础制作命令
uv run python -m producer.cli produce \
  --title "明朝风云" \
  --theme "宫廷斗争" \
  --era "明朝" \
  --duration 180

# 完整参数示例
uv run python -m producer.cli produce \
  --title "唐太宗贞观之治" \
  --theme "政治改革" \
  --era "唐朝" \
  --duration 200 \
  --budget 2.0 \
  --output ./output \
  --config ./config/config.yaml
```

#### 独立脚本方式
```bash
# 使用独立脚本（参数可能有限制）
uv run python producer_cli.py produce \
  --title "唐太宗贞观之治" \
  --theme "政治改革" \
  --era "唐朝" \
  --duration 200

# 注意：独立脚本可能不支持所有参数，如budget、output等
```

#### 直接运行方式
```bash
# 如果已激活虚拟环境，可直接运行
python -m producer.cli produce \
  --title "唐太宗贞观之治" \
  --theme "政治改革" \
  --era "唐朝" \
  --duration 200 \
  --budget 2.0 \
  --output ./output \
  --config ./config/config.yaml
```

### 📋 参数详细说明

> **重要提示**：以下参数说明基于完整的模块CLI（`producer.cli`）。独立脚本 `producer_cli.py` 可能不支持所有参数。

#### 必需参数
- `--title, -t`: **剧本标题**（必需）
  - 用途：定义视频的主题和标识
  - 示例：`"唐太宗贞观之治"`、`"明朝风云"`
  - 注意：标题会用于生成文件名和项目ID

#### 可选参数
- `--theme`: **剧本主题**（默认：`历史剧情`）
  - 用途：指导AI生成内容的风格和方向
  - 示例：`"政治改革"`、`"宫廷斗争"`、`"战争史诗"`
  - 影响：决定故事情节的类型和氛围

- `--era, -e`: **历史时代**（默认：`明朝`）
  - 用途：设定故事背景的历史时期
  - 示例：`"唐朝"`、`"宋朝"`、`"清朝"`
  - 影响：影响人物服装、建筑风格、语言特色

- `--duration, -d`: **视频时长**（默认：`180`秒）
  - 用途：控制最终视频的播放时长
  - 范围：建议60-300秒（1-5分钟）
  - 影响：决定场景数量和对话密度

- `--budget, -b`: **预算限制**（默认：`1.0`美元）
  - 用途：控制API调用成本，防止超支
  - 建议：新手1-2美元，熟练后可适当提高
  - 影响：影响模型选择和生成质量

- `--output, -o`: **输出目录**（默认：`./output`）
  - 用途：指定生成文件的保存位置
  - 格式：相对路径或绝对路径
  - 自动创建：目录不存在时会自动创建

- `--config, -c`: **配置文件**（默认：`./config/config.yaml`）
  - 用途：指定系统配置文件路径
  - 内容：API密钥、模型选择、质量参数等
  - 重要性：决定系统行为和输出质量

### 🎯 参数搭配建议

#### 快速测试（低成本）
```bash
uv run python -m producer.cli produce \
  --title "测试短剧" \
  --duration 60 \
  --budget 0.5
```

#### 标准制作（平衡质量与成本）
```bash
uv run python -m producer.cli produce \
  --title "唐太宗贞观之治" \
  --theme "政治改革" \
  --era "唐朝" \
  --duration 180 \
  --budget 2.0
```

#### 高质量制作（成本较高）
```bash
uv run python -m producer.cli produce \
  --title "康熙大帝" \
  --theme "帝王传记" \
  --era "清朝" \
  --duration 300 \
  --budget 5.0
```

---

## 📦 批量处理

### 批量制作命令

#### 推荐方式（模块方式）
```bash
# 使用示例配置文件
uv run python -m producer.cli batch examples/batch_scripts.json

# 指定输出目录和配置文件
uv run python -m producer.cli batch \
  examples/batch_scripts.json \
  --output ./batch_output \
  --config ./config/config.yaml
```

#### 独立脚本方式
```bash
# 使用示例配置文件（功能可能受限）
uv run python producer_cli.py batch examples/batch_scripts.json

# 注意：独立脚本可能不支持batch命令
```

### 批量配置文件格式
```json
{
  "scripts": [
    {
      "title": "唐太宗贞观之治",
      "theme": "政治改革",
      "era": "唐朝",
      "duration": 180,
      "summary": "李世民登基后推行贞观之治的历史故事"
    },
    {
      "title": "明成祖迁都北京",
      "theme": "历史决策",
      "era": "明朝",
      "duration": 200,
      "summary": "朱棣决定迁都北京的历史背景和过程"
    }
  ]
}
```

---

## 🚀 部署运维

### Docker部署
```bash
# 构建镜像
docker build -t producer .

# 使用docker-compose启动
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f producer

# 停止服务
docker-compose down
```

### 本地部署
```bash
# 创建输出和日志目录
mkdir -p output logs temp

# 设置权限
chmod 755 output logs temp

# 如果有API服务（当前版本主要是CLI工具）
# uv run python -m producer.api

# 后台运行（如果需要）
# nohup uv run python -m producer.api > logs/api.log 2>&1 &

# 当前版本主要通过CLI使用
echo "Producer主要通过CLI工具使用，无需启动后台服务"
```

---

## 🔑 API密钥配置

### 必需的API密钥
```bash
# 编辑.env文件，填入以下密钥：

# 文本生成（至少配置一个）
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_claude_key_here
DASHSCOPE_API_KEY=your_qwen_key_here

# 图像生成（可选，有本地FLUX.1）
STABILITY_API_KEY=your_stability_key_here

# 视频生成
KLING_API_KEY=your_kling_key_here
RUNWAY_API_KEY=your_runway_key_here

# 语音合成（可选，有本地CosyVoice）
ELEVENLABS_API_KEY=your_elevenlabs_key_here
```

### 验证API密钥
```bash
# 测试API连接
uv run python -c "from core.config import ConfigManager; print('配置加载成功')"

# 运行连接测试
uv run python -m producer.cli test
```

---

## 🔧 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 清理缓存重新安装
uv cache clean
uv sync --reinstall
```

#### 2. API密钥错误
```bash
# 检查环境变量
cat .env | grep API_KEY

# 测试API连接（推荐方式）
uv run python -m producer.cli test

# 测试API连接（独立脚本方式）
uv run python producer_cli.py test

# 验证特定API密钥
echo $OPENAI_API_KEY
echo $ANTHROPIC_API_KEY
```

#### 3. 内存不足
```bash
# 检查系统资源
free -h
df -h

# 调整配置文件中的并发数
nano config/config.yaml
# 修改 max_concurrent_tasks: 1
```

#### 4. 权限问题
```bash
# 检查目录权限
ls -la output/
ls -la temp/
ls -la logs/

# 创建必要目录
mkdir -p output temp logs
chmod 755 output temp logs

# 检查Python环境权限
which python
echo $VIRTUAL_ENV
```

### 日志查看
```bash
# 查看系统日志（如果存在）
tail -f logs/producer.log

# 查看错误日志
grep ERROR logs/producer.log 2>/dev/null || echo "日志文件不存在"

# 调试模式运行（推荐方式）
LOG_LEVEL=DEBUG uv run python -m producer.cli produce -t "测试"

# 调试模式运行（独立脚本方式）
LOG_LEVEL=DEBUG uv run python producer_cli.py produce -t "测试"

# 创建日志目录（如果不存在）
mkdir -p logs
```

---

## 📊 监控和维护

### 性能监控
```bash
# 查看系统状态（推荐方式）
uv run python -m producer.cli status

# 查看系统状态（独立脚本方式）
uv run python producer_cli.py status

# 监控资源使用
htop
# 或
top

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 定期维护
```bash
# 清理临时文件
rm -rf temp/*

# 清理输出目录中的旧文件（小心使用）
# find output/ -name "*.mp4" -mtime +30 -delete

# 清理旧日志（保留最近7天）
find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || echo "logs目录不存在或为空"

# 更新依赖
uv sync --upgrade

# 清理uv缓存
uv cache clean

# 检查依赖安全漏洞
uv audit
```

---

## 💡 最佳实践

### 1. 成本控制
- 设置合理的日预算和月预算
- 优先使用本地模型（FLUX.1、CosyVoice）
- 监控API调用成本

### 2. 质量保证
- 定期运行测试确保系统正常
- 检查输出质量并调整参数
- 备份重要配置和数据

### 3. 性能优化
- 根据硬件资源调整并发数
- 使用缓存减少重复计算
- 定期清理临时文件

---

## 📞 支持和帮助

如遇到问题，请按以下顺序排查：

1. **查看本操作手册**和相关文档
2. **运行系统测试**：`uv run python -m producer.cli test`
3. **检查环境配置**：
   - Python版本：`python --version`（需要3.11+）
   - 依赖安装：`uv sync`
   - 环境变量：`cat .env`
4. **验证API密钥**：检查`.env`文件中的API密钥是否正确
5. **查看日志文件**：`tail -f logs/producer.log`（如果存在）
6. **检查配置文件**：`config/config.yaml`
7. **尝试模拟测试**：`uv run python test_workflow_mock.py`（不消耗API额度）

### 📚 相关文档
- [API密钥配置详细指南](./API密钥配置详细指南.md)
- [图像适配器测试命令手册](./图像适配器测试命令手册.md)
- [test_workflow详细原理和用法说明](./test_workflow详细原理和用法说明.md)

---

**🎬 祝你使用Producer制作出精彩的历史短剧！**

---

## 📁 项目结构说明

### 核心目录结构
```
producer/
├── 📁 core/                 # 核心引擎模块
│   ├── chains/             # LangChain剧本生成链
│   ├── workflow.py         # 主工作流引擎
│   ├── models.py          # 数据模型定义
│   ├── config.py          # 配置管理
│   └── cost_control.py    # 成本控制
├── 📁 adapters/            # AI服务适配器
│   ├── text/              # 文本生成适配器
│   ├── image/             # 图像生成适配器
│   ├── video/             # 视频生成适配器
│   └── voice/             # 语音合成适配器
├── 📁 producer/            # CLI入口模块
│   ├── cli.py             # 主要CLI命令
│   └── adapters/          # 简化的适配器
├── 📁 config/              # 配置文件
│   └── config.yaml        # 主配置文件
├── 📁 templates/           # Jinja2模板
├── 📁 tests/              # 测试套件
├── 📁 doc/                # 文档目录
├── 📁 examples/           # 示例文件
│   └── batch_scripts.json # 批量脚本示例
├── 📁 output/             # 输出目录
├── 📁 temp/               # 临时文件目录
├── 📁 logs/               # 日志目录
├── producer_cli.py        # 独立CLI脚本
├── script_cli.py          # 剧本生成专用CLI
├── video_cli.py           # 视频制作专用CLI
├── test_workflow.py       # 工作流测试脚本
└── test_workflow_mock.py  # 模拟测试脚本
```

### 重要文件说明
- **producer_cli.py**: 简化的独立CLI脚本，参数可能有限
- **producer/cli.py**: 完整的CLI模块，包含所有功能
- **config/config.yaml**: 系统主配置文件
- **.env**: 环境变量和API密钥配置
- **examples/batch_scripts.json**: 批量处理示例配置

---

## 🔧 系统要求和兼容性

### 系统要求
- **Python**: 3.11+ (推荐 3.11 或 3.12)
- **操作系统**: macOS, Linux, Windows
- **内存**: 至少 4GB RAM (推荐 8GB+)
- **存储**: 至少 2GB 可用空间
- **网络**: 稳定的互联网连接（用于API调用）

### 可选硬件要求
- **GPU**: 用于本地模型加速（FLUX.1, CosyVoice）
- **CUDA**: 支持NVIDIA GPU加速

### 软件依赖
- **FFmpeg**: 视频处理（系统级依赖）
- **uv**: Python包管理器
- **Docker**: 容器化部署（可选）

---

## ❓ 常见问题解答 (FAQ)

### Q1: 为什么有多个CLI入口点？
**A1**: 
- `producer_cli.py`: 简化的独立脚本，适合快速测试
- `producer/cli.py`: 完整功能的主CLI模块，建议生产使用
- `script_cli.py`: 专门用于剧本生成
- `video_cli.py`: 专门用于视频制作

### Q2: 如何选择合适的运行方式？
**A2**: 
- **开发调试**: 使用独立脚本 `producer_cli.py`
- **生产环境**: 使用模块方式 `python -m producer.cli`
- **自动化脚本**: 使用模块方式确保稳定性

### Q3: API密钥配置复杂怎么办？
**A3**: 
1. 先配置最基本的文本生成API（如OpenAI）
2. 参考 [API密钥配置详细指南](./API密钥配置详细指南.md)
3. 逐步添加其他服务的API密钥
4. 使用模拟测试验证配置

### Q4: 视频制作失败怎么排查？
**A4**: 
1. 运行 `uv run python -m producer.cli test` 检查系统状态
2. 检查 `logs/producer.log` 日志文件
3. 验证API密钥是否正确配置
4. 确认网络连接正常
5. 检查预算限制是否足够

### Q5: 如何降低制作成本？
**A5**: 
1. 使用本地模型（FLUX.1、CosyVoice）
2. 选择成本较低的API服务
3. 设置合理的预算限制
4. 先用较短的视频测试
5. 优先使用免费额度的服务

### Q6: 批量制作如何配置？
**A6**: 
1. 参考 `examples/batch_scripts.json`
2. 每个剧本包含 title、theme、era、duration 等字段
3. 使用 `uv run python -m producer.cli batch` 命令
4. 建议先小批量测试

---

## 🆕 版本信息

### 当前版本: v1.0.0
- **发布日期**: 2024年8月
- **主要特性**: 
  - 基于LangChain的剧本生成
  - 混合优化方案（本地+云端）
  - 多种AI服务适配器
  - 完整的CLI工具集
  - 成本控制和监控

### 版本兼容性
- **Python**: 3.11+ (不支持 3.10 及以下)
- **LangChain**: >= 0.3.27
- **uv**: 最新版本

---

## 🔮 后续版本规划

### v1.1.0 (计划中)
- Web界面支持
- 更多本地模型集成
- 批量处理优化
- 实时进度监控

### v1.2.0 (计划中)
- 插件系统
- 自定义模板支持
- 高级成本分析
- 团队协作功能

---
