# Producer完整使用手册

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [剧本生成详解](#剧本生成详解)
4. [视频制作流程](#视频制作流程)
5. [系统架构](#系统架构)
6. [配置管理](#配置管理)
7. [高级用法](#高级用法)
8. [故障排除与最佳实践](#故障排除与最佳实践)

---

## 系统概述

Producer是一个基于LangChain的历史短剧视频制作系统，采用模块化设计，支持从剧本生成到视频制作的完整工作流。系统整合了多种AI服务，包括文本生成、图像生成、视频生成和语音合成，提供灵活的配置选项和成本控制机制。

### 核心特性

- **模块化架构**：系统采用分层解耦架构，各组件职责清晰，易于维护和扩展
- **多阶段工作流**：支持剧本生成和视频制作的分阶段执行，可灵活控制制作流程
- **成本控制**：内置成本监控和预算管理功能，确保在预算范围内运行
- **多模型支持**：支持多种AI模型和服务提供商，可根据需求选择最适合的模型
- **命令行界面**：提供简单易用的CLI工具，支持批量处理和项目管理

### 系统组件

- **核心引擎**：包含工作流引擎、剧本生成器、数据模型和成本控制
- **适配器层**：支持多种AI服务的适配器，包括文本、图像、视频和语音
- **CLI接口**：提供多个命令行工具，支持不同使用场景
- **配置管理**：灵活的配置系统，支持环境变量和配置文件

---

## 快速开始

### 安装与配置

1. **环境准备**

```bash
# 克隆项目
git clone https://github.com/your-repo/producer.git
cd producer

# 安装依赖
pip install -r requirements.txt
# 或使用uv
uv sync
```

2. **配置API密钥**

```bash
# 设置环境变量
export GOOGLE_AI_API_KEY="your_google_api_key"
export OPENAI_API_KEY="your_openai_api_key"
export KLING_API_KEY="your_kling_api_key"
```

3. **配置文件**

系统使用YAML格式的配置文件，默认位置为`./config/config.yaml`：

```yaml
system:
  mode: "hybrid_optimized"
  debug: false

text_generation:
  primary_model: "glm-4"
  backup_models: ["gpt-3.5-turbo", "claude-3-sonnet"]
  max_tokens: 4000
  temperature: 0.7

image_generation:
  primary_service: "google"
  google:
    model: "gemini-2.0-flash-exp"
    max_images: 4

video_generation:
  primary_service: "kling"
  model: "kling-v1"
  max_duration: 30

voice_synthesis:
  primary_service: "edgetts"
  voice_id: "zh-CN-XiaoxiaoNeural"

cost_control:
  daily_budget: 5.0
  monthly_budget: 100.0
  enable_alerts: true
```

### 基本使用

#### 1. 制作完整视频

```bash
# 使用主CLI制作完整视频
python -m producer.cli produce \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180 \
  --output ./output \
  --budget 2.0
```

#### 2. 生成剧本

```bash
# 创建新剧本项目（仅大纲）
python script_cli.py create \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180

# 创建完整剧本（包括场景和对话）
python script_cli.py create \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180 \
  --full

# 从大纲继续生成完整剧本
python script_cli.py continue <project_id>
```

#### 3. 基于剧本制作视频

```bash
# 使用现有剧本制作视频
python video_cli.py produce_from_script <script_id>
```

#### 4. 管理剧本项目

```bash
# 列出所有剧本项目
python script_cli.py list-projects

# 显示项目详情
python script_cli.py show <project_id>

# 删除项目
python script_cli.py delete <project_id>
```

---

## 剧本生成详解

### 剧本生成流程

剧本生成分为三个主要阶段：

1. **大纲生成**：创建剧本的基本框架，包括剧情摘要、主要角色和场景列表
2. **场景详细化**：为每个场景添加详细描述、背景、氛围等元素
3. **对话生成**：为角色生成符合情境和性格的对话内容

### 大纲生成

大纲生成是剧本创作的第一步，它定义了剧本的整体结构和关键元素。

```python
# 大纲生成示例
from core.script_generator import ScriptGenerator
from core.config import ConfigManager

async def generate_outline():
    config_manager = ConfigManager()
    generator = ScriptGenerator(config_manager)
    
    # 创建项目
    project = await generator.create_project(
        title="贞观之治",
        theme="唐太宗治国理政",
        era="唐朝",
        target_duration=180
    )
    
    # 生成大纲
    project = await generator.generate_outline(
        project,
        character_count=3,
        scene_count=5
    )
    
    return project
```

大纲生成结果包含：

- **剧情摘要**：简要描述剧本的主要情节和发展
- **主要角色**：角色列表，包含姓名、描述和性格特征
- **场景列表**：场景列表，包含标题、地点、时间段和描述
- **关键冲突**：剧本中的主要冲突和矛盾
- **情感发展线**：角色情感的变化和发展

### 场景详细化

场景详细化阶段为每个场景添加丰富的细节，包括环境描述、氛围营造、视觉元素等。

```python
# 场景详细化示例
async def generate_scenes(project):
    config_manager = ConfigManager()
    generator = ScriptGenerator(config_manager)
    
    # 生成场景详情
    project = await generator.generate_scenes(project)
    
    return project
```

场景详细化包含：

- **场景描述**：详细的环境和背景描述
- **氛围营造**：场景的情感氛围和基调
- **视觉元素**：重要的视觉提示和元素
- **角色动作**：角色在场景中的动作和互动
- **时间安排**：场景的时间安排和节奏控制

### 对话生成

对话生成阶段为角色创建符合情境和性格的对话内容。

```python
# 对话生成示例
async def generate_dialogues(project):
    config_manager = ConfigManager()
    generator = ScriptGenerator(config_manager)
    
    # 生成对话
    project = await generator.generate_dialogues(project)
    
    return project
```

对话生成包含：

- **角色对话**：每个角色的具体对话内容
- **情感表达**：对话中的情感和语气
- **互动模式**：角色间的互动和对话模式
- **情节推进**：通过对话推动情节发展

### 剧本导出与管理

生成的剧本可以导出为多种格式，便于后续使用和分享。

```python
# 剧本导出示例
async def export_script(project_id, format="json"):
    config_manager = ConfigManager()
    generator = ScriptGenerator(config_manager)
    
    # 加载项目
    project = await generator.load_project(project_id)
    
    # 导出剧本
    if format == "json":
        script_json = await generator.export_to_json(project_id)
        return script_json
    elif format == "markdown":
        script_md = await generator.export_to_markdown(project_id)
        return script_md
    elif format == "txt":
        script_txt = await generator.export_to_txt(project_id)
        return script_txt
```

---

## 视频制作流程

### 视频制作工作流

视频制作工作流包含以下主要步骤：

1. **剧本准备**：加载或生成剧本数据
2. **媒体生成**：根据剧本生成图像、视频和音频素材
3. **视频合成**：将各种媒体素材合成为最终视频
4. **后期处理**：添加字幕、特效等后期处理

### 完整视频制作

```python
# 完整视频制作示例
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

async def produce_video():
    # 初始化系统
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    # 创建剧本数据
    script_data = ScriptData(
        script_id="script_tang_taizong",
        title="贞观之治",
        theme="唐太宗治国理政",
        era="唐朝",
        summary="讲述唐太宗李世民在贞观年间的治国故事",
        characters=[],
        scenes=[],
        dialogues=[],
        media_cues=[],
        total_duration=180
    )
    
    # 执行工作流
    result = await workflow.execute_workflow(script_data)
    
    if result.status == 'completed':
        print("视频制作完成!")
        print(f"执行ID: {result.execution_id}")
        print(f"总成本: ${result.total_cost:.4f}")
        print(f"完成步骤: {len(result.completed_steps)}")
    else:
        print(f"视频制作失败: {result.error_message}")
```

### 基于剧本制作视频

如果已有剧本数据，可以直接基于剧本制作视频，跳过剧本生成步骤。

```python
# 基于剧本制作视频示例
async def produce_from_script(script_id):
    # 初始化系统
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    # 获取剧本数据
    script_manager = ScriptManager(config_manager)
    script_data = await script_manager.get_script_for_video_production(script_id)
    
    if not script_data:
        print("找不到剧本或剧本未完成")
        return
    
    # 执行视频制作工作流
    execution = await workflow.execute_video_production_workflow(script_data)
    
    if execution.status.value == "completed":
        print("视频制作完成!")
        
        # 显示输出信息
        final_results = execution.step_results.get('finalize_output', {})
        if final_results.get('project_directory'):
            print(f"项目目录: {final_results['project_directory']}")
        
        compose_results = execution.step_results.get('compose_video', {})
        if compose_results.get('final_video_path'):
            print(f"视频文件: {compose_results['final_video_path']}")
    else:
        print(f"视频制作失败，状态: {execution.status.value}")
        if execution.error_message:
            print(f"错误信息: {execution.error_message}")
```

### 媒体生成详解

#### 图像生成

图像生成使用AI模型根据剧本描述生成场景图像。

```python
# 图像生成示例
from adapters.image.google_adapter import GoogleImageAdapter
from adapters.base import AdapterConfig

async def generate_images():
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    # 初始化图像适配器
    google_api_key = config_manager.get_api_key("google")
    image_config = AdapterConfig(
        service_name="google_image",
        api_key=google_api_key,
        model="gemini-2.0-flash-exp",
        max_retries=3
    )
    
    image_adapter = GoogleImageAdapter(image_config, config_manager, cost_controller)
    
    # 生成图像
    prompt = "唐朝太极殿内，李世民身着龙袍坐在龙椅上，威严庄重"
    result = await image_adapter.generate(prompt)
    
    if result.success:
        print(f"图像生成成功: {result.data.get('image_path')}")
    else:
        print(f"图像生成失败: {result.error}")
```

#### 视频生成

视频生成使用AI模型根据剧本描述生成动态视频片段。

```python
# 视频生成示例
from adapters.video.kling_adapter import KlingVideoAdapter
from adapters.base import AdapterConfig

async def generate_videos():
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    # 初始化视频适配器
    kling_api_key = config_manager.get_api_key("kling")
    video_config = AdapterConfig(
        service_name="kling",
        api_key=kling_api_key,
        model="kling-v1",
        max_retries=3
    )
    
    video_adapter = KlingVideoAdapter(video_config, config_manager, cost_controller)
    
    # 生成视频
    prompt = "唐朝太极殿内，李世民身着龙袍坐在龙椅上，威严庄重"
    result = await video_adapter.generate(prompt)
    
    if result.success:
        print(f"视频生成成功: {result.data.get('video_path')}")
    else:
        print(f"视频生成失败: {result.error}")
```

#### 语音合成

语音合成将剧本中的对话转换为语音文件。

```python
# 语音合成示例
from adapters.voice.edge_tts_adapter import EdgeTTSAdapter
from adapters.base import AdapterConfig

async def generate_speech():
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    # 初始化语音适配器
    voice_config = AdapterConfig(
        service_name="edgetts",
        api_key="free",  # EdgeTTS不需要API密钥
        model="zh-CN-XiaoxiaoNeural",
        max_retries=3
    )
    
    voice_adapter = EdgeTTSAdapter(voice_config, config_manager, cost_controller)
    
    # 生成语音
    text = "众爱卿，今日朝会，有何要事奏报？"
    result = await voice_adapter.generate(text)
    
    if result.success:
        print(f"语音生成成功: {result.data.get('audio_path')}")
    else:
        print(f"语音生成失败: {result.error}")
```

### 视频合成与后期处理

视频合成将各种媒体素材组合成最终视频文件。

```python
# 视频合成示例
async def compose_video():
    # 这里是视频合成的示例代码
    # 实际实现可能使用FFmpeg或其他视频处理库
    
    # 假设我们有以下素材
    video_clips = ["scene1.mp4", "scene2.mp4", "scene3.mp4"]
    audio_tracks = ["dialogue1.mp3", "dialogue2.mp3", "background_music.mp3"]
    subtitle_files = ["subtitles.srt"]
    
    # 合成视频
    # 实际实现会调用视频处理库
    final_video_path = "final_video.mp4"
    
    print(f"视频合成完成: {final_video_path}")
    
    return final_video_path
```

---

## 系统架构

### 整体架构

Producer系统采用模块化、分层架构设计，确保各组件职责清晰、易于维护和扩展。

```
+---------------------+
|     CLI/API接口      |
+----------+----------+
           |
+----------v----------+
|    工作流引擎核心     |
+----------+----------+
           |
+----------v----------+
|   配置管理 | 成本控制   |
+----------+----------+
           |
+----------v----------+
|      适配器层        |
|  +----------------+  |
|  | 文本生成适配器  |  |
|  +----------------+  |
|  | 图像生成适配器  |  |
|  +----------------+  |
|  | 视频生成适配器  |  |
|  +----------------+  |
|  | 语音合成适配器  |  |
|  +----------------+  |
+---------------------+
```

### 核心组件

#### 1. 工作流引擎 (WorkflowEngine)

工作流引擎是系统的核心，负责协调整个视频制作流程。它将复杂的制作过程分解为多个阶段，并按顺序执行。

- **职责**：管理制作流程、调度各阶段任务、处理异常情况
- **主要类**：`WorkflowEngine`
- **关键特性**：
  - 支持自定义工作流步骤
  - 异步执行提高效率
  - 错误处理和重试机制

```python
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController

# 初始化工作流引擎
config_manager = ConfigManager()
cost_controller = CostController(config_manager)
workflow = WorkflowEngine(config_manager, cost_controller)

# 执行工作流
result = await workflow.execute_workflow(script_data)
```

#### 2. 剧本生成器 (ScriptGenerator)

剧本生成器负责创建和管理剧本项目，支持大纲生成、场景详细化和对话生成。

- **职责**：创建剧本项目、生成剧本内容、管理剧本数据
- **主要类**：`ScriptGenerator`, `ScriptManager`
- **关键特性**：
  - 分阶段剧本生成
  - 多格式导出支持
  - 项目状态管理

```python
from core.script_generator import ScriptGenerator
from core.config import ConfigManager

# 初始化剧本生成器
config_manager = ConfigManager()
generator = ScriptGenerator(config_manager)

# 创建项目
project = await generator.create_project(
    title="贞观之治",
    theme="唐太宗治国理政",
    era="唐朝",
    target_duration=180
)

# 生成大纲
project = await generator.generate_outline(project)
```

#### 3. 配置管理 (ConfigManager)

配置管理模块负责系统的配置加载、验证和管理，支持多种配置源和环境。

- **职责**：加载配置、管理API密钥、提供配置查询接口
- **主要类**：`ConfigManager`
- **关键特性**：
  - 支持YAML配置文件
  - 环境变量覆盖
  - 配置验证和默认值

```python
from core.config import ConfigManager

# 初始化配置管理器
config_manager = ConfigManager(config_path="./config/config.yaml")

# 获取API密钥
api_key = config_manager.get_api_key("google")

# 获取模型配置
model_config = config_manager.get_model_config("text_generation")
```

#### 4. 成本控制 (CostController)

成本控制模块负责监控和管理API调用成本，确保在预算范围内运行。

- **职责**：跟踪API调用成本、管理预算、提供成本报告
- **主要类**：`CostController`
- **关键特性**：
  - 实时成本监控
  - 预算限制和警告
  - 成本优化建议

```python
from core.cost_control import CostController
from core.config import ConfigManager

# 初始化成本控制器
config_manager = ConfigManager()
cost_controller = CostController(config_manager)

# 获取预算状态
budget_status = cost_controller.get_budget_status()

# 检查是否超出预算
is_over = cost_controller.is_over_budget()
```

### 适配器系统

适配器系统提供了与各种AI服务的统一接口，支持文本生成、图像生成、视频生成和语音合成。

#### 1. 文本生成适配器

文本生成适配器负责与各种LLM服务交互，生成剧本内容。

```python
from adapters.text.glm_adapter import LangChainGLMAdapter
from adapters.base import AdapterConfig

# 初始化文本适配器
config = AdapterConfig(
    service_name="glm",
    api_key="your_api_key",
    model="glm-4",
    max_retries=3
)

adapter = LangChainGLMAdapter(config, config_manager, cost_controller)

# 生成文本
result = await adapter.generate("请生成一个关于唐朝的历史故事")
```

#### 2. 图像生成适配器

图像生成适配器负责与各种图像生成服务交互，根据剧本描述生成图像。

```python
from adapters.image.google_adapter import GoogleImageAdapter
from adapters.base import AdapterConfig

# 初始化图像适配器
config = AdapterConfig(
    service_name="google_image",
    api_key="your_api_key",
    model="gemini-2.0-flash-exp",
    max_retries=3
)

adapter = GoogleImageAdapter(config, config_manager, cost_controller)

# 生成图像
result = await adapter.generate("唐朝太极殿内，李世民身着龙袍坐在龙椅上")
```

#### 3. 视频生成适配器

视频生成适配器负责与各种视频生成服务交互，根据剧本描述生成视频片段。

```python
from adapters.video.kling_adapter import KlingVideoAdapter
from adapters.base import AdapterConfig

# 初始化视频适配器
config = AdapterConfig(
    service_name="kling",
    api_key="your_api_key",
    model="kling-v1",
    max_retries=3
)

adapter = KlingVideoAdapter(config, config_manager, cost_controller)

# 生成视频
result = await adapter.generate("唐朝太极殿内，李世民身着龙袍坐在龙椅上")
```

#### 4. 语音合成适配器

语音合成适配器负责与各种语音合成服务交互，将剧本对话转换为语音。

```python
from adapters.voice.edge_tts_adapter import EdgeTTSAdapter
from adapters.base import AdapterConfig

# 初始化语音适配器
config = AdapterConfig(
    service_name="edgetts",
    api_key="free",  # EdgeTTS不需要API密钥
    model="zh-CN-XiaoxiaoNeural",
    max_retries=3
)

adapter = EdgeTTSAdapter(config, config_manager, cost_controller)

# 生成语音
result = await adapter.generate("众爱卿，今日朝会，有何要事奏报？")
```

### 数据模型

系统使用Pydantic定义了核心数据模型，确保数据的一致性和类型安全。

#### 1. 剧本数据 (ScriptData)

```python
from core.models import ScriptData

script_data = ScriptData(
    script_id="script_001",
    title="贞观之治",
    theme="唐太宗治国理政",
    era="唐朝",
    summary="讲述唐太宗李世民在贞观年间的治国故事",
    characters=[],
    scenes=[],
    dialogues=[],
    media_cues=[],
    total_duration=180
)
```

#### 2. 角色数据 (CharacterData)

```python
from core.models import CharacterData

character = CharacterData(
    name="李世民",
    title="唐太宗",
    description="唐朝第二位皇帝，贞观之治的开创者",
    personality=["英明", "果断", "仁慈"],
    appearance="中年男子，身材魁梧，面容威严，身着龙袍",
    voice_style="威严",
    era="唐朝",
    social_status="皇帝"
)
```

#### 3. 场景数据 (SceneData)

```python
from core.models import SceneData

scene = SceneData(
    scene_id="scene_001",
    title="太极殿朝会",
    location="太极殿",
    time_period="清晨",
    weather="晴朗",
    atmosphere="庄严肃穆",
    background_description="金碧辉煌的太极殿内，文武百官整齐列队",
    lighting="宫殿内的烛光和晨光",
    camera_angle="wide",
    duration=8.0
)
```

#### 4. 对话数据 (DialogueData)

```python
from core.models import DialogueData

dialogue = DialogueData(
    speaker="李世民",
    content="众爱卿，今日朝会，有何要事奏报？",
    emotion="威严",
    voice_style="imperial",
    emphasis=["众爱卿", "要事"]
)
```

---

## 配置管理

### 配置文件结构

系统使用YAML格式的配置文件，支持分层配置和环境特定设置。

```yaml
# config/config.yaml
system:
  mode: "hybrid_optimized"  # 运行模式
  debug: false              # 调试模式
  log_level: "INFO"         # 日志级别

# 文本生成配置
text_generation:
  primary_model: "glm-4"                    # 主要模型
  backup_models: ["gpt-3.5-turbo", "claude-3-sonnet"]  # 备用模型
  max_tokens: 4000                          # 最大令牌数
  temperature: 0.7                          # 温度参数
  top_p: 0.9                                # Top-p参数
  frequency_penalty: 0.0                    # 频率惩罚
  presence_penalty: 0.0                     # 存在惩罚

# 图像生成配置
image_generation:
  primary_service: "google"                 # 主要服务
  google:
    model: "gemini-2.0-flash-exp"           # 模型名称
    max_images: 4                           # 最大图像数
    size: "1024x1024"                       # 图像尺寸
    quality: "high"                         # 图像质量

# 视频生成配置
video_generation:
  primary_service: "kling"                  # 主要服务
  model: "kling-v1"                         # 模型名称
  max_duration: 30                          # 最大时长（秒）
  quality: "high"                           # 视频质量

# 语音合成配置
voice_synthesis:
  primary_service: "edgetts"                # 主要服务
  voice_id: "zh-CN-XiaoxiaoNeural"          # 语音ID
  speed: 1.0                                # 语速
  pitch: 1.0                                # 音调
  volume: 1.0                               # 音量

# 成本控制配置
cost_control:
  daily_budget: 5.0                         # 日预算（美元）
  monthly_budget: 100.0                     # 月预算（美元）
  enable_alerts: true                       # 启用警告
  alert_threshold: 0.8                      # 警告阈值（80%）
  cost_tracking: true                       # 成本跟踪

# 输出配置
output:
  base_dir: "./output"                      # 基础输出目录
  script_dir: "scripts"                     # 剧本输出子目录
  video_dir: "videos"                       # 视频输出子目录
  temp_dir: "temp"                          # 临时文件目录
  log_dir: "logs"                           # 日志目录

# API密钥配置（也可以通过环境变量设置）
api_keys:
  google: "your_google_api_key"             # Google API密钥
  openai: "your_openai_api_key"             # OpenAI API密钥
  kling: "your_kling_api_key"               # Kling API密钥
```

### 环境变量配置

系统支持通过环境变量覆盖配置文件中的设置，便于在不同环境中灵活配置。

```bash
# 系统配置
export PRODUCER_MODE="hybrid_optimized"
export PRODUCER_DEBUG="false"
export PRODUCER_LOG_LEVEL="INFO"

# API密钥
export GOOGLE_AI_API_KEY="your_google_api_key"
export OPENAI_API_KEY="your_openai_api_key"
export KLING_API_KEY="your_kling_api_key"

# 成本控制
export PRODUCER_DAILY_BUDGET="5.0"
export PRODUCER_MONTHLY_BUDGET="100.0"

# 输出目录
export PRODUCER_OUTPUT_DIR="./output"
```

### 配置管理器使用

配置管理器提供了统一的接口来访问和管理配置。

```python
from core.config import ConfigManager

# 初始化配置管理器
config_manager = ConfigManager(config_path="./config/config.yaml")

# 获取系统配置
system_config = config_manager.get_system_config()
print(f"运行模式: {system_config.mode}")
print(f"调试模式: {system_config.debug}")

# 获取API密钥
google_api_key = config_manager.get_api_key("google")
openai_api_key = config_manager.get_api_key("openai")

# 获取模型配置
text_config = config_manager.get_model_config("text_generation")
print(f"主要模型: {text_config.primary_model}")
print(f"备用模型: {text_config.backup_models}")

# 获取输出目录
output_dir = config_manager.get_output_dir()
script_dir = config_manager.get_script_output_dir()
video_dir = config_manager.get_video_output_dir()
```

### 动态配置更新

配置管理器支持动态更新配置，无需重启系统。

```python
# 更新配置
config_manager.update_config({
    "text_generation": {
        "primary_model": "gpt-4",
        "temperature": 0.8
    }
})

# 重新加载配置文件
config_manager.reload_config()
```

### 配置验证

配置管理器会对配置进行验证，确保配置的正确性和完整性。

```python
# 验证配置
is_valid, errors = config_manager.validate_config()
if not is_valid:
    print("配置验证失败:")
    for error in errors:
        print(f"  - {error}")
```

---

## 高级用法

### 自定义工作流

系统支持自定义工作流，可以根据需求添加、修改或删除工作流步骤。

```python
from core.workflow import WorkflowEngine, WorkflowStep, WorkflowStage
from core.config import ConfigManager
from core.cost_control import CostController

# 初始化工作流引擎
config_manager = ConfigManager()
cost_controller = CostController(config_manager)
workflow = WorkflowEngine(config_manager, cost_controller)

# 定义自定义步骤
async def custom_step(script_data, context):
    """自定义工作流步骤"""
    print("执行自定义步骤...")
    # 自定义逻辑
    return {"success": True, "data": "custom_result"}

# 注册自定义步骤
custom_step_obj = WorkflowStep(
    step_id="custom_step",
    name="自定义步骤",
    stage=WorkflowStage.MEDIA_GENERATION,
    handler=custom_step,
    dependencies=["script_generation"],
    estimated_duration=5.0,
    estimated_cost=0.1,
    priority=1
)

workflow.register_step(custom_step_obj)

# 执行工作流
result = await workflow.execute_workflow(script_data)
```

### 批量处理

系统支持批量处理多个剧本或视频制作任务。

```python
import asyncio
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

async def batch_produce():
    # 初始化系统
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    # 批量剧本数据
    batch_scripts = [
        {
            "title": "唐朝文化",
            "theme": "文化繁荣",
            "era": "唐朝",
            "duration": 180
        },
        {
            "title": "宋朝科技",
            "theme": "科技发展",
            "era": "宋朝",
            "duration": 200
        }
    ]
    
    # 批量处理
    results = []
    for script_info in batch_scripts:
        script_data = ScriptData(
            script_id=f"script_{script_info['title'].replace(' ', '_')}",
            title=script_info['title'],
            theme=script_info['theme'],
            era=script_info['era'],
            summary=f"一个关于{script_info['era']}{script_info['theme']}的历史短剧",
            characters=[],
            scenes=[],
            dialogues=[],
            media_cues=[],
            total_duration=script_info['duration']
        )
        
        result = await workflow.execute_workflow(script_data)
        results.append(result)
        
        print(f"完成制作: {script_info['title']}, 状态: {result.status}")
    
    return results

# 运行批量处理
asyncio.run(batch_produce())
```

### 自定义适配器

系统支持自定义适配器，可以集成新的AI服务或修改现有适配器的行为。

```python
from adapters.base import BaseAdapter, AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController

class CustomTextAdapter(BaseAdapter):
    """自定义文本生成适配器"""
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        # 初始化自定义适配器
        self.api_client = self._init_api_client()
    
    def _init_api_client(self):
        """初始化API客户端"""
        # 自定义API客户端初始化逻辑
        return None
    
    async def generate(self, prompt: str, **kwargs) -> AdapterResult:
        """生成文本"""
        try:
            # 自定义文本生成逻辑
            response = await self.api_client.generate_text(prompt, **kwargs)
            
            # 记录成本
            await self._record_cost(response)
            
            return AdapterResult(
                success=True,
                data={"text": response.text},
                cost=response.cost,
                metadata={"model": self.config.model}
            )
        except Exception as e:
            return AdapterResult(
                success=False,
                error=str(e),
                metadata={"model": self.config.model}
            )

# 使用自定义适配器
config = AdapterConfig(
    service_name="custom_text",
    api_key="your_api_key",
    model="custom-model",
    max_retries=3
)

adapter = CustomTextAdapter(config, config_manager, cost_controller)
result = await adapter.generate("请生成一个关于唐朝的历史故事")
```

### 成本优化

系统提供了多种成本优化策略，帮助控制API调用成本。

```python
from core.cost_control import CostController
from core.config import ConfigManager

# 初始化成本控制器
config_manager = ConfigManager()
cost_controller = CostController(config_manager)

# 设置预算
cost_controller.set_daily_budget(5.0)
cost_controller.set_monthly_budget(100.0)

# 获取成本统计
cost_stats = cost_controller.get_cost_statistics()
print(f"今日已用: ${cost_stats.daily_used:.4f}")
print(f"本月已用: ${cost_stats.monthly_used:.4f}")

# 获取成本优化建议
optimization_tips = cost_controller.get_optimization_tips()
for tip in optimization_tips:
    print(f"- {tip}")

# 模型选择优化
def optimize_model_selection(task_type, complexity):
    """根据任务类型和复杂度选择最优模型"""
    if task_type == "outline_generation":
        if complexity == "low":
            return "gpt-3.5-turbo"  # 简单大纲使用低成本模型
        else:
            return "glm-4"          # 复杂大纲使用高质量模型
    elif task_type == "dialogue_generation":
        return "claude-3-sonnet"    # 对话生成使用适合的模型
    else:
        return "glm-4"              # 默认使用高质量模型
```

### 质量控制

系统提供了多种质量控制机制，确保生成内容的质量。

```python
from core.evaluators import HistoricalStoryEvaluator, GenericEvaluator
from core.config import ConfigManager

# 初始化评估器
config_manager = ConfigManager()
story_evaluator = HistoricalStoryEvaluator(config_manager)
generic_evaluator = GenericEvaluator(config_manager)

# 评估剧本质量
async def evaluate_script_quality(script_data):
    """评估剧本质量"""
    # 历史准确性评估
    historical_score = await story_evaluator.evaluate_historical_accuracy(script_data)
    
    # 故事连贯性评估
    coherence_score = await generic_evaluator.evaluate_coherence(script_data)
    
    # 角色一致性评估
    consistency_score = await generic_evaluator.evaluate_character_consistency(script_data)
    
    # 综合评分
    overall_score = (historical_score + coherence_score + consistency_score) / 3
    
    return {
        "historical_accuracy": historical_score,
        "coherence": coherence_score,
        "character_consistency": consistency_score,
        "overall_score": overall_score
    }

# 质量改进建议
async def get_quality_improvements(script_data):
    """获取质量改进建议"""
    improvements = await story_evaluator.get_improvement_suggestions(script_data)
    return improvements
```

### 质量诊断工具

系统提供了质量诊断工具，帮助识别和解决生成内容中的质量问题。

```python
async def diagnose_quality(self, script_data: ScriptData) -> Dict[str, Any]:
    """质量诊断"""
    issues = []
    
    # 检查角色一致性
    character_names = [c.name for c in script_data.characters]
    if len(set(character_names)) != len(character_names):
        issues.append("角色名称重复")
    
    # 检查场景时长
    scene_durations = [s.duration for s in script_data.scenes]
    if any(d < 10 or d > 60 for d in scene_durations):
        issues.append("场景时长不合理")
    
    return {
        "quality_score": max(0, 100 - len(issues) * 20),
        "issues": issues,
        "suggestions": self._generate_suggestions(issues)
    }
```

### 自动优化机制

系统支持自动优化机制，可以根据质量评估结果自动改进生成内容。

```python
async def auto_optimize(self, script_data: ScriptData) -> ScriptData:
    """自动优化剧本"""
    validation_result = self.validator.validate_outline(script_data)
    
    if validation_result.quality_score < 80:
        # 触发自动优化
        optimization_prompt = self._build_optimization_prompt(
            script_data, validation_result.issues
        )
        
        optimized_content = await self.llm_adapter.generate_async(
            optimization_prompt
        )
        
        return self._merge_optimizations(script_data, optimized_content)
    
    return script_data
```

### 质量优化配置

可以通过配置文件设置质量优化参数：

```yaml
quality_enhancement:
  enable_multi_round_generation: true
  character_consistency_check: true
  historical_accuracy_validation: true
  
  refinement_prompts:
    character_development: "请深化角色性格特点和背景故事"
    scene_enhancement: "请丰富场景的视觉描述和氛围营造"
    dialogue_improvement: "请优化对话的自然度和情感表达"
```

### 监控与分析

系统提供了全面的监控与分析功能，帮助用户了解系统运行状态和生成内容的质量。

#### 性能指标监控

```python
class PerformanceMonitor:
    def track_generation_metrics(self, task_type: str, duration: float, cost: float):
        """跟踪生成指标"""
        metrics = {
            "task_type": task_type,
            "duration_seconds": duration,
            "cost_usd": cost,
            "timestamp": datetime.now().isoformat(),
            "tokens_per_second": self.calculate_token_rate(duration),
            "cost_per_token": cost / self.total_tokens if self.total_tokens > 0 else 0
        }
        
        self.metrics_store.append(metrics)
        
        # 实时告警
        if cost > self.cost_threshold:
            self.send_cost_alert(cost, task_type)
```

#### 质量分析报告

```python
def generate_quality_report(self, project_id: str) -> Dict[str, Any]:
    """生成质量分析报告"""
    project = self.load_project(project_id)
    
    return {
        "project_info": {
            "title": project.title,
            "total_cost": project.total_cost,
            "generation_time": project.total_duration
        },
        "quality_metrics": {
            "character_consistency": self._check_character_consistency(project),
            "scene_coherence": self._check_scene_coherence(project),
            "dialogue_naturalness": self._check_dialogue_quality(project)
        },
        "optimization_suggestions": self._generate_optimization_suggestions(project),
        "cost_breakdown": self._analyze_cost_distribution(project)
    }
```

#### 实时告警机制

系统支持实时告警机制，可以在出现问题时及时通知用户。

```python
class AlertManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.alert_thresholds = config_manager.get_alert_thresholds()
    
    async def check_and_send_alerts(self):
        """检查并发送告警"""
        # 检查预算使用情况
        budget_status = self.cost_controller.get_budget_status()
        if budget_status.daily_usage_percentage > self.alert_thresholds['budget']:
            await self.send_budget_alert(budget_status)
        
        # 检查API错误率
        error_rate = self.calculate_api_error_rate()
        if error_rate > self.alert_thresholds['error_rate']:
            await self.send_error_rate_alert(error_rate)
        
        # 检查生成质量
        quality_scores = self.get_recent_quality_scores()
        if quality_scores and quality_scores[-1] < self.alert_thresholds['quality']:
            await self.send_quality_alert(quality_scores[-1])
    
    async def send_budget_alert(self, budget_status):
        """发送预算告警"""
        message = f"预算使用告警: 日预算已使用 {budget_status.daily_usage_percentage:.1%}"
        await self.send_notification("budget_alert", message)
    
    async def send_error_rate_alert(self, error_rate):
        """发送错误率告警"""
        message = f"API错误率告警: 当前错误率为 {error_rate:.1%}"
        await self.send_notification("error_rate_alert", message)
    
    async def send_quality_alert(self, quality_score):
        """发送质量告警"""
        message = f"生成质量告警: 最近质量评分为 {quality_score:.1f}"
        await self.send_notification("quality_alert", message)
```

### 案例研究

#### 案例1：唐朝短剧制作

##### 业务场景

制作一部以唐朝为背景的短剧，讲述诗人与歌姬的邂逅故事。

##### 制作流程

```bash
# 1. 创建剧本项目
uv run python script_cli.py create \
  --title "长安月下" \
  --theme "诗人与歌姬的邂逅" \
  --era "唐朝" \
  --duration 60

# 2. 生成大纲
uv run python script_cli.py outline <project_id>

# 3. 细化场景
uv run python script_cli.py scenes <project_id>

# 4. 生成对话
uv run python script_cli.py dialogue <project_id>

# 5. 导出剧本
uv run python script_cli.py export <project_id>
```

##### 关键优化点

**1. 提示词优化**：

```python
TANG_DYNASTY_PROMPT = """
背景设定：唐朝开元盛世，长安城繁华似锦

角色设定：
- 男主：年轻诗人，才华横溢但家境贫寒
- 女主：青楼歌姬，美貌与才艺并重

情感主线：
- 第一眼：诗词相遇，心生好感
- 深入：才华相惜，情愫暗生  
- 冲突：身份差距，现实阻碍
- 升华：真情超越，携手未来

语言风格：
- 对话要有唐诗韵味，但不过于文言
- 融入唐朝特色词汇：长安、青楼、诗会等
- 情感表达要含蓄而深刻
"""
```

**2. 成本控制**：

```yaml
# 针对性配置
cost_optimization:
  outline_model: "glm-4-flash"      # 大纲用经济模型
  dialogue_model: "claude-3-haiku"  # 对话用平衡模型
  max_iterations: 2                 # 限制优化轮次
  batch_processing: true            # 批量处理场景
```

##### 制作结果

- **总耗时**：8分钟
- **总成本**：$3.2
- **质量评分**：8.5/10
- **用户反馈**：情节紧凑，对话自然，历史感强

#### 案例2：批量历史短剧制作

##### 业务场景

某内容公司需要批量制作10部历史短剧，涵盖不同朝代和主题。

##### 批量配置

```json
{
  "batch_scripts": [
    {
      "title": "汉宫春晓",
      "theme": "宫廷爱情与权谋",
      "era": "汉朝",
      "duration": 90
    },
    {
      "title": "宋词情缘", 
      "theme": "文人墨客的浪漫",
      "era": "宋朝",
      "duration": 75
    },
    {
      "title": "明月清风",
      "theme": "江湖侠客与红颜",
      "era": "明朝", 
      "duration": 120
    }
  ],
  "global_config": {
    "quality_level": "medium",
    "budget_per_script": 5.0,
    "parallel_processing": 3
  }
}
```

##### 批量执行

```python
async def batch_production(config_file: str):
    """批量剧本制作"""
    with open(config_file) as f:
        config = json.load(f)
    
    tasks = []
    for script_config in config["batch_scripts"]:
        task = create_script_project(**script_config)
        tasks.append(task)
    
    # 并发执行，控制并发数
    semaphore = asyncio.Semaphore(config["global_config"]["parallel_processing"])
    
    async def process_with_limit(task):
        async with semaphore:
            return await task
    
    results = await asyncio.gather(*[process_with_limit(task) for task in tasks])
    return results
```

##### 制作效果

- **总耗时**：45分钟（10部剧本）
- **平均成本**：$4.2/部
- **成功率**：100%
- **质量一致性**：标准差<0.5

### 扩展功能

系统支持多种扩展功能，可以根据需求添加新的功能模块。

```python
# 添加自定义元数据
from core.models import ScriptData

script_data = ScriptData(
    script_id="script_001",
    title="贞观之治",
    theme="唐太宗治国理政",
    era="唐朝",
    summary="讲述唐太宗李世民在贞观年间的治国故事",
    characters=[],
    scenes=[],
    dialogues=[],
    media_cues=[],
    total_duration=180,
    # 添加自定义元数据
    metadata={
        "genre": "历史剧",
        "target_audience": "成人",
        "language": "中文",
        "custom_tags": ["唐朝", "李世民", "贞观之治"]
    }
)

# 自定义提示词模板
from core.chains.outline_chain import OutlineChain

# 创建自定义大纲链
class CustomOutlineChain(OutlineChain):
    """自定义大纲生成链"""
    
    def get_prompt_template(self):
        """获取自定义提示词模板"""
        return """
        请为以下剧本生成一个详细大纲：
        
        标题：{title}
        主题：{theme}
        时代：{era}
        目标时长：{duration}秒
        
        请特别关注历史准确性，并包含以下内容：
        1. 剧情摘要
        2. 主要角色（包括历史背景）
        3. 场景列表（包括历史地点）
        4. 关键历史事件
        
        请以JSON格式返回。
        """

# 使用自定义链
config_manager = ConfigManager()
outline_chain = CustomOutlineChain(config_manager)
result = await outline_chain.generate_outline({
    "title": "贞观之治",
    "theme": "唐太宗治国理政",
    "era": "唐朝",
    "duration": 180
})
```

---

## 故障排除与最佳实践

### 常见问题与解决方案

#### 1. API密钥问题

**问题**：API密钥配置错误或无效

**症状**：
- 生成内容时出现认证错误
- 系统提示API密钥无效

**解决方案**：
```bash
# 检查环境变量
echo $GOOGLE_AI_API_KEY
echo $OPENAI_API_KEY
echo $KLING_API_KEY

# 重新设置API密钥
export GOOGLE_AI_API_KEY="your_correct_google_api_key"
export OPENAI_API_KEY="your_correct_openai_api_key"
export KLING_API_KEY="your_correct_kling_api_key"

# 或在配置文件中设置
# config/config.yaml
api_keys:
  google: "your_correct_google_api_key"
  openai: "your_correct_openai_api_key"
  kling: "your_correct_kling_api_key"
```

#### 2. 生成质量问题

**问题**：生成的内容质量不满足要求

**症状**：
- 剧本内容不连贯
- 历史准确性不足
- 图像/视频质量低

**解决方案**：
```python
# 调整模型参数
from core.config import ConfigManager

config_manager = ConfigManager()

# 更新文本生成配置
config_manager.update_config({
    "text_generation": {
        "temperature": 0.5,  # 降低温度以减少随机性
        "primary_model": "gpt-4"  # 使用更高质量的模型
    }
})

# 更新图像生成配置
config_manager.update_config({
    "image_generation": {
        "google": {
            "quality": "high",  # 提高图像质量
            "size": "1024x1024"  # 提高图像分辨率
        }
    }
})

# 使用评估器检查质量
from core.evaluators import HistoricalStoryEvaluator

evaluator = HistoricalStoryEvaluator(config_manager)
quality_score = await evaluator.evaluate_historical_accuracy(script_data)
print(f"历史准确性评分: {quality_score}")

# 获取改进建议
improvements = await evaluator.get_improvement_suggestions(script_data)
for suggestion in improvements:
    print(f"改进建议: {suggestion}")
```

#### 3. 成本超限问题

**问题**：API调用成本超出预算

**症状**：
- 系统提示预算不足
- 生成任务被中断

**解决方案**：
```python
from core.cost_control import CostController
from core.config import ConfigManager

config_manager = ConfigManager()
cost_controller = CostController(config_manager)

# 检查预算状态
budget_status = cost_controller.get_budget_status()
print(f"日预算剩余: ${budget_status.daily_remaining:.4f}")
print(f"月预算剩余: ${budget_status.monthly_remaining:.4f}")

# 调整预算
cost_controller.set_daily_budget(10.0)  # 增加日预算
cost_controller.set_monthly_budget(200.0)  # 增加月预算

# 获取成本优化建议
optimization_tips = cost_controller.get_optimization_tips()
for tip in optimization_tips:
    print(f"优化建议: {tip}")

# 使用更经济的模型
config_manager.update_config({
    "text_generation": {
        "primary_model": "gpt-3.5-turbo",  # 使用更经济的模型
        "backup_models": ["glm-4"]
    }
})
```

#### 4. 工作流执行问题

**问题**：工作流执行失败或卡住

**症状**：
- 工作流长时间无响应
- 某些步骤执行失败

**解决方案**：
```python
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController

config_manager = ConfigManager()
cost_controller = CostController(config_manager)
workflow = WorkflowEngine(config_manager, cost_controller)

# 检查工作流状态
status = workflow.get_workflow_status()
print(f"当前状态: {status}")

# 获取失败步骤
failed_steps = workflow.get_failed_steps()
for step in failed_steps:
    print(f"失败步骤: {step.step_id}, 错误: {step.error_message}")

# 重试失败步骤
for step in failed_steps:
    await workflow.retry_step(step.step_id)

# 或者重新执行整个工作流
result = await workflow.execute_workflow(script_data)
```

### 最佳实践

#### 1. 剧本创作最佳实践

- **明确主题和时代**：在开始创作前，明确剧本的主题和历史时代背景
- **角色设计**：确保角色性格鲜明，符合历史背景
- **场景规划**：合理规划场景数量和时长，确保故事流畅
- **对话质量**：对话应符合角色性格，推动情节发展

```python
# 剧本创作示例
from core.script_generator import ScriptGenerator
from core.config import ConfigManager

async def create_quality_script():
    config_manager = ConfigManager()
    generator = ScriptGenerator(config_manager)
    
    # 创建项目
    project = await generator.create_project(
        title="贞观之治",
        theme="唐太宗治国理政",
        era="唐朝",
        target_duration=180,
        metadata={
            "genre": "历史剧",
            "target_audience": "成人",
            "historical_accuracy": "high"
        }
    )
    
    # 生成大纲
    project = await generator.generate_outline(
        project,
        character_count=3,
        scene_count=5
    )
    
    # 生成场景详情
    project = await generator.generate_scenes(project)
    
    # 生成对话
    project = await generator.generate_dialogues(project)
    
    # 评估质量
    from core.evaluators import HistoricalStoryEvaluator
    evaluator = HistoricalStoryEvaluator(config_manager)
    quality_score = await evaluator.evaluate_historical_accuracy(project.script_data)
    
    if quality_score < 0.8:
        # 获取改进建议
        improvements = await evaluator.get_improvement_suggestions(project.script_data)
        print("质量改进建议:")
        for suggestion in improvements:
            print(f"- {suggestion}")
    
    return project
```

#### 2. 视频制作最佳实践

- **素材质量**：使用高质量的图像、视频和音频素材
- **场景过渡**：确保场景之间的过渡自然流畅
- **音频同步**：确保音频与视频画面同步
- **后期处理**：适当添加字幕、特效等后期处理

```python
# 视频制作示例
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController

async def produce_quality_video():
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    # 使用高质量设置
    config_manager.update_config({
        "image_generation": {
            "google": {
                "quality": "high",
                "size": "1024x1024"
            }
        },
        "video_generation": {
            "quality": "high",
            "max_duration": 30
        },
        "voice_synthesis": {
            "quality": "high"
        }
    })
    
    # 创建剧本数据
    script_data = ScriptData(
        script_id="script_tang_taizong",
        title="贞观之治",
        theme="唐太宗治国理政",
        era="唐朝",
        summary="讲述唐太宗李世民在贞观年间的治国故事",
        characters=[],
        scenes=[],
        dialogues=[],
        media_cues=[],
        total_duration=180
    )
    
    # 执行工作流
    result = await workflow.execute_workflow(script_data)
    
    if result.status == 'completed':
        print("视频制作完成!")
        # 检查输出文件
        final_results = result.step_results.get('finalize_output', {})
        if final_results.get('project_directory'):
            print(f"项目目录: {final_results['project_directory']}")
        
        compose_results = result.step_results.get('compose_video', {})
        if compose_results.get('final_video_path'):
            print(f"视频文件: {compose_results['final_video_path']}")
    else:
        print(f"视频制作失败: {result.error_message}")
```

#### 3. 成本控制最佳实践

- **预算设置**：根据项目需求合理设置日预算和月预算
- **模型选择**：根据任务需求选择合适的模型，平衡质量和成本
- **批量处理**：合理利用批量处理，提高效率
- **成本监控**：定期检查成本使用情况，及时调整策略

```python
# 成本控制示例
from core.cost_control import CostController
from core.config import ConfigManager

async def cost_effective_production():
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    # 设置合理预算
    cost_controller.set_daily_budget(5.0)
    cost_controller.set_monthly_budget(100.0)
    
    # 使用经济型模型配置
    config_manager.update_config({
        "text_generation": {
            "primary_model": "gpt-3.5-turbo",  # 大纲生成使用经济模型
            "backup_models": ["glm-4"]
        },
        "image_generation": {
            "google": {
                "model": "gemini-2.0-flash-exp",  # 使用免费图像模型
                "max_images": 2  # 减少图像数量
            }
        }
    })
    
    # 批量处理多个剧本
    from core.workflow import WorkflowEngine
    from core.models import ScriptData
    
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    batch_scripts = [
        {
            "title": "唐朝文化",
            "theme": "文化繁荣",
            "era": "唐朝",
            "duration": 120  # 减少时长以降低成本
        },
        {
            "title": "宋朝科技",
            "theme": "科技发展",
            "era": "宋朝",
            "duration": 120
        }
    ]
    
    results = []
    for script_info in batch_scripts:
        # 检查预算状态
        budget_status = cost_controller.get_budget_status()
        if budget_status.daily_remaining < 1.0:
            print("日预算不足，停止处理")
            break
        
        script_data = ScriptData(
            script_id=f"script_{script_info['title'].replace(' ', '_')}",
            title=script_info['title'],
            theme=script_info['theme'],
            era=script_info['era'],
            summary=f"一个关于{script_info['era']}{script_info['theme']}的历史短剧",
            characters=[],
            scenes=[],
            dialogues=[],
            media_cues=[],
            total_duration=script_info['duration']
        )
        
        result = await workflow.execute_workflow(script_data)
        results.append(result)
        
        print(f"完成制作: {script_info['title']}, 成本: ${result.total_cost:.4f}")
    
    # 显示成本统计
    cost_stats = cost_controller.get_cost_statistics()
    print(f"总成本: ${cost_stats.daily_used:.4f}")
    print(f"剩余预算: ${budget_status.daily_remaining:.4f}")
    
    return results
```

#### 4. 系统维护最佳实践

- **定期更新**：定期更新系统依赖和模型
- **备份管理**：定期备份重要的配置文件和生成的剧本
- **日志管理**：合理配置日志级别和日志轮转
- **性能监控**：监控系统性能，及时优化

```python
# 系统维护示例
from core.config import ConfigManager
import shutil
from pathlib import Path
from datetime import datetime

def system_maintenance():
    config_manager = ConfigManager()
    
    # 备份配置文件
    config_file = Path("./config/config.yaml")
    backup_dir = Path("./backups")
    backup_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = backup_dir / f"config_{timestamp}.yaml"
    
    shutil.copy2(config_file, backup_file)
    print(f"配置文件已备份到: {backup_file}")
    
    # 备份剧本数据
    scripts_dir = config_manager.get_script_output_dir()
    if scripts_dir.exists():
        scripts_backup = backup_dir / f"scripts_{timestamp}"
        shutil.copytree(scripts_dir, scripts_backup)
        print(f"剧本数据已备份到: {scripts_backup}")
    
    # 清理临时文件
    temp_dir = Path("./temp")
    if temp_dir.exists():
        for file in temp_dir.glob("*"):
            if file.is_file() and datetime.fromtimestamp(file.stat().st_mtime) < datetime.now().replace(hour=0, minute=0, second=0, microsecond=0):
                file.unlink()
        print("临时文件已清理")
    
    # 检查系统状态
    try:
        from core.workflow import WorkflowEngine
        from core.cost_control import CostController
        
        cost_controller = CostController(config_manager)
        workflow = WorkflowEngine(config_manager, cost_controller)
        
        status = workflow.get_workflow_status()
        print(f"系统状态: {status}")
        
        # 检查API连接
        api_keys = ["google", "openai", "kling"]
        for key in api_keys:
            api_key = config_manager.get_api_key(key)
            if api_key:
                print(f"{key} API密钥: 已配置")
            else:
                print(f"{key} API密钥: 未配置")
    except Exception as e:
        print(f"系统检查失败: {e}")
    
    print("系统维护完成")

# 执行系统维护
system_maintenance()
```

### 性能优化

#### 1. 异步处理优化

```python
import asyncio
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController

async def optimized_async_processing():
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    # 并行处理独立任务
    async def process_task(task):
        return await workflow.execute_workflow(task)
    
    # 创建多个任务
    tasks = [
        ScriptData(
            script_id=f"script_{i}",
            title=f"剧本 {i}",
            theme="历史剧情",
            era="唐朝",
            summary=f"第{i}个历史短剧",
            characters=[],
            scenes=[],
            dialogues=[],
            media_cues=[],
            total_duration=60
        )
        for i in range(5)
    ]
    
    # 并行执行
    results = await asyncio.gather(*[process_task(task) for task in tasks])
    
    return results
```

#### 2. 缓存优化

```python
from functools import lru_cache
from core.config import ConfigManager

class CachedConfigManager(ConfigManager):
    """带缓存的配置管理器"""
    
    @lru_cache(maxsize=128)
    def get_api_key(self, service_name: str) -> str:
        """缓存API密钥查询结果"""
        return super().get_api_key(service_name)
    
    @lru_cache(maxsize=32)
    def get_model_config(self, model_type: str) -> dict:
        """缓存模型配置查询结果"""
        return super().get_model_config(model_type)
    
    def clear_cache(self):
        """清除缓存"""
        self.get_api_key.cache_clear()
        self.get_model_config.cache_clear()

# 使用带缓存的配置管理器
config_manager = CachedConfigManager()

# 第一次调用会执行实际查询
api_key = config_manager.get_api_key("google")

# 第二次调用会从缓存返回
api_key_cached = config_manager.get_api_key("google")
```

#### 3. 资源管理优化

```python
import asyncio
from contextlib import asynccontextmanager
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController

@asynccontextmanager
async def workflow_context():
    """工作流上下文管理器"""
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    workflow = WorkflowEngine(config_manager, cost_controller)
    
    try:
        yield workflow
    finally:
        # 清理资源
        await workflow.cleanup()
        await cost_controller.cleanup()

# 使用上下文管理器
async def resource_optimized_processing():
    async with workflow_context() as workflow:
        script_data = ScriptData(
            script_id="script_optimized",
            title="优化处理示例",
            theme="历史剧情",
            era="唐朝",
            summary="资源优化处理示例",
            characters=[],
            scenes=[],
            dialogues=[],
            media_cues=[],
            total_duration=60
        )
        
        result = await workflow.execute_workflow(script_data)
        return result
```

---

## 总结

Producer是一个功能强大、灵活可扩展的历史短剧视频制作系统。通过模块化设计和丰富的配置选项，用户可以根据需求定制自己的视频制作流程。系统提供了完整的剧本生成和视频制作功能，支持多种AI服务和模型，并内置了成本控制和质量保证机制。

无论是个人创作者还是专业团队，都可以通过Producer轻松制作高质量的历史短剧视频。希望本手册能够帮助您更好地使用Producer系统，创作出精彩的作品！

如有任何问题或建议，欢迎通过以下方式联系我们：

- 项目主页：https://github.com/your-repo/producer
- 问题反馈：https://github.com/your-repo/producer/issues
- 邮箱：<EMAIL>

---

**附录：命令参考**

### 主CLI命令

#### 1. 制作完整视频命令

```bash
python -m producer.cli produce \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180 \
  --output ./output \
  --budget 2.0
```

**执行原理：**
此命令是Producer系统的核心功能，它触发完整的历史短剧视频制作工作流。系统首先创建一个工作流引擎实例，然后按照以下步骤执行：
1. 初始化配置管理器和成本控制器
2. 创建剧本数据结构，包含用户提供的主题、时代和时长等信息
3. 执行剧本生成工作流，包括大纲生成、场景详细化和对话生成
4. 执行视频制作工作流，包括图像生成、视频合成和语音合成
5. 应用后期处理效果，生成最终视频文件

整个流程采用异步处理架构，各阶段可以并行执行，提高制作效率。系统会实时监控成本，确保不超过预设预算。

**参数说明：**
- `--title`：指定视频标题，用于生成剧本内容和文件命名
- `--theme`：定义视频主题，影响剧本生成的方向和内容
- `--era`：指定历史时代，系统会根据该时代的历史背景生成相应内容
- `--duration`：设置视频时长（秒），影响剧本场景数量和对话长度
- `--output`：指定输出目录，生成的视频文件将保存在此目录
- `--budget`：设置制作预算（美元），系统会根据预算调整资源使用和模型选择

**流程分解：**
1. **初始化阶段**：加载配置文件，初始化各适配器，验证API密钥
2. **剧本生成阶段**：
   - 大纲生成：基于主题和时代创建故事大纲
   - 场景详细化：将大纲分解为具体场景，包含场景描述和角色动作
   - 对话生成：为每个场景生成角色对话
3. **视频制作阶段**：
   - 图像生成：为每个场景生成视觉图像
   - 视频合成：将静态图像转换为动态视频
   - 语音合成：为对话生成语音
4. **后期处理阶段**：添加背景音乐、字幕和特效，生成最终视频

**最佳实践：**
- 对于复杂主题，建议增加预算以确保内容质量
- 时长设置建议在120-300秒之间，过短可能导致内容不完整，过长会增加成本
- 输出目录应确保有足够的存储空间，一个3分钟视频可能需要数百MB空间
- 首次使用建议先用较小预算测试，确认系统正常工作后再进行完整制作

#### 2. 系统测试命令

```bash
python -m producer.cli test
```

**执行原理：**
此命令执行系统的自检测试，验证所有组件和服务的可用性。测试框架会依次检查：
1. 配置文件加载和验证
2. API密钥有效性检查
3. 各适配器连接测试
4. 核心模块功能测试
5. 端到端工作流测试

测试结果会生成详细报告，包括成功/失败状态、响应时间和错误信息。

**参数说明：**
此命令不接受额外参数，但可以通过环境变量控制测试行为：
- `TEST_LEVEL`：设置测试级别（basic/advanced/comprehensive）
- `TEST_ADAPTERS`：指定要测试的适配器（逗号分隔）
- `TEST_TIMEOUT`：设置单个测试的超时时间（秒）

**流程分解：**
1. **配置测试**：验证配置文件格式和必要参数
2. **连接测试**：检查各AI服务的API连接
3. **功能测试**：验证核心模块的基本功能
4. **集成测试**：测试模块间的协作
5. **性能测试**：评估系统响应时间和资源使用

**最佳实践：**
- 首次安装后应运行完整测试，确保系统配置正确
- 定期运行测试可及时发现服务或配置问题
- 在更新系统或添加新适配器后应运行测试
- 测试失败时应查看详细日志，按错误提示进行修复

#### 3. 系统状态命令

```bash
python -m producer.cli status
```

**执行原理：**
此命令收集并显示系统的当前状态信息，包括：
1. 系统版本和运行环境
2. 配置状态和有效设置
3. API服务连接状态
4. 资源使用情况（CPU、内存、存储）
5. 成本控制统计（当前周期使用量、剩余预算）
6. 最近任务执行状态

状态信息通过系统监控模块收集，并以结构化格式展示，便于用户快速了解系统健康状况。

**参数说明：**
此命令支持以下可选参数：
- `--format`：指定输出格式（text/json/table）
- `--detail`：显示详细信息（布尔标志）
- `--history`：显示历史状态记录（数量）

**流程分解：**
1. **信息收集**：从各模块收集状态数据
2. **数据处理**：格式化和汇总收集的信息
3. **结果展示**：按指定格式输出状态报告

**最佳实践：**
- 定期检查系统状态，特别是进行大规模制作前
- 使用`--detail`参数获取更多信息，有助于性能调优
- 结合`--history`参数分析系统状态变化趋势
- 状态异常时应检查相应模块的详细日志

### 剧本生成CLI命令

#### 1. 创建新剧本项目（仅大纲）

```bash
python script_cli.py create \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180
```

**执行原理：**
此命令启动剧本生成工作流的第一阶段——大纲生成。系统会：
1. 创建剧本数据结构，初始化基本参数
2. 调用文本生成适配器，基于主题和时代生成故事大纲
3. 应用历史故事评估器，确保内容的历史准确性
4. 保存生成的剧本大纲到项目文件

大纲生成采用提示词工程技术，通过精心设计的提示引导AI生成符合要求的故事结构。系统会使用链式处理，将复杂任务分解为多个简单步骤。

**参数说明：**
- `--title`：剧本标题，用于生成内容和文件命名
- `--theme`：剧本主题，影响故事方向和内容
- `--era`：历史时代，系统会根据该时代的历史背景生成内容
- `--duration`：预期视频时长（秒），影响大纲的复杂度和场景数量

**流程分解：**
1. **初始化**：创建剧本数据结构，设置基本参数
2. **提示构建**：基于用户输入构建生成提示
3. **大纲生成**：调用LLM生成故事大纲
4. **内容评估**：使用评估器检查大纲质量
5. **结果保存**：将大纲保存到项目文件

**最佳实践：**
- 主题描述应具体明确，避免过于宽泛
- 时代参数应与主题匹配，确保历史一致性
- 时长设置应合理，通常每分钟内容需要2-3个场景
- 生成的大纲可以作为后续完整剧本生成的基础

#### 2. 创建完整剧本（包括场景和对话）

```bash
python script_cli.py create \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180 \
  --full
```

**执行原理：**
此命令执行完整的剧本生成工作流，包括大纲生成、场景详细化和对话生成三个阶段。系统采用分层处理架构：

1. **大纲生成**：首先生成故事大纲，确定整体结构
2. **场景详细化**：将大纲分解为具体场景，每个场景包含场景描述、角色动作和情绪
3. **对话生成**：为每个场景生成角色对话，确保对话符合角色性格和时代背景

整个过程使用链式处理，每个阶段的输出作为下一阶段的输入，确保内容的一致性和连贯性。系统会在每个阶段应用相应的评估器，确保内容质量。

**参数说明：**
- `--title`：剧本标题，用于生成内容和文件命名
- `--theme`：剧本主题，影响故事方向和内容
- `--era`：历史时代，系统会根据该时代的历史背景生成内容
- `--duration`：预期视频时长（秒），影响剧本的复杂度和场景数量
- `--full`：标志参数，指示生成完整剧本而非仅大纲

**流程分解：**
1. **大纲生成**：创建故事整体结构
2. **场景分解**：将大纲分解为具体场景
3. **场景详细化**：为每个场景添加详细描述
4. **角色定义**：定义主要角色及其性格特征
5. **对话生成**：为每个场景生成角色对话
6. **剧本整合**：将所有元素整合为完整剧本
7. **质量评估**：评估剧本整体质量
8. **结果保存**：保存完整剧本到项目文件

**最佳实践：**
- 完整剧本生成需要更多时间和资源，建议确保预算充足
- 复杂主题可能需要多次生成才能获得满意结果
- 可以先生成大纲，确认方向正确后再生成完整剧本
- 生成的剧本可以手动编辑，进一步优化内容

#### 3. 从大纲继续生成完整剧本

```bash
python script_cli.py continue <project_id>
```

**执行原理：**
此命令允许用户基于已有的大纲继续生成完整剧本。系统会：
1. 加载指定项目的大纲数据
2. 从大纲生成阶段继续执行工作流
3. 执行场景详细化和对话生成
4. 整合所有内容生成完整剧本

这种分阶段生成方式提供了更大的灵活性，用户可以先审核和修改大纲，然后再继续生成完整内容，提高最终质量和用户满意度。

**参数说明：**
- `project_id`：项目标识符，用于加载已有大纲数据

**流程分解：**
1. **项目加载**：加载指定项目的大纲数据
2. **场景详细化**：基于大纲生成详细场景
3. **对话生成**：为场景生成角色对话
4. **剧本整合**：整合大纲、场景和对话为完整剧本
5. **质量评估**：评估生成剧本的质量
6. **结果保存**：保存完整剧本，更新项目状态

**最佳实践：**
- 在继续生成前，建议检查和编辑大纲，确保内容方向正确
- 可以修改大纲中的场景数量或复杂度，以控制最终剧本长度
- 继续生成前确保有足够的预算和时间
- 生成完成后应审核完整剧本，必要时进行手动调整

#### 4. 列出所有剧本项目

```bash
python script_cli.py list-projects
```

**执行原理：**
此命令查询并显示所有剧本项目的列表。系统会：
1. 扫描项目存储目录
2. 读取每个项目的元数据
3. 格式化项目信息
4. 显示项目列表，包括项目ID、标题、状态和创建时间

项目信息存储在专门的数据文件中，系统通过解析这些文件获取项目状态和基本信息。

**参数说明：**
此命令支持以下可选参数：
- `--status`：按状态过滤项目（all/active/completed/failed）
- `--sort`：指定排序字段（created/updated/title/status）
- `--format`：指定输出格式（text/table/json）

**流程分解：**
1. **目录扫描**：扫描项目存储目录
2. **数据读取**：读取项目元数据文件
3. **数据过滤**：根据参数过滤项目列表
4. **数据排序**：按指定字段排序项目
5. **格式化输出**：按指定格式格式化并显示结果

**最佳实践：**
- 定期列出项目，了解项目进展状态
- 使用状态过滤功能，专注于特定状态的项目
- 结合排序功能，快速找到最新或最旧的项目
- 对于大量项目，考虑使用JSON格式输出，便于进一步处理

#### 5. 显示项目详情

```bash
python script_cli.py show <project_id>
```

**执行原理：**
此命令显示指定项目的详细信息。系统会：
1. 加载项目数据文件
2. 解析项目内容和元数据
3. 格式化项目信息
4. 显示详细的项目报告，包括剧本内容、生成历史和状态信息

项目详情包括剧本结构、角色信息、场景列表、对话内容以及生成过程中的各种统计信息。

**参数说明：**
- `project_id`：项目标识符，指定要显示的项目
- `--detail`：显示更详细的信息（布尔标志）
- `--section`：仅显示特定部分（outline/scenes/dialogues/stats）

**流程分解：**
1. **项目加载**：加载指定项目的数据文件
2. **数据解析**：解析项目内容和元数据
3. **信息过滤**：根据参数过滤要显示的信息
4. **格式化输出**：格式化并显示项目详情

**最佳实践：**
- 使用详情命令了解项目的完整状态和内容
- 对于大型项目，使用`--section`参数仅查看需要的部分
- 结合`--detail`参数获取更多技术细节，有助于问题诊断
- 定期检查项目详情，监控项目进展和质量

#### 6. 删除项目

```bash
python script_cli.py delete <project_id>
```

**执行原理：**
此命令删除指定的项目及其所有相关文件。系统会：
1. 验证项目存在性和访问权限
2. 删除项目数据文件
3. 删除项目生成的媒体文件（如有）
4. 更新项目索引

删除操作是不可逆的，系统会要求用户确认，以防止误删除重要项目。

**参数说明：**
- `project_id`：项目标识符，指定要删除的项目
- `--force`：跳过确认提示，直接删除（谨慎使用）
- `--keep-media`：保留媒体文件，仅删除项目数据

**流程分解：**
1. **项目验证**：验证项目存在性和访问权限
2. **用户确认**：提示用户确认删除操作（除非使用--force）
3. **文件删除**：删除项目数据文件和相关媒体文件
4. **索引更新**：更新项目索引，移除已删除项目

**最佳实践：**
- 删除前确保项目确实不再需要
- 对于重要项目，考虑先备份再删除
- 使用`--keep-media`参数可以保留生成的媒体文件，仅删除项目数据
- 定期清理不需要的项目，释放存储空间

### 视频制作CLI命令

#### 1. 制作完整视频（剧本+视频）

```bash
python video_cli.py produce \
  --title "贞观之治" \
  --theme "唐太宗治国理政" \
  --era "唐朝" \
  --duration 180 \
  --output-dir ./output
```

**执行原理：**
此命令执行完整的视频制作工作流，包括剧本生成和视频制作两个主要阶段。系统采用模块化架构：

1. **剧本生成阶段**：
   - 生成故事大纲
   - 详细化场景
   - 生成角色对话

2. **视频制作阶段**：
   - 为每个场景生成图像
   - 将图像转换为视频片段
   - 为对话生成语音
   - 合成视频和音频
   - 添加后期效果

整个流程由工作流引擎协调，各阶段可以并行执行，提高制作效率。系统会实时监控资源使用和成本，确保在预算内完成制作。

**参数说明：**
- `--title`：视频标题，用于生成内容和文件命名
- `--theme`：视频主题，影响剧本生成的方向和内容
- `--era`：历史时代，系统会根据该时代的历史背景生成内容
- `--duration`：视频时长（秒），影响剧本复杂度和制作时间
- `--output-dir`：输出目录，生成的视频文件将保存在此目录

**流程分解：**
1. **初始化**：设置工作流环境，加载配置和适配器
2. **剧本生成**：生成完整剧本，包括大纲、场景和对话
3. **图像生成**：为每个场景生成视觉图像
4. **视频合成**：将静态图像转换为动态视频片段
5. **语音合成**：为对话生成语音文件
6. **音视频合成**：将视频片段和语音合成为完整视频
7. **后期处理**：添加背景音乐、字幕和特效
8. **结果保存**：保存最终视频到指定目录

**最佳实践：**
- 确保输出目录有足够的存储空间
- 对于长视频，确保预算充足，可能需要调整质量设置
- 首次使用建议先用较短时长测试，确认系统正常工作
- 制作完成后检查输出视频质量，必要时调整参数重新制作

#### 2. 基于现有剧本制作视频

```bash
python video_cli.py produce_from_script <script_id>
```

**执行原理：**
此命令基于已有剧本制作视频，跳过剧本生成阶段。系统会：
1. 加载指定剧本的数据
2. 解析剧本结构，提取场景和对话信息
3. 为每个场景生成视觉图像
4. 将图像转换为视频片段
5. 为对话生成语音
6. 合成视频和音频，添加后期效果
7. 生成最终视频文件

这种方式允许用户先创建和优化剧本，然后再进行视频制作，提供了更大的灵活性和质量控制。

**参数说明：**
- `script_id`：剧本标识符，指定要用于制作视频的剧本
- `--output-dir`：输出目录，生成的视频文件将保存在此目录
- `--quality`：视频质量设置（low/medium/high）
- `--style`：视频风格设置（cinematic/documentary/artistic）

**流程分解：**
1. **剧本加载**：加载指定剧本的数据文件
2. **剧本解析**：解析剧本结构，提取场景和对话信息
3. **图像生成**：为每个场景生成视觉图像
4. **视频合成**：将静态图像转换为动态视频片段
5. **语音合成**：为对话生成语音文件
6. **音视频合成**：将视频片段和语音合成为完整视频
7. **后期处理**：添加背景音乐、字幕和特效
8. **结果保存**：保存最终视频到指定目录

**最佳实践：**
- 在制作视频前，确保剧本内容完整且质量良好
- 使用质量参数平衡视频效果和制作成本
- 风格参数应根据内容主题选择，增强观看体验
- 制作完成后检查视频质量，特别是音视频同步情况

### 配置参数详解

#### 1. 模型选择配置

**执行原理：**
模型选择配置决定了系统使用的AI模型类型和版本，直接影响生成内容的质量、风格和成本。系统通过配置管理器加载这些设置，并在初始化适配器时应用相应的模型。

不同类型的生成任务（文本、图像、视频、语音）可以配置不同的模型，系统会根据任务类型自动选择合适的模型。模型配置还包含版本信息，确保使用稳定和兼容的模型版本。

**关键配置参数：**
```yaml
models:
  text:
    default: "gpt-4"
    alternatives: ["claude-3", "glm-4"]
    version: "latest"
  image:
    default: "dall-e-3"
    alternatives: ["midjourney", "stable-diffusion"]
    version: "v3"
  video:
    default: "runway-gen2"
    alternatives: ["pika", "kaiber"]
    version: "v1"
  voice:
    default: "elevenlabs"
    alternatives: ["edge-tts", "azure-tts"]
    version: "v1"
```

**参数说明：**
- `default`：默认使用的模型，系统优先选择此模型
- `alternatives`：备选模型列表，当默认模型不可用时使用
- `version`：模型版本，确保使用稳定的模型版本

**技术背景：**
不同模型有不同的特点和适用场景：
- GPT-4适合复杂文本生成，但成本较高
- Claude-3在长文本处理上表现优秀
- DALL-E-3图像质量高，但生成速度较慢
- Runway Gen2在视频生成方面领先
- ElevenLabs提供最自然的语音合成

**最佳实践：**
- 根据项目需求选择合适的模型，平衡质量和成本
- 对于关键项目，建议使用高质量模型
- 批量处理时可以考虑使用成本较低的模型
- 定期更新模型版本，获取最新功能和改进

#### 2. 成本控制配置

**执行原理：**
成本控制配置管理系统资源使用和预算分配，确保制作过程在预算范围内完成。系统通过成本控制器实时监控各阶段的资源消耗，并在接近预算限制时采取相应措施。

成本控制基于预定义的成本模型，每个操作（如生成一段文本、一张图像）都有对应的成本估算。系统会累积这些成本，并与预算进行比较，当达到预算阈值时会触发警告或停止操作。

**关键配置参数：**
```yaml
cost_control:
  budget:
    daily: 10.0
    weekly: 50.0
    monthly: 200.0
  thresholds:
    warning: 0.8    # 预算的80%时发出警告
    critical: 0.95  # 预算的95%时停止操作
  optimization:
    enabled: true
    strategy: "balanced"  # conservative/balanced/aggressive
  model_selection:
    cost_aware: true
    fallback_threshold: 0.7
```

**参数说明：**
- `budget`：预算设置，支持日、周、月不同周期
- `thresholds`：阈值设置，定义警告和停止操作的预算比例
- `optimization`：优化设置，控制成本优化策略
- `model_selection`：模型选择设置，控制基于成本的模型选择行为

**技术背景：**
成本控制系统采用实时监控和预测算法：
- 实时监控各操作的API调用和资源消耗
- 基于历史数据预测完成剩余工作所需的成本
- 当预测成本超过预算时，自动调整参数或切换到更经济的模型
- 提供详细的成本报告，帮助用户了解资源使用情况

**最佳实践：**
- 根据项目规模和重要性设置合理的预算
- 对于测试项目，使用较低的预算限制
- 启用成本优化功能，特别是在批量处理时
- 定期检查成本报告，了解资源使用模式
- 根据成本数据调整配置，优化资源使用效率

#### 3. 质量控制配置

**执行原理：**
质量控制配置确保生成内容符合预期的质量标准。系统通过质量评估器对生成的内容进行多维度评估，并根据评估结果决定是否接受内容或需要重新生成。

质量控制采用多层次评估机制，包括内容相关性、历史准确性、语言质量、视觉质量等多个维度。每个维度都有相应的评估标准和阈值，系统会综合这些评估结果给出总体质量评分。

**关键配置参数：**
```yaml
quality_control:
  enabled: true
  auto_improve: true
  thresholds:
    text:
      relevance: 0.8
      accuracy: 0.85
      coherence: 0.75
    image:
      relevance: 0.75
      quality: 0.8
      consistency: 0.7
    video:
      quality: 0.8
      smoothness: 0.75
      sync: 0.9
    voice:
      clarity: 0.85
      emotion: 0.7
      sync: 0.9
  retry:
    max_attempts: 3
    backoff_factor: 2
    timeout: 300
```

**参数说明：**
- `enabled`：是否启用质量控制
- `auto_improve`：是否自动改进不达标的内容
- `thresholds`：各质量维度的阈值设置
- `retry`：重试设置，控制质量不达标时的重试行为

**技术背景：**
质量控制系统基于机器学习评估模型：
- 使用预训练模型评估内容质量
- 结合规则引擎检查特定质量标准
- 采用多维度综合评分，避免单一维度的偏差
- 支持自适应阈值，根据历史表现动态调整质量标准

**最佳实践：**
- 根据项目需求设置合理的质量阈值
- 对于关键项目，使用较高的质量标准
- 启用自动改进功能，但设置合理的重试次数
- 定期检查质量报告，了解系统表现
- 根据质量数据调整配置，优化生成效果

#### 4. 工作流配置

**执行原理：**
工作流配置定义了系统执行任务的流程和步骤。系统通过工作流引擎解析这些配置，并按照定义的步骤执行相应的操作。工作流配置支持条件分支、并行处理和错误处理等高级功能。

工作流采用有向无环图(DAG)结构，每个节点代表一个操作，边代表操作间的依赖关系。工作流引擎会根据这个结构调度任务，确保操作按正确顺序执行，并处理可能出现的错误。

**关键配置参数：**
```yaml
workflow:
  engine: "parallel"  # sequential/parallel
  steps:
    - name: "outline_generation"
      adapter: "text"
      model: "gpt-4"
      timeout: 300
      retry: 3
    - name: "scene_detailing"
      adapter: "text"
      model: "gpt-4"
      depends_on: ["outline_generation"]
      timeout: 300
      retry: 3
    - name: "dialogue_generation"
      adapter: "text"
      model: "gpt-4"
      depends_on: ["scene_detailing"]
      timeout: 300
      retry: 3
    - name: "image_generation"
      adapter: "image"
      model: "dall-e-3"
      depends_on: ["scene_detailing"]
      timeout: 600
      retry: 2
      parallel: true
    - name: "video_synthesis"
      adapter: "video"
      model: "runway-gen2"
      depends_on: ["image_generation", "dialogue_generation"]
      timeout: 900
      retry: 2
    - name: "voice_synthesis"
      adapter: "voice"
      model: "elevenlabs"
      depends_on: ["dialogue_generation"]
      timeout: 300
      retry: 2
    - name: "post_processing"
      adapter: "internal"
      depends_on: ["video_synthesis", "voice_synthesis"]
      timeout: 300
      retry: 1
  error_handling:
    strategy: "continue"  # stop/continue/retry
    max_retries: 3
    backoff_factor: 2
```

**参数说明：**
- `engine`：工作流引擎类型，支持顺序和并行执行
- `steps`：工作流步骤定义，包括名称、适配器、模型等
- `depends_on`：步骤依赖关系，定义执行顺序
- `timeout`：步骤超时时间（秒）
- `retry`：步骤失败时的重试次数
- `parallel`：是否允许并行执行
- `error_handling`：错误处理策略和参数

**技术背景：**
工作流系统基于任务调度和依赖解析算法：
- 使用拓扑排序解析步骤依赖关系
- 采用任务队列管理待执行任务
- 支持动态任务分配和负载均衡
- 实现错误恢复和重试机制
- 提供工作流状态监控和报告

**最佳实践：**
- 根据任务特性选择合适的引擎类型
- 合理设置超时时间，避免任务卡住
- 对于关键步骤，增加重试次数
- 利用并行处理提高效率，但注意资源限制
- 配置适当的错误处理策略，平衡健壮性和效率

#### 5. 适配器配置

**执行原理：**
适配器配置定义了系统与各种AI服务的接口和参数。每个适配器负责与特定的AI服务通信，将系统请求转换为服务特定的API调用，并将响应转换为系统标准格式。

适配器系统采用插件式架构，每个适配器实现统一的接口，但内部处理逻辑针对特定服务优化。这种设计使得系统可以轻松添加对新服务的支持，而不需要修改核心代码。

**关键配置参数：**
```yaml
adapters:
  text:
    gpt-4:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      model: "gpt-4"
      max_tokens: 4000
      temperature: 0.7
      timeout: 60
    claude-3:
      api_key: "${ANTHROPIC_API_KEY}"
      base_url: "https://api.anthropic.com"
      model: "claude-3-opus-20240229"
      max_tokens: 4000
      temperature: 0.7
      timeout: 60
  image:
    dall-e-3:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      model: "dall-e-3"
      size: "1024x1024"
      quality: "standard"
      timeout: 120
    midjourney:
      api_key: "${MIDJOURNEY_API_KEY}"
      base_url: "https://api.midjourney.com"
      model: "v6"
      size: "1024x1024"
      quality: "standard"
      timeout: 180
  video:
    runway-gen2:
      api_key: "${RUNWAY_API_KEY}"
      base_url: "https://api.runwayml.com/v1"
      model: "gen2"
      resolution: "1280x720"
      fps: 24
      timeout: 300
    pika:
      api_key: "${PIKA_API_KEY}"
      base_url: "https://api.pika.art"
      model: "v1"
      resolution: "1280x720"
      fps: 24
      timeout: 300
  voice:
    elevenlabs:
      api_key: "${ELEVENLABS_API_KEY}"
      base_url: "https://api.elevenlabs.io/v1"
      model: "multilingual-v2"
      voice: "Bella"
      timeout: 60
    edge-tts:
      api_key: ""
      base_url: ""
      model: ""
      voice: "zh-CN-XiaoxiaoNeural"
      timeout: 30
```

**参数说明：**
- `api_key`：API密钥，支持环境变量引用
- `base_url`：服务基础URL
- `model`：使用的模型名称
- `max_tokens`：最大令牌数（文本适配器）
- `temperature`：生成随机性（文本适配器）
- `size`：图像尺寸（图像适配器）
- `quality`：图像质量（图像适配器）
- `resolution`：视频分辨率（视频适配器）
- `fps`：视频帧率（视频适配器）
- `voice`：语音模型（语音适配器）
- `timeout`：请求超时时间（秒）

**技术背景：**
适配器系统基于统一接口和工厂模式：
- 每个适配器实现统一的接口，确保互换性
- 使用工厂模式创建适配器实例，支持动态加载
- 采用请求转换器将系统请求转换为服务特定格式
- 实现响应解析器将服务响应转换为系统标准格式
- 支持连接池和缓存，提高性能和可靠性

**最佳实践：**
- 使用环境变量存储敏感信息如API密钥
- 根据服务特性设置合适的超时时间
- 为关键服务配置备用适配器，提高系统可靠性
- 定期检查适配器配置，确保与服务API兼容
- 根据项目需求调整适配器参数，优化生成效果