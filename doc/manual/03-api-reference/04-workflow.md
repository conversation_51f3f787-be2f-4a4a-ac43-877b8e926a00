# 工作流引擎 API

工作流引擎是Producer系统的核心组件，负责协调各个模块完成视频制作流程。

## WorkflowEngine 类

`WorkflowEngine` 是工作流引擎的核心类，负责管理工作流的执行。

### 初始化

```python
from core.workflow import WorkflowEngine

class WorkflowEngine:
    def __init__(self, config: ConfigManager, cost_controller: CostController):
        """
        初始化工作流引擎
        
        Args:
            config (ConfigManager): 配置管理器实例
            cost_controller (CostController): 成本控制器实例
        """
        pass
```

### 主要方法

#### register_step

注册自定义工作流步骤。

```python
def register_step(self, step_func: Callable, stage: WorkflowStage, priority: int = 0):
    """
    注册自定义工作流步骤
    
    Args:
        step_func (Callable): 步骤函数
        stage (WorkflowStage): 工作流阶段
        priority (int): 优先级（数字越小优先级越高）
    """
    pass
```

#### execute_workflow

执行完整的工作流。

```python
async def execute_workflow(self, script_data: ScriptData) -> WorkflowResult:
    """
    执行完整的工作流
    
    Args:
        script_data (ScriptData): 剧本数据
        
    Returns:
        WorkflowResult: 工作流执行结果
    """
    pass
```

#### get_default_workflow

获取默认工作流步骤。

```python
def get_default_workflow(self) -> List[WorkflowStep]:
    """
    获取默认工作流步骤
    
    Returns:
        List[WorkflowStep]: 默认工作流步骤列表
    """
    pass
```

## 数据结构

### WorkflowStage

工作流阶段枚举。

```python
from enum import Enum

class WorkflowStage(Enum):
    SCRIPT_GENERATION = "script_generation"      # 剧本生成
    OUTLINE_GENERATION = "outline_generation"    # 大纲生成
    SCENE_PLANNING = "scene_planning"           # 场景规划
    IMAGE_GENERATION = "image_generation"       # 图像生成
    VIDEO_GENERATION = "video_generation"       # 视频生成
    VOICE_SYNTHESIS = "voice_synthesis"         # 语音合成
    VIDEO_COMPOSITION = "video_composition"     # 视频合成
```

### ScriptData

剧本数据结构。

```python
class ScriptData:
    def __init__(self, script_id: str, title: str, theme: str, era: str, 
                 summary: str = "", total_duration: int = 0):
        self.script_id: str = script_id           # 剧本ID
        self.title: str = title                   # 标题
        self.theme: str = theme                   # 主题
        self.era: str = era                       # 时代
        self.summary: str = summary               # 摘要
        self.total_duration: int = total_duration # 总时长（秒）
```

### WorkflowResult

工作流执行结果。

```python
class WorkflowResult:
    def __init__(self):
        self.status: str = "success"             # 执行状态
        self.total_cost: float = 0.0             # 总成本
        self.output_path: str = ""               # 输出路径
        self.script_data: ScriptData = None      # 剧本数据
```

## 使用示例

```python
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

# 初始化系统组件
config = ConfigManager()
cost_controller = CostController(config)
workflow = WorkflowEngine(config, cost_controller)

# 创建剧本数据
script_data = ScriptData(
    script_id="example_001",
    title="明朝风云",
    theme="宫廷斗争",
    era="明朝",
    total_duration=180
)

# 执行工作流
result = await workflow.execute_workflow(script_data)
print(f"制作状态: {result.status}")
print(f"总成本: ${result.total_cost:.4f}")
print(f"输出路径: {result.output_path}")
```

通过这些API，您可以灵活地控制和扩展工作流的执行过程。