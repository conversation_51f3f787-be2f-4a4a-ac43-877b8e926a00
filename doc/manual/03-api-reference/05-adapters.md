# 适配器 API

适配器模块为系统提供了与各种AI服务交互的统一接口。

## BaseAdapter 类

`BaseAdapter` 是所有适配器的基类，定义了适配器的通用接口。

### 初始化

```python
from adapters.base import BaseAdapter

class BaseAdapter:
    def __init__(self, config: AdapterConfig, system_config: ConfigManager, 
                 cost_controller: CostController):
        """
        初始化基础适配器
        
        Args:
            config (AdapterConfig): 适配器配置
            system_config (ConfigManager): 系统配置管理器
            cost_controller (CostController): 成本控制器
        """
        pass
```

### 主要方法

#### generate

生成内容的抽象方法，需要在子类中实现。

```python
async def generate(self, prompt: str, **kwargs) -> AdapterResult:
    """
    生成内容的抽象方法
    
    Args:
        prompt (str): 提示词
        **kwargs: 其他参数
        
    Returns:
        AdapterResult: 生成结果
    """
    raise NotImplementedError
```

#### check_rate_limit

检查速率限制。

```python
def check_rate_limit(self) -> bool:
    """
    检查速率限制
    
    Returns:
        bool: 是否超出速率限制
    """
    pass
```

#### execute_with_retry

带重试机制的执行方法。

```python
async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
    """
    带重试机制的执行方法
    
    Args:
        func (Callable): 要执行的函数
        *args: 函数参数
        **kwargs: 函数关键字参数
        
    Returns:
        Any: 函数执行结果
    """
    pass
```

## 数据结构

### AdapterConfig

适配器配置数据结构。

```python
class AdapterConfig:
    def __init__(self, service_name: str, api_key: str, model: str = ""):
        self.service_name: str = service_name  # 服务名称
        self.api_key: str = api_key            # API密钥
        self.model: str = model                # 模型名称
```

### AdapterResult

适配器执行结果数据结构。

```python
class AdapterResult:
    def __init__(self):
        self.content: str = ""                 # 生成内容
        self.actual_cost: float = 0.0         # 实际成本
        self.input_tokens: int = 0            # 输入token数
        self.output_tokens: int = 0           # 输出token数
```

## GPTAdapter 类

`GPTAdapter` 是OpenAI GPT服务的适配器实现。

### 初始化

```python
from adapters.text.gpt_adapter import GPTAdapter

class GPTAdapter(BaseAdapter):
    def __init__(self, config: AdapterConfig, system_config: ConfigManager, 
                 cost_controller: CostController):
        """
        初始化GPT适配器
        
        Args:
            config (AdapterConfig): 适配器配置
            system_config (ConfigManager): 系统配置管理器
            cost_controller (CostController): 成本控制器
        """
        super().__init__(config, system_config, cost_controller)
```

### 主要方法

#### generate

实现文本生成方法。

```python
async def generate(self, prompt: str, **kwargs) -> AdapterResult:
    """
    生成文本内容
    
    Args:
        prompt (str): 提示词
        **kwargs: 其他参数
        
    Returns:
        AdapterResult: 生成结果
    """
    pass
```

#### _build_messages

构建消息列表。

```python
def _build_messages(self, prompt: str) -> List[Dict[str, str]]:
    """
    构建消息列表
    
    Args:
        prompt (str): 提示词
        
    Returns:
        List[Dict[str, str]]: 消息列表
    """
    pass
```

#### _calculate_actual_cost

计算实际成本。

```python
def _calculate_actual_cost(self, input_tokens: int, output_tokens: int) -> float:
    """
    计算实际成本
    
    Args:
        input_tokens (int): 输入token数
        output_tokens (int): 输出token数
        
    Returns:
        float: 实际成本
    """
    pass
```

## 使用示例

```python
from adapters.text.gpt_adapter import GPTAdapter
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController

# 配置适配器
adapter_config = AdapterConfig(
    service_name="gpt",
    api_key="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    model="gpt-4o-mini"
)

# 初始化系统组件
config = ConfigManager()
cost_controller = CostController(config)

# 初始化适配器
adapter = GPTAdapter(adapter_config, config, cost_controller)

# 生成内容
result = await adapter.generate("生成一个关于唐朝的故事")
print(f"生成内容: {result.content}")
print(f"实际成本: ${result.actual_cost:.4f}")
```

通过这些API，您可以方便地使用和扩展各种AI服务适配器。