# 配置管理 API

配置管理模块负责系统的配置加载、验证和管理。

## ConfigManager 类

`ConfigManager` 是配置管理的核心类，负责加载和管理系统的各种配置。

### 初始化

```python
from core.config import ConfigManager

class ConfigManager:
    def __init__(self, config_path: str = "./config/config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path (str): 配置文件路径
        """
        pass
```

### 主要方法

#### get_api_key

获取指定服务的API密钥。

```python
def get_api_key(self, service_name: str) -> str:
    """
    获取指定服务的API密钥
    
    Args:
        service_name (str): 服务名称 (e.g., "google", "openai")
        
    Returns:
        str: API密钥
    """
    pass
```

#### get_model_config

获取指定模型的配置。

```python
def get_model_config(self, model_type: str) -> dict:
    """
    获取指定模型的配置
    
    Args:
        model_type (str): 模型类型 (e.g., "text_generation", "image_generation")
        
    Returns:
        dict: 模型配置
    """
    pass
```

#### get

获取配置项的值。

```python
def get(self, key_path: str, default=None):
    """
    获取配置项的值
    
    Args:
        key_path (str): 配置项路径 (e.g., "system.name")
        default: 默认值
        
    Returns:
        配置项的值
    """
    pass
```

#### set

设置配置项的值。

```python
def set(self, key_path: str, value):
    """
    设置配置项的值
    
    Args:
        key_path (str): 配置项路径 (e.g., "system.debug")
        value: 配置项的值
    """
    pass
```

## 使用示例

```python
from core.config import ConfigManager

# 初始化配置管理器
config = ConfigManager("./config/custom_config.yaml")

# 获取API密钥
openai_key = config.get_api_key("openai")

google_config = config.get_model_config("image_generation")

# 获取系统配置
system_name = config.get("system.name")
debug_mode = config.get("system.debug", False)

# 设置配置项
config.set("system.debug", True)
```

通过这些API，您可以方便地管理系统的各种配置项。