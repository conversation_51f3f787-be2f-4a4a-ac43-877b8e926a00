# 成本控制 API

成本控制模块负责监控和管理系统的API调用成本，确保在预算范围内运行。

## CostController 类

`CostController` 是成本控制的核心类，负责预算管理、成本计算和超支检查。

### 初始化

```python
from core.cost_control import CostController

class CostController:
    def __init__(self, config: ConfigManager):
        """
        初始化成本控制器
        
        Args:
            config (ConfigManager): 配置管理器实例
        """
        pass
```

### 主要方法

#### get_budget_status

获取当前预算状态。

```python
def get_budget_status(self) -> BudgetStatus:
    """
    获取当前预算状态
    
    Returns:
        BudgetStatus: 预算状态对象
    """
    pass
```

#### is_over_budget

检查是否超出预算。

```python
def is_over_budget(self) -> bool:
    """
    检查是否超出预算
    
    Returns:
        bool: 是否超出预算
    """
    pass
```

#### record_cost

记录一次API调用的成本。

```python
def record_cost(self, service_name: str, model: str, cost: float):
    """
    记录一次API调用的成本
    
    Args:
        service_name (str): 服务名称
        model (str): 模型名称
        cost (float): 调用成本
    """
    pass
```

#### set_temporary_limit

设置临时预算限制。

```python
def set_temporary_limit(self, limit_usd: float):
    """
    设置临时预算限制
    
    Args:
        limit_usd (float): 临时预算限制（美元）
    """
    pass
```

## 数据结构

### BudgetStatus

预算状态数据结构。

```python
class BudgetStatus:
    def __init__(self):
        self.daily_limit: float = 0.0      # 日预算限制
        self.daily_spent: float = 0.0      # 日已花费
        self.daily_remaining: float = 0.0  # 日预算剩余
        self.monthly_limit: float = 0.0    # 月预算限制
        self.monthly_spent: float = 0.0    # 月已花费
        self.monthly_remaining: float = 0.0 # 月预算剩余
```

## 使用示例

```python
from core.cost_control import CostController
from core.config import ConfigManager

# 初始化配置和成本控制器
config = ConfigManager()
cost_controller = CostController(config)

# 获取预算状态
budget_status = cost_controller.get_budget_status()
print(f"日预算剩余: ${budget_status.daily_remaining:.2f}")
print(f"月预算剩余: ${budget_status.monthly_remaining:.2f}")

# 检查是否超出预算
if cost_controller.is_over_budget():
    print("已超出预算限制")
else:
    print("预算充足")

# 记录API调用成本
cost_controller.record_cost("openai", "gpt-4o-mini", 0.002)

# 设置临时预算限制
cost_controller.set_temporary_limit(5.0)
```

通过这些API，您可以有效地监控和控制系统的成本。