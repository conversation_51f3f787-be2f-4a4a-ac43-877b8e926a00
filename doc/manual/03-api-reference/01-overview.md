# API 概览

本指南介绍了Producer系统的API设计理念、认证方式、版本策略和错误处理机制。

## API设计理念

Producer系统的API设计遵循以下核心理念：

1. **模块化设计**：API按功能模块划分，便于理解和使用。
2. **异步支持**：所有耗时操作均支持异步执行，提高系统效率。
3. **可扩展性**：API设计考虑了未来的扩展需求，支持插件化架构。
4. **易用性**：提供简洁明了的接口，降低使用门槛。
5. **安全性**：通过API密钥和权限控制确保系统安全。

## 认证方式

Producer系统使用API密钥进行认证，密钥通过环境变量或配置文件配置。

### API密钥配置

在`.env`文件中配置API密钥：

```
GLM_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GOOGLE_AI_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Kling_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 在代码中使用API密钥

```python
from core.config import ConfigManager

config = ConfigManager()
api_key = config.get_api_key("google")
```

## 版本策略

Producer系统遵循语义化版本控制（Semantic Versioning）策略：

- 主版本号（MAJOR）：不兼容的API变更
- 次版本号（MINOR）：向后兼容的功能性新增
- 修订号（PATCH）：向后兼容的问题修正

当前版本：1.0.0

## 错误处理机制

Producer系统定义了统一的错误处理机制：

### 异常类层次结构

```
Exception
└── AdapterError
    ├── RateLimitError
    └── APIError
```

### 错误处理示例

```python
from adapters.base import AdapterError, RateLimitError, APIError

try:
    result = await adapter.generate(prompt)
except RateLimitError as e:
    print(f"速率限制错误: {e}")
except APIError as e:
    print(f"API调用错误: {e}")
except AdapterError as e:
    print(f"适配器错误: {e}")
```

## 核心API模块

### 配置管理 (core.config)

```python
from core.config import ConfigManager

# 初始化配置管理器
config = ConfigManager(config_path="./config/config.yaml")

# 获取API密钥
api_key = config.get_api_key("google")

# 获取模型配置
model_config = config.get_model_config("text_generation")
```

### 成本控制 (core.cost_control)

```python
from core.cost_control import CostController

# 初始化成本控制器
cost_controller = CostController(config)

# 获取预算状态
budget_status = cost_controller.get_budget_status()

# 检查是否超出预算
is_over = cost_controller.is_over_budget()
```

### 工作流引擎 (core.workflow)

```python
from core.workflow import WorkflowEngine

# 初始化工作流引擎
workflow = WorkflowEngine(config, cost_controller)

# 注册自定义步骤
workflow.register_step(custom_step)

# 执行工作流
result = await workflow.execute_workflow(script_data)
```

### 适配器 (adapters)

```python
from adapters.text.gpt_adapter import GPTAdapter
from adapters.base import AdapterConfig

# 配置适配器
adapter_config = AdapterConfig(
    service_name="gpt",
    api_key=api_key,
    model="gpt-4o-mini"
)

# 初始化适配器
adapter = GPTAdapter(adapter_config, config, cost_controller)

# 生成内容
result = await adapter.generate("生成一个关于唐朝的故事")
```

通过了解这些API概览信息，您可以更好地使用和扩展Producer系统。