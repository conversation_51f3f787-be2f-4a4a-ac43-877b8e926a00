# 常见问题解答 (FAQ)

本指南解答用户在使用Producer系统过程中可能遇到的常见问题。

## 一般问题

### 1. Producer系统是什么？

Producer是一个基于LangChain的历史短剧视频自动制作系统。它能够根据用户提供的标题、主题和时代背景，自动生成完整的视频内容，包括剧本、场景图像、场景视频、语音对话和最终合成视频。

### 2. 系统支持哪些AI服务？

系统支持多种主流AI服务：

- **文本生成**：OpenAI GPT系列、Google Gemini、DeepSeek、GLM等
- **图像生成**：Google Imagen、Kling、FLUX、ComfyUI等
- **视频生成**：Google Veo、Kling、SVD等
- **语音合成**：ElevenLabs、Edge TTS等

### 3. 系统的最低配置要求是什么？

推荐配置：
- Python 3.11或更高版本
- 至少8GB内存
- 足够的磁盘空间存储生成的视频文件
- 稳定的网络连接

### 4. 如何选择合适的运行模式？

系统提供多种运行模式：

- **zero_cost**：零成本模式，使用免费或本地模型
- **hybrid_optimized**：混合优化模式，根据任务特点选择最优服务组合
- **quality_first**：质量优先模式，使用最高质量的服务

## 配置问题

### 1. 如何配置API密钥？

在项目根目录创建`.env`文件，添加所需的API密钥：

```
# .env
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GOOGLE_AI_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. 如何切换不同的AI服务？

在`config/config.yaml`中修改相应配置：

```yaml
# config.yaml
text_generation:
  primary_service: "gpt"  # 可选: gpt, google, glm, deepseek
  
image_generation:
  primary_service: "google"  # 可选: google, kling, flux
```

### 3. 如何设置预算限制？

在配置文件中设置预算限制：

```yaml
# config.yaml
budget:
  daily_limit_usd: 20.0   # 日预算限制
  monthly_limit_usd: 200.0 # 月预算限制
```

## 使用问题

### 1. 如何制作第一个视频？

```bash
# 使用CLI制作视频
uv run python -m producer.cli produce \
  --title "唐朝文化" \
  --theme "文化繁荣" \
  --era "唐朝" \
  --duration 180
```

### 2. 如何批量制作视频？

创建包含多个剧本的JSON文件：

```json
[
  {
    "title": "宋朝科技",
    "theme": "科技发展",
    "era": "宋朝",
    "duration": 200
  },
  {
    "title": "元朝疆域",
    "theme": "军事征服",
    "era": "元朝",
    "duration": 220
  }
]
```

然后运行批量处理命令：

```bash
uv run python -m producer.cli batch scripts.json
```

### 3. 如何查看系统状态？

```bash
# 查看系统状态和统计信息
uv run python -m producer.cli status
```

## 成本控制问题

### 1. 如何查看当前成本使用情况？

```bash
# 查看系统状态，包括成本信息
uv run python -m producer.cli status
```

或在Python代码中：

```python
from core.cost_control import CostController
from core.config import ConfigManager

config = ConfigManager()
cost_controller = CostController(config)
budget_status = cost_controller.get_budget_status()
print(f"日预算剩余: ${budget_status.daily_remaining:.2f}")
```

### 2. 如何设置临时预算限制？

在Python代码中：

```python
from core.cost_control import CostController

cost_controller.set_temporary_limit(5.0)  # 设置5美元临时预算
```

## 开发问题

### 1. 如何运行测试？

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/unit/test_config.py
```

### 2. 如何添加新的AI服务适配器？

1. 在`adapters/`目录下创建新的适配器文件
2. 继承`BaseAdapter`类并实现必要方法
3. 在配置文件中注册新服务

```python
from adapters.base import BaseAdapter

class NewServiceAdapter(BaseAdapter):
    async def generate(self, prompt: str, **kwargs) -> AdapterResult:
        # 实现生成逻辑
        pass
```

### 3. 如何自定义工作流步骤？

```python
from core.workflow import WorkflowEngine, WorkflowStage

# 定义自定义步骤函数
async def custom_step(script_data, config, cost_controller):
    # 实现自定义逻辑
    pass

# 注册自定义步骤
workflow = WorkflowEngine(config, cost_controller)
workflow.register_step(custom_step, WorkflowStage.SCRIPT_GENERATION)
```

## 性能问题

### 1. 如何提高视频生成速度？

- 启用并行处理：在配置中设置`system.parallel_processing: true`
- 使用更快的模型：在配置中选择速度优先的模型
- 增加并发任务数：调整`system.max_concurrent_tasks`

### 2. 如何减少内存使用？

- 禁用并行处理：设置`system.parallel_processing: false`
- 减少并发任务数：降低`system.max_concurrent_tasks`值
- 及时清理临时文件：设置`output.cleanup_temp: true`

通过了解这些常见问题的解答，您可以更好地使用Producer系统。