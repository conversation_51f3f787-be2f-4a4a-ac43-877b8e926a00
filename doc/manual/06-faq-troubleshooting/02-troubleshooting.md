# 故障排查指南

本指南帮助您诊断和解决在使用Producer系统过程中可能遇到的问题。

## 环境配置问题

### 1. Python版本不兼容

**问题**：运行时出现语法错误或模块导入错误

**解决方案**：
1. 检查Python版本：
   ```bash
   python --version
   ```
2. 确保使用Python 3.11或更高版本
3. 如果需要，使用pyenv管理Python版本：
   ```bash
   # 安装pyenv后
   pyenv install 3.11.0
   pyenv local 3.11.0
   ```

### 2. uv包管理器未安装

**问题**：运行`uv`命令时提示命令未找到

**解决方案**：
1. 安装uv包管理器：
   ```bash
   # macOS
   brew install uv
   
   # Ubuntu/Debian
   curl -LsSf https://astral.sh/uv/install.sh | sh
   
   # Windows (使用PowerShell)
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```
2. 重启终端或重新加载shell配置

### 3. 依赖安装失败

**问题**：运行`uv sync`时出现错误

**解决方案**：
1. 清理缓存：
   ```bash
   uv clean
   ```
2. 重新安装依赖：
   ```bash
   uv sync --refresh
   ```
3. 如果仍有问题，尝试删除.lock文件后重新安装：
   ```bash
   rm uv.lock
   uv sync
   ```

## API密钥问题

### 1. API密钥无效

**问题**：出现API认证错误或401状态码

**解决方案**：
1. 检查`.env`文件中的API密钥是否正确：
   ```
   # .env
   OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```
2. 确保没有多余的空格或换行符
3. 验证API密钥在对应服务商网站上有效
4. 重启应用以重新加载环境变量

### 2. API配额耗尽

**问题**：出现配额限制错误或429状态码

**解决方案**：
1. 检查服务商的API使用情况和配额限制
2. 等待配额重置（通常是每日或每月）
3. 考虑升级API套餐以获得更高配额
4. 在系统中设置更严格的预算限制以避免超支

## 网络连接问题

### 1. 网络超时

**问题**：API调用超时或连接失败

**解决方案**：
1. 检查网络连接是否稳定
2. 如果在有网络限制的环境中，配置代理：
   ```bash
   # 设置HTTP代理
   export HTTP_PROXY=http://proxy.company.com:8080
   export HTTPS_PROXY=http://proxy.company.com:8080
   ```
3. 增加超时时间（在适配器配置中）：
   ```yaml
   http_timeout: 60  # 增加到60秒
   ```

### 2. SSL证书错误

**问题**：出现SSL证书验证失败错误

**解决方案**：
1. 更新系统证书：
   ```bash
   # macOS
   brew install ca-certificates
   
   # Ubuntu/Debian
   sudo apt-get update && sudo apt-get install ca-certificates
   ```
2. 如果在企业网络中，可能需要安装企业证书
3. 临时解决方案（不推荐用于生产环境）：
   ```bash
   export PYTHONHTTPSVERIFY=0
   ```

## 模型和服务问题

### 1. 模型不可用

**问题**：出现模型未找到或不支持的错误

**解决方案**：
1. 检查配置文件中的模型名称是否正确：
   ```yaml
   text_generation:
     gpt:
       model: "gpt-4o-mini"  # 确保模型名称正确
   ```
2. 验证所选模型在对应服务商中可用
3. 检查服务商是否更新了模型名称或版本

### 2. 生成质量不佳

**问题**：生成的内容质量不符合预期

**解决方案**：
1. 尝试更换更高质量的模型：
   ```yaml
   text_generation:
     primary_service: "gpt"
     gpt:
       model: "gpt-4o"  # 从gpt-4o-mini升级到gpt-4o
   ```
2. 调整生成参数：
   ```yaml
   text_generation:
     temperature: 0.7  # 调整创造性
     max_tokens: 2000  # 增加最大token数
   ```
3. 检查提示词是否足够清晰和具体

## 性能问题

### 1. 内存不足

**问题**：系统运行缓慢或出现内存溢出错误

**解决方案**：
1. 减少并发任务数：
   ```yaml
   system:
     max_concurrent_tasks: 5  # 从10减少到5
   ```
2. 启用临时文件清理：
   ```yaml
   output:
     cleanup_temp: true
   ```
3. 增加系统交换空间（swap）

### 2. 生成速度慢

**问题**：视频生成耗时过长

**解决方案**：
1. 启用并行处理：
   ```yaml
   system:
     parallel_processing: true
   ```
2. 使用更快的模型（可能质量会有所降低）
3. 检查网络带宽是否足够

## Docker相关问题

### 1. 容器无法启动

**问题**：运行`docker-compose up`时容器启动失败

**解决方案**：
1. 检查Dockerfile和docker-compose.yml配置
2. 确保端口未被占用：
   ```bash
   docker-compose down
   docker-compose up --build
   ```
3. 查看容器日志：
   ```bash
   docker-compose logs producer
   ```

### 2. 文件权限问题

**问题**：容器内无法访问文件或目录

**解决方案**：
1. 检查挂载目录的权限：
   ```yaml
   volumes:
     - ./output:/app/output:Z  # 在SELinux系统中添加:Z标记
   ```
2. 在Dockerfile中设置正确的用户权限

## 测试问题

### 1. 测试失败

**问题**：运行测试时出现失败

**解决方案**：
1. 确保所有必需的环境变量已设置
2. 检查测试数据是否正确
3. 运行特定测试以定位问题：
   ```bash
   uv run pytest -v  # 详细输出
   uv run pytest -s  # 显示打印输出
   ```

### 2. 测试覆盖率不足

**问题**：覆盖率报告提示覆盖率不足

**解决方案**：
1. 为未覆盖的代码路径编写测试
2. 运行覆盖率报告查看具体缺失部分：
   ```bash
   uv run pytest --cov=core --cov-report=html
   open htmlcov/index.html
   ```

通过按照本指南进行故障排查，您应该能够解决大部分使用Producer系统时遇到的问题。如果问题仍然存在，请在GitHub上提交Issue以获得进一步帮助。