# 系统架构

Producer系统采用模块化、分层架构设计，确保各组件职责清晰、易于维护和扩展。

## 整体架构图

```
+---------------------+
|     CLI/API接口      |
+----------+----------+
           |
+----------v----------+
|    工作流引擎核心     |
+----------+----------+
           |
+----------v----------+
|   配置管理 | 成本控制   |
+----------+----------+
           |
+----------v----------+
|      适配器层        |
|  +----------------+  |
|  | 文本生成适配器  |  |
|  +----------------+  |
|  | 图像生成适配器  |  |
|  +----------------+  |
|  | 视频生成适配器  |  |
|  +----------------+  |
|  | 语音合成适配器  |  |
|  +----------------+  |
+---------------------+
```

## 核心组件

### 1. 工作流引擎 (Workflow Engine)

工作流引擎是系统的核心，负责协调整个视频制作流程。它将复杂的制作过程分解为多个阶段，并按顺序执行。

- **职责**：管理制作流程、调度各阶段任务、处理异常情况
- **主要类**：`WorkflowEngine`
- **关键特性**：
  - 支持自定义工作流步骤
  - 异步执行提高效率
  - 错误处理和重试机制

### 2. 配置管理 (Configuration Management)

配置管理模块负责系统的配置加载、验证和管理，支持多种配置源和环境。

- **职责**：加载配置、管理API密钥、提供配置查询接口
- **主要类**：`ConfigManager`
- **关键特性**：
  - 支持YAML配置文件
  - 环境变量覆盖
  - 配置项验证

### 3. 成本控制 (Cost Control)

成本控制模块监控和管理系统的API调用成本，确保在预算范围内运行。

- **职责**：预算管理、成本计算、超支检查
- **主要类**：`CostController`
- **关键特性**：
  - 实时成本跟踪
  - 预算限制设置
  - 成本报告生成

### 4. 适配器层 (Adapter Layer)

适配器层为系统提供了与各种AI服务交互的统一接口，支持多种服务提供商。

- **职责**：与AI服务通信、处理API响应、计算成本
- **主要类**：`BaseAdapter`, `GPTAdapter`, `GoogleAdapter`等
- **关键特性**：
  - 统一接口设计
  - 速率限制处理
  - 错误重试机制

## 数据流

1. **输入阶段**：用户通过CLI或API提供视频制作需求（标题、主题、时代等）
2. **剧本生成**：系统根据需求生成详细的剧本大纲
3. **场景规划**：将剧本分解为多个场景，规划每个场景的内容
4. **内容生成**：
   - 生成场景图像
   - 生成场景视频
   - 合成语音对话
5. **视频合成**：将所有内容按时间轴合成最终视频
6. **输出阶段**：生成视频文件和相关元数据

## 技术栈

- **编程语言**：Python 3.11+
- **包管理**：uv
- **异步框架**：asyncio
- **CLI框架**：Click
- **配置管理**：PyYAML
- **HTTP客户端**：httpx
- **日志系统**：Python标准库logging
- **测试框架**：pytest

通过这种架构设计，Producer系统实现了高内聚、低耦合，便于维护和扩展。