# 参数配置详解

本指南详细介绍了Producer系统的所有可配置参数、环境变量及其对项目行为的影响。

## 环境变量

环境变量用于配置API密钥和其他敏感信息，应保存在项目根目录的`.env`文件中。

### 必需的环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `GLM_API_KEY` | 智谱AI GLM模型API密钥 | `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `DEEPSEEK_API_KEY` | DeepSeek模型API密钥 | `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `GOOGLE_AI_API_KEY` | Google AI服务API密钥 | `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `Kling_API_KEY` | 可灵视频生成API密钥 | `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |

### 可选的环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | OpenAI GPT模型API密钥（可选） | 无 |
| `ELEVENLABS_API_KEY` | ElevenLabs语音合成API密钥（可选） | 无 |

## 配置文件

主配置文件位于`config/config.yaml`，包含系统的详细配置选项。

### 系统基础配置

```yaml
system:
  name: "历史短剧视频制作系统"  # 系统名称
  version: "1.0.0"             # 系统版本
  mode: "hybrid_optimized"     # 运行模式：hybrid_optimized | zero_cost
  debug: false                  # 调试模式
  log_level: "INFO"            # 日志级别：DEBUG, INFO, WARNING, ERROR
```

### 输出配置

```yaml
output:
  base_dir: "./output"         # 基础输出目录
  temp_dir: "./temp"           # 临时文件目录
  video_format: "mp4"          # 视频格式
  audio_format: "wav"          # 音频格式
  image_format: "png"          # 图像格式
  cleanup_temp: true            # 是否清理临时文件
```

### 预算控制

```yaml
budget:
  daily_limit_usd: 10.0         # 每日预算限制（美元）
  monthly_limit_usd: 100.0      # 每月预算限制（美元）
  cost_tracking: true           # 是否启用成本追踪
  alert_threshold: 0.8          # 警告阈值（80%时发出警告）
```

### 文本生成配置

```yaml
text_generation:
  primary_model: "glm-4-flash" # 主要模型
  fallback_model: "deepseek-chat" # 备用模型
  max_tokens: 4000              # 最大token数
  temperature: 0.7              # 温度参数
  timeout: 30                   # 超时时间（秒）
```

### 图像生成配置

```yaml
image_generation:
  primary_service: "google"     # 主要服务
  fallback_service: "flux"     # 备用服务
  resolution: "1024x1024"      # 分辨率
  quality: "high"              # 质量等级
  
  # Google Imagen 配置
  google:
    model: "imagen-4.0-generate-preview-06-06" # 模型名称
    aspect_ratio: "1:1"        # 宽高比
    num_images: 1               # 生成图片数量
    person_generation: "allow_adult" # 人物生成策略
```

### 视频生成配置

```yaml
video_generation:
  primary_service: "google"     # 主要服务
  fallback_service: "svd"      # 备用服务
  model: "veo-2"               # 模型选择
  resolution: "1080p"          # 分辨率
  fps: 24                       # 帧率
  duration: 5                  # 默认时长（秒）
  quality: "high"              # 质量等级
  aspect_ratio: "16:9"         # 宽高比
  style: "realistic"           # 风格
```

### 语音合成配置

```yaml
voice_synthesis:
  primary_service: "edgetts"    # 主要服务
  fallback_service: "cosyvoice" # 备用服务
  language: "zh-CN"            # 语言
  
  # 默认语音设置
  voice_id: "zh-CN-XiaoxiaoNeural"   # 默认语音ID
  male_voice_id: "zh-CN-YunxiNeural" # 男声配置
  female_voice_id: "zh-CN-XiaoxiaoNeural" # 女声配置
  
  # 语音参数
  speed: 1.0                    # 语速
  pitch: 1.0                    # 音调
  voice_style: "neutral"        # 语音风格
  auto_gender_selection: true   # 自动根据角色性别选择语音
```

## 不同场景下的推荐配置

### 开发测试场景

对于开发和测试环境，建议使用成本较低的配置：

```yaml
system:
  mode: "zero_cost"
  debug: true
  log_level: "DEBUG"

budget:
  daily_limit_usd: 1.0
  monthly_limit_usd: 10.0

text_generation:
  primary_model: "glm-4-flash"  # 使用免费额度模型

image_generation:
  primary_service: "flux"       # 使用本地免费模型

video_generation:
  primary_service: "svd"        # 使用开源模型

voice_synthesis:
  primary_service: "edgetts"    # 使用免费服务
```

### 生产环境场景

对于生产环境，建议使用高质量的配置：

```yaml
system:
  mode: "hybrid_optimized"
  debug: false
  log_level: "INFO"

budget:
  daily_limit_usd: 20.0
  monthly_limit_usd: 200.0

text_generation:
  primary_model: "gpt-4o-mini"  # 使用高质量模型

image_generation:
  primary_service: "google"     # 使用高质量服务
  google:
    model: "imagen-4.0-generate-preview-06-06"

video_generation:
  primary_service: "google"     # 使用高质量服务
  model: "veo-2"

voice_synthesis:
  primary_service: "elevenlabs" # 使用高质量服务
```

### 批量处理场景

对于批量处理场景，建议优化并行处理和资源管理：

```yaml
system:
  mode: "hybrid_optimized"
  parallel_processing: true
  max_concurrent_tasks: 10
  max_memory_usage: "4GB"

output:
  cleanup_temp: false  # 保留临时文件以供调试

budget:
  daily_limit_usd: 50.0
  monthly_limit_usd: 500.0
```

通过合理配置这些参数，您可以根据具体需求优化Producer系统的行为和性能。