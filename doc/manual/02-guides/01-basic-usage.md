# 基础用法

本指南介绍了Producer系统的核心功能和基本使用方法。

## CLI命令

Producer提供了一个功能丰富的命令行界面(CLI)，用于执行各种操作。

### 制作单个视频

使用`produce`命令制作单个历史短剧视频：

```bash
uv run python -m producer.cli produce -t "剧本标题" -e "历史朝代"
```

参数说明：
- `-t, --title`：剧本标题（必需）
- `-e, --era`：历史时代（默认：明朝）
- `--theme`：剧本主题（默认：历史剧情）
- `-d, --duration`：视频时长（秒，默认：180）
- `-o, --output`：输出目录（默认：./output）
- `-c, --config`：配置文件路径（默认：./config/config.yaml）
- `-b, --budget`：预算限制（美元，默认：1.0）

示例：
```bash
uv run python -m producer.cli produce \
  --title "唐朝盛世" \
  --theme "文化繁荣" \
  --era "唐朝" \
  --duration 240
```

### 批量制作

使用`batch`命令批量制作视频：

```bash
uv run python -m producer.cli batch scripts.json
```

`scripts.json`文件格式示例：
```json
[
  {
    "title": "宋朝科技",
    "theme": "科技发展",
    "era": "宋朝",
    "duration": 180
  },
  {
    "title": "元朝疆域",
    "theme": "疆域扩张",
    "era": "元朝",
    "duration": 200
  }
]
```

### 查看系统状态

使用`status`命令查看系统状态和统计信息：

```bash
uv run python -m producer.cli status
```

### 运行测试

使用`test`命令运行系统测试：

```bash
uv run python -m producer.cli test
```

### 查看版本信息

使用`--version`参数查看版本信息：

```bash
uv run python -m producer.cli --version
```

## Python API

除了CLI，Producer还提供了Python API，允许开发者在自己的应用程序中集成视频生成功能。

### 基本用法

```python
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

# 初始化系统
config = ConfigManager()
cost_controller = CostController(config)
workflow = WorkflowEngine(config, cost_controller)

# 创建剧本数据
script_data = ScriptData(
    script_id="example_001",
    title="明朝风云",
    theme="宫廷斗争",
    era="明朝",
    total_duration=180
)

# 执行制作流程
result = await workflow.execute_workflow(script_data)
print(f"制作状态: {result.status}")
print(f"总成本: ${result.total_cost:.4f}")
```

### 配置管理

```python
# 加载自定义配置文件
config = ConfigManager(config_path="./config/custom_config.yaml")

# 获取特定配置项
api_key = config.get_api_key("google")
model_config = config.get_model_config("text_generation")
```

### 成本控制

```python
# 检查预算状态
cost_controller = CostController(config)
budget_status = cost_controller.get_budget_status()
print(f"日预算剩余: ${budget_status.daily_remaining:.2f}")

# 检查是否超出预算
if cost_controller.is_over_budget():
    print("已超出预算限制")
```

## 输出文件结构

制作完成的视频和相关文件将保存在指定的输出目录中，文件结构如下：

```
output/
├── script_example_001/
│   ├── script.json          # 剧本数据
│   ├── scenes/
│   │   ├── scene_001.png    # 场景图像
│   │   └── scene_002.png
│   ├── videos/
│   │   ├── scene_001.mp4    # 场景视频
│   │   └── scene_002.mp4
│   ├── audios/
│   │   ├── dialogue_001.wav # 对话音频
│   │   └── dialogue_002.wav
│   └── final_video.mp4     # 最终合成视频
└── logs/
    └── workflow.log         # 工作流日志
```

通过掌握这些基础用法，您可以开始使用Producer系统制作历史短剧视频。