# 实例代码与用例

本指南提供了一系列真实场景的示例代码和项目配置，用户可以直接参考和使用。

## 基础示例

### 制作单个视频

```python
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

# 初始化系统
config = ConfigManager()
cost_controller = CostController(config)
workflow = WorkflowEngine(config, cost_controller)

# 创建剧本数据
script_data = ScriptData(
    script_id="example_001",
    title="明朝风云",
    theme="宫廷斗争",
    era="明朝",
    total_duration=180
)

# 执行制作流程
result = await workflow.execute_workflow(script_data)
print(f"制作状态: {result.status}")
print(f"总成本: ${result.total_cost:.4f}")
```

### 批量制作视频

```python
import asyncio
import json
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

# 批量脚本数据
batch_scripts = [
    {
        "title": "唐朝文化",
        "theme": "文化繁荣",
        "era": "唐朝",
        "duration": 180
    },
    {
        "title": "宋朝科技",
        "theme": "科技发展",
        "era": "宋朝",
        "duration": 200
    }
]

async def batch_produce():
    # 初始化系统
    config = ConfigManager()
    cost_controller = CostController(config)
    workflow = WorkflowEngine(config, cost_controller)
    
    # 批量处理
    results = []
    for script_info in batch_scripts:
        script_data = ScriptData(
            script_id=f"script_{script_info['title'].replace(' ', '_')}",
            title=script_info['title'],
            theme=script_info['theme'],
            era=script_info['era'],
            total_duration=script_info['duration']
        )
        
        result = await workflow.execute_workflow(script_data)
        results.append(result)
        
        print(f"完成制作: {script_info['title']}, 状态: {result.status}")
    
    return results

# 运行批量处理
asyncio.run(batch_produce())
```

## 高级示例

### 自定义剧本生成

```python
from core.chains.outline_chain import OutlineChain, OutlineRequest
from core.config import ConfigManager
from core.cost_control import CostController

# 初始化配置和成本控制器
config = ConfigManager()
cost_controller = CostController(config)

# 创建自定义剧本大纲链路
outline_chain = OutlineChain(config, cost_controller)

# 自定义请求
request = OutlineRequest(
    title="清朝兴衰",
    theme="王朝更替",
    era="清朝",
    custom_prompt="请重点描述清朝的兴起和衰落过程，突出关键历史事件和人物。"
)

# 生成剧本大纲
outline_result = await outline_chain.generate_outline(request)
print(f"剧本大纲: {outline_result.outline}")
```

### 多语言支持

```python
# 配置多语言支持
config = ConfigManager()
config.set("voice_synthesis.language", "en-US")
config.set("voice_synthesis.voice_id", "en-US-JennyNeural")

# 创建英文历史剧本数据
script_data = ScriptData(
    script_id="english_example_001",
    title="The Rise and Fall of the Roman Empire",
    theme="Historical Events",
    era="Ancient Rome",
    summary="A short drama about the rise and fall of the Roman Empire",
    total_duration=240
)
```

### 成本控制示例

```python
from core.cost_control import CostController
from core.config import ConfigManager

# 初始化成本控制器
config = ConfigManager()
cost_controller = CostController(config)

# 检查预算状态
budget_status = cost_controller.get_budget_status()
print(f"日预算剩余: ${budget_status.daily_remaining:.2f}")
print(f"月预算剩余: ${budget_status.monthly_remaining:.2f}")

# 设置临时预算限制
cost_controller.set_temporary_limit(5.0)  # 临时设置5美元预算

# 检查是否超出预算
if cost_controller.is_over_budget():
    print("已超出预算限制，停止制作")
else:
    print("预算充足，可以继续制作")
```

## 配置示例

### 开发环境配置

```yaml
# config/dev_config.yaml
system:
  name: "历史短剧视频制作系统 - 开发环境"
  mode: "zero_cost"
  debug: true
  log_level: "DEBUG"

budget:
  daily_limit_usd: 1.0
  monthly_limit_usd: 10.0

output:
  base_dir: "./output/dev"
  temp_dir: "./temp/dev"

# 使用免费或本地模型
image_generation:
  primary_service: "flux"

video_generation:
  primary_service: "svd"

voice_synthesis:
  primary_service: "edgetts"
```

### 生产环境配置

```yaml
# config/prod_config.yaml
system:
  name: "历史短剧视频制作系统 - 生产环境"
  mode: "hybrid_optimized"
  debug: false
  log_level: "INFO"

budget:
  daily_limit_usd: 20.0
  monthly_limit_usd: 200.0

output:
  base_dir: "./output/prod"
  temp_dir: "./temp/prod"
  cleanup_temp: true

# 使用高质量付费模型
image_generation:
  primary_service: "google"
  google:
    model: "imagen-4.0-generate-preview-06-06"

video_generation:
  primary_service: "google"
  model: "veo-2"

voice_synthesis:
  primary_service: "elevenlabs"
```

### 批量处理配置

```yaml
# config/batch_config.yaml
system:
  name: "历史短剧视频制作系统 - 批量处理"
  mode: "hybrid_optimized"
  parallel_processing: true
  max_concurrent_tasks: 10
  max_memory_usage: "4GB"

budget:
  daily_limit_usd: 50.0
  monthly_limit_usd: 500.0

output:
  base_dir: "./output/batch"
  temp_dir: "./temp/batch"
  cleanup_temp: false  # 保留临时文件以供调试
```

## CLI使用示例

### 制作特定历史时期的视频

```bash
# 制作唐朝文化视频
uv run python -m producer.cli produce \
  --title "唐朝文化繁荣" \
  --theme "文化发展" \
  --era "唐朝" \
  --duration 200

# 制作宋朝科技视频
uv run python -m producer.cli produce \
  --title "宋朝科技成就" \
  --theme "科技创新" \
  --era "宋朝" \
  --duration 180
```

### 批量处理脚本

```json
[
  {
    "title": "元朝疆域扩张",
    "theme": "军事征服",
    "era": "元朝",
    "duration": 220
  },
  {
    "title": "明朝海上贸易",
    "theme": "经济发展",
    "era": "明朝",
    "duration": 190
  },
  {
    "title": "清朝文字狱",
    "theme": "政治控制",
    "era": "清朝",
    "duration": 160
  }
]
```

```bash
# 批量处理上述脚本
uv run python -m producer.cli batch scripts.json
```

通过这些示例，您可以更好地理解和使用Producer系统的各种功能。