# 快速上手指南

本指南将帮助您快速启动Producer系统，并制作您的第一个历史短剧视频。

## 环境准备

### 系统要求

- Python 3.11或更高版本
- uv包管理器（推荐）
- Git版本控制工具

### 安装步骤

1. 克隆项目仓库：
   ```bash
   git clone https://github.com/matrix/producer.git
   cd producer
   ```

2. 安装uv包管理器（推荐）：
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

3. 安装项目依赖：
   ```bash
   uv sync
   ```

## 配置设置

1. 复制环境变量模板：
   ```bash
   cp .env.template .env
   ```

2. 编辑配置文件，填入必要的API密钥：
   ```bash
   nano .env
   ```

## 运行测试

在制作视频之前，建议先运行系统测试以确保所有组件正常工作：

```bash
uv run python test_workflow.py
```

或者使用CLI工具：

```bash
uv run python -m producer.cli test
```

## 制作第一个视频

使用CLI工具制作您的第一个历史短剧视频：

```bash
uv run python -m producer.cli produce \
  --title "明朝风云" \
  --theme "宫廷斗争" \
  --era "明朝" \
  --duration 180
```

## 查看结果

制作完成的视频将保存在`./output`目录中，您可以查看生成的视频文件和相关日志。

## 下一步

- 查看[基础用法](../02-guides/01-basic-usage.md)文档，了解更多功能。
- 阅读[配置详解](../02-guides/03-configuration.md)文档，了解如何优化配置。
- 探索[高级用法](../02-guides/02-advanced-usage.md)文档，掌握更多高级功能。