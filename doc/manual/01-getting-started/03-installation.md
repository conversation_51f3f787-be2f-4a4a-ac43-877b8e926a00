# 详细安装说明

本指南提供了在不同操作系统和环境下的详细安装步骤。

## 系统要求

- Python 3.11或更高版本
- 至少4GB内存
- 至少10GB可用磁盘空间
- 稳定的网络连接

## 操作系统安装指南

### macOS

1. 安装Homebrew（如果尚未安装）：
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. 安装Python 3.11+：
   ```bash
   brew install python
   ```

3. 安装Git：
   ```bash
   brew install git
   ```

4. 安装uv包管理器：
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

### Ubuntu/Debian

1. 更新包列表：
   ```bash
   sudo apt update
   ```

2. 安装Python 3.11+和相关工具：
   ```bash
   sudo apt install python3 python3-pip git curl
   ```

3. 安装uv包管理器：
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

### Windows

1. 下载并安装Python 3.11+：
   - 访问[Python官网](https://www.python.org/downloads/)
   - 下载Windows安装包并运行安装程序
   - 确保勾选"Add Python to PATH"选项

2. 安装Git：
   - 访问[Git官网](https://git-scm.com/downloads)
   - 下载Windows版本并运行安装程序

3. 安装uv包管理器：
   - 打开命令提示符或PowerShell
   - 运行以下命令：
     ```bash
     curl -LsSf https://astral.sh/uv/install.sh | sh
     ```

## 环境配置

### 克隆项目

```bash
git clone https://github.com/matrix/producer.git
cd producer
```

### 安装依赖

```bash
uv sync
```

### 配置API密钥

1. 复制环境变量模板：
   ```bash
   cp .env.template .env
   ```

2. 编辑`.env`文件，填入必要的API密钥：
   ```bash
   nano .env
   ```

## Docker环境搭建

### 安装Docker

根据您的操作系统，参考[Docker官方文档](https://docs.docker.com/get-docker/)安装Docker。

### 构建镜像

```bash
docker build -t producer .
```

### 使用docker-compose启动

```bash
docker-compose up -d
```

### 查看日志

```bash
docker-compose logs -f producer
```

## 验证安装

运行系统测试以验证安装是否成功：

```bash
uv run python test_workflow.py
```

或者使用CLI工具：

```bash
uv run python -m producer.cli test
```

如果测试通过，说明安装成功。