# 编码规范

为了保持代码的一致性和可读性，Producer项目遵循一套严格的编码规范。

## Python编码规范

### 1. 代码风格

遵循PEP 8编码规范，使用Black代码格式化工具。

#### 命名规范

- 类名使用PascalCase：`ConfigManager`
- 函数和变量名使用snake_case：`get_api_key`
- 常量使用UPPER_SNAKE_CASE：`DEFAULT_CONFIG_PATH`
- 私有成员使用单下划线前缀：`_private_method`

#### 代码格式化

使用Black自动格式化代码：

```bash
# 安装Black
uv add black

# 格式化代码
uv run black .
```

### 2. 类型提示

全面使用类型提示提高代码可读性和可靠性。

```python
from typing import Optional, Dict, List


def get_api_key(self, service_name: str) -> Optional[str]:
    pass


def get_model_config(self, model_type: str) -> Dict[str, any]:
    pass


class WorkflowEngine:
    def __init__(self, config: Config<PERSON><PERSON><PERSON>, cost_controller: CostController):
        pass

    async def execute_workflow(self, script_data: ScriptData) -> WorkflowResult:
        pass
```

### 3. 文档字符串

为所有公共接口编写详细的文档字符串，使用Google风格。

```python
class ConfigManager:
    """配置管理器负责加载和管理系统的各种配置。

    该类提供了一种统一的方式来访问和管理系统的配置项，
    包括API密钥、模型配置、输出设置等。
    """

    def get_api_key(self, service_name: str) -> Optional[str]:
        """获取指定服务的API密钥。

        Args:
            service_name (str): 服务名称 (e.g., "google", "openai")

        Returns:
            Optional[str]: API密钥，如果未找到则返回None
        """
        pass
```

### 4. 异常处理

#### 异常层次结构

定义清晰的异常层次结构：

```python
class ProducerError(Exception):
    """Producer系统基础异常类。"""
    pass


class AdapterError(ProducerError):
    """适配器相关异常。"""
    pass


class RateLimitError(AdapterError):

    """速率限制异常。"""
    pass
```

#### 异常处理最佳实践

1. 捕获具体异常类型
2. 提供有意义的错误信息
3. 记录错误日志

```python
import logging

logger = logging.getLogger(__name__)

try:
    result = await adapter.generate(prompt)
except RateLimitError as e:
    logger.warning(f"速率限制错误: {e}")
    # 处理速率限制
except AdapterError as e:
    logger.error(f"适配器错误: {e}")
    # 处理其他适配器错误
```

### 5. 模块和导入

#### 模块结构

```python
# 1. 模块文档字符串
"""配置管理模块。

该模块提供了配置加载、验证和管理功能。
"""

# 2. 标准库导入
import os
import json
from typing import Optional, Dict

# 3. 第三方库导入
import yaml
import httpx

# 4. 本地库导入
from core.models import ScriptData
from adapters.base import BaseAdapter

# 5. 模块级常量
DEFAULT_CONFIG_PATH = "./config/config.yaml"

# 6. 模块级类和函数
class ConfigManager:
    pass
```

#### 导入规范

- 按标准库、第三方库、本地库顺序分组导入
- 避免循环导入
- 使用相对导入处理包内引用

### 6. 异步编程

#### 异步函数定义

```python
async def generate_content(self, prompt: str) -> AdapterResult:
    """异步生成内容。"""
    pass
```

#### 异步上下文管理

```python
async with httpx.AsyncClient() as client:
    response = await client.post(url, json=payload)
```

#### 异步并发

```python
import asyncio

async def batch_process(self, tasks: List[Task]):
    """批量处理任务。"""
    results = await asyncio.gather(*[task.execute() for task in tasks])
    return results
```

## Git提交规范

### 提交信息格式

使用清晰的提交信息格式：

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型（Type）

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整（不影响代码运行）
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 范例

```
feat(config): 添加多环境配置支持

支持通过环境变量切换开发、测试和生产环境配置

Closes #123
```

```
fix(adapter): 修复Google适配器速率限制处理

正确处理Google API的速率限制响应，实现指数退避重试机制
```

## 代码审查检查清单

### 代码质量

- [ ] 代码符合PEP 8规范
- [ ] 使用了类型提示
- [ ] 编写了文档字符串
- [ ] 没有未使用的导入
- [ ] 没有未使用的变量

### 功能实现

- [ ] 实现了预期功能
- [ ] 处理了边界条件
- [ ] 包含了错误处理
- [ ] 添加了单元测试

### 测试

- [ ] 单元测试覆盖率达标
- [ ] 集成测试通过
- [ ] 测试用例覆盖主要场景

### 文档

- [ ] 更新了相关文档
- [ ] API文档字符串完整
- [ ] README更新（如需要）

遵循这些编码规范有助于保持代码库的一致性和高质量。