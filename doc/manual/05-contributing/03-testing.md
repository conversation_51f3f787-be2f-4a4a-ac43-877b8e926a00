# 测试指南

Producer项目采用全面的测试策略，确保代码质量和系统稳定性。

## 测试策略

### 1. 测试类型

#### 单元测试

测试单个函数或类的功能，确保其按预期工作。

- 位置：`tests/unit/`
- 命名：`test_*.py` 或 `*_test.py`
- 运行：`uv run pytest tests/unit/`

#### 集成测试

测试多个模块间的交互，验证系统组件协同工作。

- 位置：`tests/integration/`
- 命名：`test_*.py` 或 `*_test.py`
- 运行：`uv run pytest tests/integration/`

#### API测试

测试与外部API的连接和交互。

- 位置：`tests/api_tests/`
- 命名：`test_*.py` 或 `*_test.py`
- 运行：`uv run pytest tests/api_tests/`

### 2. 测试工具

- **测试框架**：pytest
- **Mock库**：unittest.mock
- **测试覆盖率**：coverage.py

## 编写测试

### 1. 测试结构

```python
import pytest
from core.config import ConfigManager


class TestConfigManager:
    """配置管理器测试类。"""
    
    def test_get_api_key(self):
        """测试获取API密钥功能。"""
        # 准备测试数据
        config = ConfigManager()
        
        # 执行测试
        api_key = config.get_api_key("google")
        
        # 验证结果
        assert api_key is not None
        assert isinstance(api_key, str)
        assert len(api_key) > 0
```

### 2. Fixture使用

使用pytest fixture管理测试依赖和设置。

```python
import pytest
from core.config import ConfigManager


@pytest.fixture
def config_manager():
    """配置管理器fixture。"""
    return ConfigManager()


class TestConfigManager:
    def test_get_api_key(self, config_manager):
        """测试获取API密钥功能。"""
        api_key = config_manager.get_api_key("google")
        assert api_key is not None
```

### 3. Mock和Patch

使用mock模拟外部依赖和复杂对象。

```python
from unittest.mock import patch, AsyncMock


class TestAdapter:
    @patch('adapters.text.gpt_adapter.httpx.AsyncClient')
    async def test_generate(self, mock_client):
        """测试适配器生成功能。"""
        # 设置mock
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "choices": [{"message": {"content": "测试内容"}}]
        }
        mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
        
        # 执行测试
        adapter = GPTAdapter(config, system_config, cost_controller)
        result = await adapter.generate("测试提示")
        
        # 验证结果
        assert result.content == "测试内容"
```

### 4. 参数化测试

使用参数化测试覆盖多种输入情况。

```python
import pytest


class TestCostController:
    @pytest.mark.parametrize("service,model,expected_cost", [
        ("openai", "gpt-4o-mini", 0.002),
        ("google", "gemini-pro", 0.001),
        ("openai", "gpt-4o", 0.03),
    ])
    def test_cost_calculation(self, service, model, expected_cost):
        """测试成本计算功能。"""
        # 测试实现
        pass
```

## 运行测试

### 1. 运行所有测试

```bash
# 运行所有测试
uv run pytest

# 运行所有测试并显示覆盖率
uv run pytest --cov=.
```

### 2. 运行特定测试

```bash
# 运行特定文件
uv run pytest tests/unit/test_config.py

# 运行特定类
uv run pytest tests/unit/test_config.py::TestConfigManager

# 运行特定方法
uv run pytest tests/unit/test_config.py::TestConfigManager::test_get_api_key
```

### 3. 按标记运行测试

```bash
# 运行集成测试
uv run pytest -m "integration"

# 运行API测试
uv run pytest -m "api"

# 运行快速测试
uv run pytest -m "not slow"
```

### 4. 测试环境配置

```bash
# 复制测试环境配置
cp .env.template .env

# 编辑.env文件，添加必要的API密钥
# GOOGLE_AI_API_KEY=your_google_api_key
# OPENAI_API_KEY=your_openai_api_key
```

## 测试覆盖率

### 1. 生成覆盖率报告

```bash
# 运行测试并生成覆盖率报告
uv run pytest --cov=core --cov=adapters --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

### 2. 覆盖率要求

- 单元测试覆盖率应达到90%以上
- 关键路径覆盖率应达到100%
- 新增代码覆盖率应达到100%

## 测试最佳实践

### 1. 测试设计

- 每个测试只验证一个功能点
- 测试应独立于其他测试
- 使用描述性的测试名称
- 测试数据应易于理解

### 2. 测试数据

- 使用真实但脱敏的数据
- 避免硬编码敏感信息
- 使用工厂函数生成测试数据

```python
import pytest
from core.models import ScriptData


@pytest.fixture
def sample_script_data():
    """示例剧本数据fixture。"""
    return ScriptData(
        script_id="test_001",
        title="测试标题",
        theme="测试主题",
        era="测试时代",
        total_duration=180
    )
```

### 3. 异步测试

测试异步代码时使用async/await：

```python
import pytest


class TestWorkflowEngine:
    @pytest.mark.asyncio
    async def test_execute_workflow(self, workflow_engine, sample_script_data):
        """测试工作流执行功能。"""
        result = await workflow_engine.execute_workflow(sample_script_data)
        assert result.status == "success"
```

### 4. 测试调试

```bash
# 详细输出
uv run pytest -v

# 显示测试执行过程
uv run pytest -s

# 在第一个失败处停止
uv run pytest -x

# 失败时显示本地变量
uv run pytest -l
```

通过遵循这些测试指南，您可以编写高质量的测试，确保Producer系统的稳定性和可靠性。