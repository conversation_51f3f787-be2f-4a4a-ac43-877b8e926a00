# 贡献指南

欢迎对Producer项目做出贡献！我们非常感谢任何形式的贡献，包括代码、文档、测试和反馈。

## 贡献方式

### 1. 报告问题

如果您发现bug或有功能建议，请在GitHub上创建Issue：

1. 检查是否已有相关Issue
2. 使用清晰的标题描述问题
3. 详细描述问题复现步骤
4. 提供系统环境信息

### 2. 提交代码

#### Fork仓库

1. Fork项目到您的GitHub账户
2. 克隆Fork的仓库到本地

```bash
git clone https://github.com/your-username/producer.git
cd producer
```

#### 创建分支

为您的修改创建新分支：

```bash
git checkout -b feature/your-feature-name
```

#### 安装开发环境

```bash
# 安装uv包管理器
# 参考 https://docs.astral.sh/uv/

# 安装项目依赖
uv sync --dev
```

#### 编写代码

1. 遵循项目的编码规范
2. 添加必要的单元测试
3. 确保所有测试通过

```bash
# 运行测试
uv run pytest
```

#### 提交更改

```bash
# 添加修改文件
git add .

# 提交更改
git commit -m "Add feature: your feature description"

# 推送到GitHub
git push origin feature/your-feature-name
```

#### 创建Pull Request

1. 在GitHub上创建Pull Request
2. 填写PR描述，说明修改内容和目的
3. 等待代码审查

### 3. 改进文档

文档改进同样重要：

1. 修复错别字和语法错误
2. 补充使用示例
3. 更新过时信息
4. 翻译文档

### 4. 参与讨论

1. 回答其他用户的问题
2. 参与功能设计讨论
3. 提供使用反馈

## 开发环境搭建

### 系统要求

- Python 3.11或更高版本
- uv包管理器
- Git

### 安装步骤

```bash
# 1. 安装uv
# 参考 https://docs.astral.sh/uv/

# 2. 克隆项目
git clone https://github.com/your-username/producer.git
cd producer

# 3. 安装依赖
uv sync --dev

# 4. 配置环境变量
# 复制.env.template并按需修改
cp .env.template .env
```

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_config.py

# 运行带标记的测试
uv run pytest -m "integration"
```

## 代码规范

### Python代码规范

1. 遵循PEP 8编码规范
2. 使用类型提示
3. 编写文档字符串
4. 保持代码简洁

### Git提交规范

1. 使用清晰的提交信息
2. 每个提交只包含一个逻辑变更
3. 使用动词开头（Add, Fix, Update等）

```bash
# 好的提交信息
"Add support for Google Imagen API"
"Fix cost calculation for video generation"
"Update documentation for adapter configuration"

# 避免的提交信息
"fix bugs"
"update code"
"changes"
```

## 测试要求

### 单元测试

1. 为新功能编写单元测试
2. 确保测试覆盖率
3. 测试边界条件

### 集成测试

1. 测试模块间交互
2. 验证API调用
3. 检查配置加载

## 文档要求

1. 为公共API编写文档字符串
2. 更新相关使用文档
3. 保持文档与代码同步

## 代码审查流程

1. 提交Pull Request
2. 自动化测试检查
3. 代码审查
4. 修改和反馈
5. 合并到主分支

感谢您的贡献！