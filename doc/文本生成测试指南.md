# 文本生成测试指南

## 概述

为了解决测试链路太长、调试困难的问题，我们创建了专门的文本生成测试功能。这个测试方案专注于验证文本生成的核心功能，避免复杂的图像和视频生成，让您能够快速验证和调试文本链路。

## 核心优势

✅ **测试速度快** - 避免耗时的图像/视频生成  
✅ **成本低廉** - 仅使用文本生成API，成本可控  
✅ **易于调试** - 专注文本链路，问题定位精准  
✅ **结果可读** - 直接查看和验证文本输出  
✅ **独立运行** - 不依赖完整的工作流程  

## 测试内容

### 1. 大纲生成 (OutlineChain)
- 根据主题、朝代、时长等要求生成剧本大纲
- 包括角色设定、场景安排、情节发展
- 验证角色数量、场景数量、内容质量

### 2. 场景生成 (SceneChain)
- 将大纲中的场景扩展为详细制作指令
- 包括视觉描述、镜头设计、角色动作
- 验证镜头数量、时长分配、描述质量

### 3. 对话生成 (DialogueChain)
- 为场景生成符合历史背景的角色对话
- 包括语言风格、情感表达、时间控制
- 验证对话行数、角色一致性、历史准确性

## 快速开始

### 1. 环境准备

确保已安装项目依赖：
```bash
cd /Volumes/mini_matrix/github/producer
uv sync
```

### 2. 配置API密钥

在 `.env` 文件中设置必要的API密钥：
```bash
# GLM API密钥（必需）
GLM_API_KEY=your_glm_api_key_here

# DeepSeek API密钥（可选，作为备用）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

### 3. 运行测试

使用简化的运行脚本：
```bash
python run_text_test.py
```

或者使用uv：
```bash
uv run run_text_test.py
```

### 4. 查看结果

测试结果会保存在 `output/text_test_results/` 目录中：
- `text_chain_test_YYYYMMDD_HHMMSS.json` - 测试结果摘要
- `test.log` - 详细测试日志

## 测试流程

```
🚀 启动测试
    ↓
🔧 初始化环境
    ↓
📝 测试大纲生成
    ↓
🎬 测试场景生成（依赖大纲）
    ↓
💬 测试对话生成（依赖场景）
    ↓
📊 生成测试报告
```

## 配置说明

### 专用配置文件

测试使用专门的配置文件 `config/text_test_config.yaml`，主要特点：

- **简化配置** - 只包含文本生成相关配置
- **调试友好** - 开启详细日志和调试信息
- **成本控制** - 设置较低的预算限制
- **错误处理** - 遇到错误立即停止，便于调试

### 关键配置项

```yaml
# 文本生成配置
text_generation:
  primary_model: "glm-4-flash"     # 主要模型
  fallback_model: "deepseek-chat"  # 备用模型
  max_tokens: 4000
  temperature: 0.7
  timeout: 60

# 测试配置
testing:
  default_duration: 3      # 默认3分钟短剧
  max_characters: 5        # 最多5个角色
  max_scenes: 8           # 最多8个场景
  save_intermediate_results: true
```

## 测试示例

### 默认测试数据

```python
# 项目信息
project = {
    "name": "文本生成测试项目",
    "budget": 100.0
}

# 大纲请求
outline_request = {
    "title": "明朝宫廷风云",
    "theme": "宫廷权谋斗争",
    "dynasty": "明朝",
    "duration": 3,  # 3分钟
    "key_elements": ["权谋", "宫廷", "忠诚", "背叛"]
}
```

### 预期输出

```
✅ 大纲生成成功 (耗时: 2.34秒)
   角色数量: 4
   场景数量: 6
   角色列表:
     - 朱棣: 明成祖，雄才大略的皇帝...
     - 解缙: 翰林学士，才华横溢但性格耿直...
     - 胡广: 内阁首辅，老谋深算的权臣...

✅ 场景生成成功 (耗时: 3.12秒)
   镜头数量: 8
   总时长: 45秒
   镜头列表:
     - 全景 (5秒): 紫禁城大殿，金碧辉煌...
     - 特写 (3秒): 朱棣凝重的表情...

✅ 对话生成成功 (耗时: 2.87秒)
   对话行数: 12
   总时长: 30秒
   对话内容:
     朱棣: 解缙，朕问你，何为忠？
     解缙: 陛下，忠者，心无二志，言无虚假...
```

## 故障排除

### 常见问题

#### 1. API密钥错误
```
❌ 大纲生成测试失败: API authentication failed
```
**解决方案**：检查 `.env` 文件中的API密钥是否正确

#### 2. 网络连接问题
```
❌ 初始化失败: Connection timeout
```
**解决方案**：检查网络连接，或增加超时时间

#### 3. 配置文件缺失
```
❌ 初始化失败: Config file not found
```
**解决方案**：确保 `config/text_test_config.yaml` 文件存在

### 调试技巧

1. **查看详细日志**
   ```bash
   tail -f output/text_test_results/test.log
   ```

2. **检查中间结果**
   - 测试会保存每个步骤的中间结果
   - 查看 `output/text_test_results/` 目录中的JSON文件

3. **单步调试**
   - 可以修改 `test_text_chains.py` 中的测试逻辑
   - 注释掉某些测试步骤，专注调试特定功能

## 进阶使用

### 自定义测试数据

修改 `test_text_chains.py` 中的 `create_test_data()` 方法：

```python
def create_test_data(self) -> Dict[str, Any]:
    return {
        'outline_request': OutlineRequest(
            title="你的剧本标题",
            theme="你的主题",
            dynasty="你选择的朝代",
            duration=5,  # 自定义时长
            key_elements=["自定义", "关键词"]
        )
    }
```

### 批量测试

可以创建多个测试用例，验证不同场景：

```python
test_cases = [
    {"dynasty": "唐朝", "theme": "盛世繁华"},
    {"dynasty": "宋朝", "theme": "文人雅士"},
    {"dynasty": "清朝", "theme": "宫廷秘史"}
]
```

### 性能测试

监控各个链路的执行时间和成本：

```python
# 在测试结果中查看
result = {
    'outline': {'duration': 2.34, 'cost': 0.02},
    'scene': {'duration': 3.12, 'cost': 0.03},
    'dialogue': {'duration': 2.87, 'cost': 0.02}
}
```

## 与完整测试的对比

| 特性 | 文本测试 | 完整测试 |
|------|----------|----------|
| 测试时间 | 1-2分钟 | 10-30分钟 |
| 成本 | $0.05-0.10 | $1-5 |
| 调试难度 | 简单 | 复杂 |
| 结果可读性 | 高 | 中等 |
| 适用场景 | 开发调试 | 完整验证 |

## 下一步

当文本生成测试通过后，您可以：

1. **测试图像生成**
   ```bash
   uv run tests/adapters/test_image_adapters.py
   ```

2. **测试完整工作流**
   ```bash
   uv run test_workflow.py
   ```

3. **进行生产测试**
   ```bash
   uv run producer_cli.py
   ```

## 总结

文本生成测试功能为您提供了一个快速、经济、易于调试的测试方案。通过专注于文本链路，您可以：

- 快速验证核心文本生成功能
- 低成本调试和优化提示词
- 独立测试各个文本链路
- 为后续的完整测试打下基础

这种分层测试的方法大大提高了开发效率，降低了调试成本，是现代AI应用开发的最佳实践。