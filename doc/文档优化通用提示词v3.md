# 文档优化通用提示词v3

## 文档概述

本提示词为各类文档创作者提供系统化、专业化的优化指导，适用于技术文档、商业报告、学术论文、营销材料等。通过结构化评估和针对性建议，帮助提升文档的准确性、全面性、专业性和实用价值。

## 适用场景

- 技术文档：API文档、用户手册、技术规范等

- 商业文档：商业计划、市场分析、项目报告等

- 学术文档：研究论文、综述文章、学术报告等

- 营销材料：产品介绍、宣传文案、案例分析等

- 一般文档：工作总结、培训材料、会议纪要等

## 优化维度

### 1. 结构优化

**检查要点：**

- 标题层级清晰，符合规范，≤4级

- 章节安排合理，逻辑顺畅

- 段落长度适宜，信息密度均衡

- 信息架构贴合读者认知路径

- 导航元素（目录、索引、引用）完整有效

**优化标准：**

- 层次分明，逻辑连贯

- 重点突出，核心内容位置恰当

- 完整性，包含前言、正文、总结

### 2. 内容优化

**检查要点：**

- 核心信息完整，覆盖主题关键方面

- 论据充分，数据准确可靠

- 案例相关且具代表性

- 内容深度适中，针对目标读者

- 无冗余或重复内容

**优化标准：**

- 完整性，无重要遗漏

- 准确性，数据、引用无误

- 相关性，内容高度相关

- 平衡性，详略得当

### 3. 表达优化

**检查要点：**

- 语言表达专业、准确、简洁

- 术语使用恰当，必要时解释

- 句式结构多样，易于理解

- 语法和用词规范

- 风格符合目标场景和读者

**优化标准：**

- 专业性，术语规范

- 清晰度，语句简明

- 一致性，术语格式统一

- 可读性，段落适宜，重点突出

### 4. 逻辑优化

**检查要点：**

- 内容逻辑严密，论证合理

- 概念定义清晰，无逻辑漏洞

- 前后内容一致，无矛盾

- 因果关系明确，推理完整

- 结论基于充分论据

**优化标准：**

- 严密性，无明显漏洞

- 一致性，无矛盾

- 连贯性，内容衔接自然

- 有效性，结论合理

### 5. 细节优化

**检查要点：**

- 数据、案例、引用准确

- 格式、标点、拼写规范

- 图表、代码、公式清晰

- 参考资料完整，引用格式规范

- 必要补充说明或修正错误

**优化标准：**

- 准确性，细节无误

- 规范性，格式标准

- 完整性，细节齐全

- 清晰性，图表代码易辨

### 6. 增值优化

**检查要点：**

- 可添加实用工具提升价值

- 补充参考资料或扩展阅读

- 加入最佳实践或经验总结

- 增加互动元素或可视化内容

- 提供进一步行动建议

**优化标准：**

- 实用性，工具资源有价值

- 参考性，资料权威

- 创新性，观点独特

- 行动导向，建议明确

## 优化流程

### 流程步骤

1. **初评阶段**（5-10分钟）

   - 浏览整体结构和内容，识别明显问题和优化机会，确定优化重点和优先级

2. **详细分析**（15-30分钟）

   - 按六维度逐项评估，记录具体问题和改进点，收集补充资料

3. **建议生成**（10-20分钟）

   - 针对每个问题提出具体建议，按优先级排序，提供实施方法和资源

4. **优化实施**（视文档长度和复杂度）

   - 按优先级实施建议，验证优化效果，必要调整

## 文档类型针对性优化

### 技术文档

- 关注：准确性、一致性、可操作性

- 优化重点：API描述准确、代码示例完整、操作步骤清晰（建议配流程图或截图）

- 特殊要求：版本控制、变更记录、兼容性说明

### 商业报告

- 关注：数据支撑、结论明确、行动导向

- 优化重点：数据可视化、关键指标突出、建议可行

- 特殊要求：执行摘要、风险分析、ROI评估

### 学术论文

- 关注：严谨性、引用规范、创新性

- 优化重点：文献综述完整、方法论描述、结果分析

- 特殊要求：参考文献格式、研究伦理声明、局限性说明

### 营销材料

- 关注：吸引力、说服力、行动召唤

- 优化重点：价值主张清晰、受众定位、差异化优势

- 特殊要求：品牌一致性、合规声明、联系方式

## 输出要求

### 内容要求

- 优化后的完整文档内容直接体现在目标文档中，提升整体质量。

- 在聊天栏同步输出每一处修改点及修改原因，便于追踪和理解优化过程。

### 修改点输出格式示例

- 修改位置：章节/段落/句子

- 修改内容：原文/新内容

- 修改原因：结构优化/表达精简/细节补充等

### 优化过程说明

- 自动识别并修正结构、内容、表达、逻辑、细节、增值等方面的问题，并在文档中直接调整

- 每次修改均输出：修改位置、修改内容、修改原因（如结构优化、表达精简、细节补充等）

- 直接调整标题层级、章节结构、表达方式、数据引用、格式规范等

- 补充缺失信息、删除冗余内容、统一术语和风格

- 增加实用工具、参考资料、最佳实践等增值内容

## 优化工具与资源

### 常用工具

- 语法检查：Grammarly、微软编辑器、Copilot、ChatGPT

- 可读性分析：Hemingway Editor、可读性测试工具

- 结构优化：Markdown编辑器、文档大纲工具、流程图/思维导图工具

- 协作优化：Google Docs、Notion、语雀

### 评估标准

- 可读性评分：Flesch-Kincaid等级、Gunning Fog指数

- 质量评估：内容完整性、准确性、相关性评分表（如：每项1-5分，汇总总分）

- 用户体验：读者反馈、可用性测试

### 参考资料

- 《微软写作风格指南》（Microsoft Writing Style Guide）

- 《芝加哥格式手册》（The Chicago Manual of Style）

- 《技术文档写作之道》（Technical Writing Best Practices）

- 《商业写作与沟通》（Business Writing and Communication）

## 常见问题解答

**Q: 如何确定文档优化的优先级？**

A: 综合文档目标、读者需求和问题严重程度评估。优先解决影响理解和使用的关键问题，再优化表达和细节。

**Q: 不同长度文档的优化时间如何估算？**

A: 短文档（1-5页）约30-60分钟，中等文档（5-20页）约1-3小时，长文档（20页以上）约3-8小时，具体时间视复杂度和优化深度调整。

**Q: 如何验证优化效果？**

A: 可通过读者反馈、A/B测试、可读性评分对比、任务完成率测试等方法验证。

**Q: 团队协作优化文档的最佳实践是什么？**

A: 明确分工、统一标准、使用协作工具、设置审核流程、收集反馈并持续改进。
