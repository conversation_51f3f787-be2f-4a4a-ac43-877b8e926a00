# 项目需求
你是一个AI应用开发专家，请仔细阅读研究文档，producer实现手册-LangChain版.md，看是否能够根据这个文档，构建一个能够自动根据输入的历史人物和信息，完整的制作一个精彩的历史短剧视频。如果不能，还有不足和需要改进的地方，请帮我补充完善，做到最好。


# 项目实施进度
你是一个AI应用开发专家，精通关于大模型的一切技术原理和应用开发技巧，请仔细阅读文件 /Volumes/mini_matrix/github/producer/doc/项目实施进度跟踪.md  然后仔细检查我项目的进展程度，是否有错误和不完善的情况，给我详细总结汇报，并更新我的文档。然后再帮助我查看我的产品有哪些问题，有哪些需要改进和优化的地方，帮我修改完善，做到最好。


# 关于虚拟环境
我已经通过uv venv --python $(which python3) 创建了虚拟环境，python 的版本为3.11.9，请根据这个环境检查我的pyproject.toml文件，看是否需要修改，并检查所有依赖的库，是否可以更新到比较新的版本

# 关于Producer操作手册
我希望你在后续的操作过程中，将一些重要的指令，常用的指令，还有后面可能需要我重复操作的指令，帮我记录到doc目录下的一个实施手册文档中，方便我今后查看，因为我对AI应用开发不是很熟悉，所以记录一个命令操作手册很有必要。



# 关于密钥配置的说明
- 我cp .env.template .env 创建了.env文件，发现里面有很多密钥，但我不知道都是干什么用的，请你给我整理一个详细的文档，把每个密钥的作用都详细说明清楚：“为什么使用，如何申请，收费如何，有没有便宜的免费的替代品“，除了这些，你觉得还有哪些需要说明，注意的问题，你都给我补充到文档里，方便我学习查看，使用

## 再次检查密钥配置文档
- 你再检查检查，上网搜搜最新资料，详细检查一下/Volumes/mini_matrix/github/producer/doc/API密钥配置详细指南.md 这个文档的内容，看看有没有错误和疏漏，或者需要补充的内容，如果有，帮我修订到文档中，务必请认真仔细，因为这设计到要花钱的事情。

## 再次检查密钥配置文档
- 你再检查检查，上网搜搜最新资料，详细检查一下/Volumes/mini_matrix/github/producer/doc/API密钥配置详细指南.md 这个文档的内容，看看有没有错误和疏漏，或者需要补充的内容，如果有，帮我修订到文档中，务必请认真仔细，因为这设计到要花钱的事情。 还有文中没有提到jimeng这个AI图像和视频生成工具，它跟keling一样，支持文生图和图生视频，请评价他们的功能和价格，帮我补充到文档中。

# 流程问题查找
- 你去output目录下，查看json文件，仔细检查流程中哪里出错了


# 剧本生成功能
- 我想要在生成视频前，先生成剧本，剧本生成后，再生成视频， 这样我就可以分段测试了，先生成剧本，我检查剧本生成的质量，如果过关，再生成视频，避免浪费视频的生成时间和资源。
  并且以后我可能会扩张剧本生成的功能，比如生成多个剧本，或者生成更复杂的剧本，所以请在设计时，考虑这些可能性。或者我将来考虑用文本生成功能来写小说。
  我希望程序能够灵活方便的支持我的功能


## 剧本生成文档
- 太好了，帮我整理一个剧本生成的技术文档使用手册吧。
- 我希望您增强文档的可理解性，添加详细的原理解释和技术背景说明。
  不但每个关键命令都添加：
  执行原理：命令背后的技术机制
  参数说明：每个参数的作用和影响
  流程分解：复杂操作的分步骤解析
  最佳实践：为什么这样配置的技术原因

  还添加更多关键配置的原理解释,
  这样的文档结构让我可以不仅知道怎么做，更重要的是理解为什么这么做，有助于：

  深度理解：掌握工作机制
  故障排除：遇到问题时能够分析根因
  配置调优：根据实际需求优化参数
  知识迁移：理解原理后可以应用到其他场景

  文档更适合初学者学习和理解，每个配置都有清晰的技术背景说明。


# 提交代码

- 项目中有很多修改的代码，请帮我整理清晰完整的commit message，然后提交代码

# 文档优化
- 请按照 《文档优化通用提示词v2.md》的内容，优化doc目录下的所有文档
- 请按照 《文档优化通用提示词v2.md》的内容，优化Producer命令手册.md