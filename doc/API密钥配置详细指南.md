# Producer系统API密钥配置详细指南

## 📋 概述

Producer系统使用多种AI服务来生成文本、图像、视频和语音内容。本文档详细说明每个API密钥的作用、申请方法、收费情况和替代方案。

---

## 📝 文本生成服务

### 1. OpenAI API Key (`OPENAI_API_KEY`)

**作用**：用于GPT系列模型进行剧本大纲、场景描述、对白生成
- 主要模型：GPT-4o、GPT-4o-mini（其他型号以官网为准）
- 用途：创意写作、剧本生成、对话创作

**如何申请**：
1. 访问 https://platform.openai.com
2. 注册账户并完成身份验证
3. 在API Keys页面创建新密钥
4. 绑定支付方式（需要信用卡）

**收费情况**（以官方定价为准）：
- GPT-4o、GPT-4o-mini等型号的最新价格请以官方价格页为准

**免费额度**：以官网当前政策为准，可能随时间调整

**替代方案**：
- **免费**：Ollama本地部署（Llama 3.1, Qwen2.5）
- **便宜**：Dashscope（阿里云通义千问）
- **开源**：Hugging Face Transformers本地部署

### 2. Dashscope API Key (`DASHSCOPE_API_KEY`)

**作用**：阿里云通义千问模型，中文内容生成效果优秀
- 主要模型：qwen-turbo, qwen-plus, qwen-max
- 优势：中文理解能力强，价格便宜

**如何申请**：
1. 访问 https://dashscope.aliyun.com
2. 注册阿里云账户
3. 开通DashScope服务
4. 创建API密钥

**收费情况**：
- qwen-turbo: ¥0.3/1K tokens
- qwen-plus: ¥4/1K tokens  
- qwen-max: ¥40/1K tokens

**免费额度**：新用户有100万tokens免费额度

**替代方案**：
- **免费**：通义千问网页版（限制较多）
- **开源**：Qwen2.5本地部署

### 3. Anthropic API Key (`ANTHROPIC_API_KEY`)

**作用**：Claude模型，擅长长文本处理和创意写作
- 主要模型：Claude-3.5-Sonnet, Claude-3-Haiku
- 优势：上下文长度大，创意能力强

**如何申请**：
1. 访问 https://console.anthropic.com
2. 注册账户并验证
3. 在API Keys页面创建密钥
4. 充值使用（预付费模式）

**收费情况**（以官方定价为准，详见官方链接）：
- Claude Sonnet 4：$3–$6/1M 输入（按上下文档位），$15–$22.5/1M 输出
- Claude Opus 4.1：$15/1M 输入，$75/1M 输出
- Claude Haiku 3.5：$0.80/1M 输入，$4/1M 输出
- Sonnet 4 已支持最高 1M tokens 上下文（公测），具体计费以官方页面为准

**免费额度**：以官网当前政策为准（通常无统一长期免费额度）

### 4. DeepSeek API Key (`DEEPSEEK_API_KEY`)

**作用**：DeepSeek深度求索的高性价比AI模型，代码和推理能力强
- 主要模型：DeepSeek-V3（最新）, DeepSeek-Reasoner, DeepSeek-Coder-V2
- 优势：极低价格，代码生成优秀，推理能力强，支持上下文缓存

**如何申请**：
1. 访问 https://platform.deepseek.com
2. 注册DeepSeek账户
3. 创建API密钥

**收费情况**（以官方定价为准，详见官方链接）：
- DeepSeek-V3（deepseek-chat）参考价：约 $0.27/1M 输入，$1.10/1M 输出
- DeepSeek-Reasoner（deepseek-reasoner）价格更高，且长推理输出全部计入输出费用
- 提供特定时段 Off-peak 折扣与上下文缓存优惠，详见官方说明
- ⚠️ **重要提醒**：价格与优惠会动态调整，务必以官网为准

**免费额度**：以官网当前活动为准（如有试用/促销额度，以平台公告为准）

**⚠️ 重要更新**：DeepSeek-V3已发布，性能大幅提升，价格保持极低

**替代方案**：
- **开源**：DeepSeek-V2本地部署
- **便宜**：目前已是最便宜的商业API之一

### 5. Kimi API Key (`KIMI_API_KEY`)

**作用**：月之暗面Kimi模型，长文本处理能力极强
- 主要模型：Kimi-K2, Kimi-K1
- 优势：200万字符上下文，中文理解优秀

**如何申请**：
1. 访问 https://platform.moonshot.cn
2. 注册月之暗面账户
3. 创建API密钥

**收费情况**（以官方定价为准，详见官方链接）：
- 最新价格与上下文分档以官网为准
- 支持超长上下文（以官网规格为准）

**免费额度**：新用户有一定免费tokens

### 6. 豆包API Key (`DOUBAO_API_KEY`)

**作用**：字节跳动豆包大模型，多模态能力强
- 主要模型：Doubao-pro-4k, Doubao-lite-4k
- 优势：价格便宜，中文能力强，多模态支持

**如何申请**：
1. 访问 [火山引擎豆包大模型](https://www.volcengine.com/product/doubao)
2. 注册并完成企业/个人认证
3. 在控制台开通豆包服务，创建API密钥

**收费情况**（以官方定价为准，详见官方链接）：
- 采用分档/模型区分计费，请以火山引擎价格页为准
- 具体上下文长度及费用以官网为准

**免费额度**：新用户有免费试用额度

### 7. GLM API Key (`GLM_API_KEY`)

**作用**：智谱AI的ChatGLM模型，中文能力强
- 主要模型：GLM-4, GLM-4-Air
- 优势：中文理解，价格实惠

**如何申请**：
1. 访问 https://open.bigmodel.cn
2. 注册智谱AI账户
3. 实名认证后创建API密钥

**收费情况**（以官方定价为准，详见官方链接）：
- GLM-4: ¥100/1M tokens
- GLM-4-Air: ¥1/1M tokens
- GLM-4-Flash: ¥0.1/1M tokens（示例）
- **提示**：请以最新官方价格为准

**免费额度**：新用户有一定免费tokens

---

## 图像生成服务

### 1. FLUX API Key (`FLUX_API_KEY`)

**作用**：FLUX.1模型图像生成，质量极高
- 用途：角色形象、场景背景、道具生成
- 优势：开源模型，可本地部署

**如何申请**：
1. 访问 Black Forest Labs 官方 API 服务（与代码一致，使用 bfl.ml 域名）
2. 注册并登录账号
3. 在控制台创建 API Key（用于 `FLUX_API_KEY`）

**收费情况**（以 Black Forest Labs 官方为准）：
- 模型：`flux-pro`、`flux-dev`、`flux-schnell`
- 计费按图片与分辨率等因素计价，价格会动态调整，请以官方价格页为准
- 本项目适配器默认直连 `https://api.bfl.ml/v1/...`，无需第三方平台

**本地部署**：
- 下载FLUX.1-schnell模型（免费商用）
- 使用ComfyUI或Diffusers运行
- 需要24GB+ VRAM的显卡

**替代方案**：
- **免费**：Stable Diffusion本地部署
- **便宜**：SDXL-Lightning（快速生成）

### 2. Stability API Key (`STABILITY_API_KEY`)

**作用**：Stable Diffusion官方API
- 主要模型：SDXL, SD3
- 用途：图像生成备用方案

**如何申请**：
1. 访问 https://platform.stability.ai
2. 注册账户
3. 创建API密钥

**收费情况**（以官方定价为准）：
- 官方常见参考：SDXL 约 $0.04/张，SD3 约 $0.065/张（价格可能调整）

**免费额度**：新用户有25个免费credits

---

## 视频生成服务

### 1. Kling API Key (`KLING_API_KEY`)

**作用**：快手可灵AI视频生成，中国领先的视频AI

- 用途：根据图片和文本生成短视频
- 优势：中文理解好，视频质量高

**如何申请**：

1. 访问 [Kling 官方网站](https://klingai.kuaishou.com/) 或 [Kling 应用站点](https://app.klingai.com/cn/)
2. 注册并登录快手/Kling 账户
3. 根据官方流程开通相应权限（如有对外 API，请以官网公告为准）
4. 在账号后台获取密钥或使用网页/客户端进行生成

**收费情况**（以官方定价为准，详见官方链接）：

- 采用会员/积分与订阅等计费形式，额度与价格会随时调整
- 不同分辨率、时长与质量选项会影响消耗与费用
- 请以官网当前页面为准，不要依据第三方未经验证的信息

**免费额度**：新用户通常有一定免费次数

**替代方案**：

- **开源**：SVD (Stable Video Diffusion) 本地部署
- **便宜**：RunwayML Gen-2

### 2. Runway API Key (`RUNWAY_API_KEY`)

{{ ... }}
**作用**：RunwayML视频生成服务
- 主要模型：Gen-2, Gen-3
- 用途：视频生成备用方案

**如何使用**：
1. 访问 [RunwayML官网](https://runwayml.com)
2. 登录后在网页/客户端内进行创作并下载成品
3. 订阅付费计划获取API访问

**收费情况**：
- 采用 Credits 计费模型，不同模型与质量档位对应的 Credits 消耗不同
- 总费用与订阅套餐、加速/并发与当期促销相关，详见 [Runway 定价](https://runwayml.com/pricing) 与 [API 定价说明](https://docs.dev.runwayml.com/guides/pricing/)

---

## 语音合成服务

---

## 即梦（Jimeng）平台（图像/视频）

### 即梦平台概述（非独立API Key）

**作用**：字节跳动旗下的一站式 AI 创作平台，支持文生图、图生图、文生视频、图生视频、智能画布等。
- 用途：图片生成、视频片段生成、风格保持/背景替换、局部重绘、扩图、抠图等
- 优势：中文语义理解强、上手门槛低、创作工具完备，适合快速出稿

**如何使用**：
1. 访问 [即梦官网](https://jimeng.jianying.com/) 并登录
2. 在网页端直接使用绘画/视频生成功能；企业场景可参考 [火山引擎·即梦产品页](https://www.volcengine.com/product/jimeng)
3. 截至目前，公开标准化 API 能力以火山引擎业务对接为主，个人用户多通过网页/客户端使用

**收费情况**（以官方为准）：
- 采用会员/积分/订阅等计费方式，不同分辨率、时长、质量档位与并发会影响消耗
- 价格会随活动和版本更新动态变动，请以即梦/火山引擎官网为准

**与 Kling 的对比（要点）**：
- 功能：二者均支持文生图与图生视频。Kling 在高质量视频生成与中文场景理解上口碑较强；即梦提供更完整的创作工具链与社区/剪映生态联动
- 价格：两者均为会员/积分制且经常调整，难以用固定单价对比。务必以各自官网当前价格页为准
- API：Kling 的对外 API 公告以官方网站为准；即梦 API 主要面向火山引擎企业接入，个人用户以网页/客户端为主

### 1. ElevenLabs API Key (`ELEVENLABS_API_KEY`)

**作用**：高质量语音合成，支持情感表达
- 用途：角色配音、旁白生成
- 优势：声音自然，情感丰富

**如何申请**：
1. 访问 https://elevenlabs.io
2. 注册账户
3. 在Profile页面获取API密钥

**收费情况**（以官方定价为准，详见官方链接）：
- 提供免费与多档订阅计划（每月包含一定字符额度）
- 实际价格、字符额度与语音类型（标准/神经/高保真）可能变动，请以 [官方价格页](https://elevenlabs.io/pricing) 为准
- **注意**：价格相对较高，可与本地方案（CosyVoice、Edge-TTS）对比后选择

**免费额度**：免费层通常包含少量字符额度（以官网为准）

**替代方案**：
- **免费**：CosyVoice本地部署（阿里开源）
- **免费**：Edge-TTS（微软免费服务）
- **便宜**：Azure Speech Services

### 2. Azure Speech Key (`AZURE_SPEECH_KEY`, `AZURE_SPEECH_REGION`)

**作用**：微软Azure语音服务
- 用途：语音合成备用方案
- 优势：稳定可靠，多语言支持

**如何申请**：
1. 访问 https://azure.microsoft.com
2. 注册Azure账户
3. 创建Speech Services资源
4. 获取密钥和区域信息

**收费情况**：
- 标准语音：$4/1M字符
- 神经语音：$16/1M字符

**免费额度**：每月500K字符免费

---

## 本地模型配置

### 1. CosyVoice模型路径 (`COSYVOICE_MODEL_PATH`)

**作用**：阿里开源的高质量中文语音合成模型
- 优势：完全免费，中文效果好
- 要求：需要下载模型文件（约2GB）

**如何配置**：
```bash
# 克隆CosyVoice仓库
git clone https://github.com/FunAudioLLM/CosyVoice.git ./models/cosyvoice
cd ./models/cosyvoice
# 按照官方文档安装依赖和下载模型
```

### 2. FLUX模型路径 (`FLUX_SCHNELL_MODEL_PATH`)

**作用**：FLUX.1-schnell开源图像生成模型
- 优势：免费商用，生成速度快
- 要求：需要下载模型文件（约12GB）

**如何配置**：
```bash
# 使用Hugging Face下载
huggingface-cli download black-forest-labs/FLUX.1-schnell --local-dir ./models/flux-schnell
```

### 3. SVD模型路径 (`SVD_MODEL_PATH`)

**作用**：Stable Video Diffusion开源视频生成模型
- 优势：完全免费，可本地运行
- 要求：需要强大显卡（24GB+ VRAM）

### 4. ComfyUI路径 (`COMFYUI_PATH`)

**作用**：图像和视频生成的用户界面工具
- 用途：本地运行FLUX、SVD等模型
- 优势：可视化操作，工作流管理

---

## AI模型性价比全面对比

### 重要价格更新提醒（价格随时变化）

**提示**：以下价格信息以撰写时的公开资料为参考，AI市场变化极快，**请以各厂商官方最新价格为准，并参考下方官方链接**。

**关键发现**：

- **Claude升级**：4.x 系列，支持更长上下文（如 Sonnet 4 支持至 1M tokens，公测，以官网为准）
- **价格动态**：部分厂商存在分时/分档/促销定价，切勿依据旧价做预算
- **分层定价趋势**：多数厂商采用基于上下文长度的分层定价

### 文本生成模型对比表

| 模型 | 价格(¥/1M tokens) | 中文能力 | 代码能力 | 创意写作 | 推理能力 | 免费额度 | 推荐指数 |
|------|------------------|----------|----------|----------|----------|----------|----------|
| **GLM-4-Flash** | 以官网为准 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 有 | ⭐⭐⭐⭐⭐ |
| **DeepSeek-V3** | 以官网为准 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 以官网为准 | ⭐⭐⭐⭐⭐ |
| **Claude-Haiku-3.5** | 以官网为准 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 以官网为准 | ⭐⭐⭐⭐⭐ |
| **Claude-Sonnet-4** | 以官网为准 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 以官网为准 | ⭐⭐⭐⭐ |
| **Claude-Opus-4.1** | 以官网为准 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 以官网为准 | ⭐⭐⭐ |
| **GLM-4-Air** | 以官网为准 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 有 | ⭐⭐⭐⭐ |
| **Doubao-lite** | 以官网为准 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 有 | ⭐⭐⭐ |

### 业内最佳工程实践

#### 1. **多模型路由策略（推荐）**
```bash
# 主力模型：DeepSeek-V3（性价比王者）
DEEPSEEK_API_KEY=your_deepseek_key

# 备用模型：豆包（超低价格）
DOUBAO_API_KEY=your_doubao_key

# 长文本专用：Kimi（200万字符上下文）
KIMI_API_KEY=your_kimi_key

# 高质量兜底：GPT-4o-mini
OPENAI_API_KEY=your_openai_key
```

#### 2. **智能成本控制**
- **简单任务**：使用豆包lite（¥300/1M）
- **复杂推理**：使用DeepSeek-V3（¥1-2/1M）
- **长文本处理**：使用Kimi-K1（200万字符上下文）
- **创意写作**：使用Claude-3-Haiku或Kimi

## 2025年8月最优成本配置方案

### **2025年8月极致性价比方案**（月成本：¥10-30）
```bash
# 文本生成：GLM + DeepSeek组合（2025年最新推荐）
GLM_API_KEY=your_glm_key               # 主力模型（GLM-4-Flash，¥0.1/1M）
DEEPSEEK_API_KEY=your_deepseek_key     # 复杂任务（¥1.9-7.7/1M）

# 图像：本地+云端混合
FLUX_SCHNELL_MODEL_PATH=./models/flux-schnell
STABILITY_API_KEY=your_stability_key

# 视频：Kling主力
KLING_API_KEY=your_kling_key

# 语音：混合方案
COSYVOICE_MODEL_PATH=./models/cosyvoice
ELEVENLABS_API_KEY=your_elevenlabs_key  # 高质量场景
```

---

## ⚠️ 重要注意事项

### 🔒 安全注意事项
1. **密钥保护**：
   - 绝不要将`.env`文件提交到Git仓库
   - 不要在代码中硬编码API密钥
   - 定期轮换API密钥

2. **权限控制**：
   - 为API密钥设置最小必要权限
   - 监控API使用情况，及时发现异常

### 💸 成本控制

1. **预算设置**：
   ```bash
   DAILY_BUDGET_USD=2.0      # 每日预算限制
   MONTHLY_BUDGET_USD=50.0   # 月度预算限制
   COST_TRACKING_ENABLED=true # 启用成本跟踪
   ```

2. **使用建议**：

   - 开发测试时使用免费/便宜的服务
   - 生产环境根据质量需求选择
   - 设置合理的预算限制避免超支

### 🚀 性能优化

1. **并发控制**：
   ```bash
   MAX_CONCURRENT_TASKS=3  # 限制并发任务数
   BATCH_SIZE=5           # 批处理大小
   ```

2. **缓存策略**：

   ```bash
   CACHE_ENABLED=true     # 启用结果缓存
   ```

### 🌐 网络和地区限制
1. **地区限制**：
   - OpenAI：部分地区不可用，可能需要代理
   - Kling：主要面向中国市场
   - ElevenLabs：全球可用

2. **网络要求**：
   - 稳定的互联网连接
   - 某些服务可能需要代理访问

---

## 🛠️ 配置验证命令

### 检查API密钥有效性
```bash
# 检查系统状态（包括API连接）
uv run python -m producer.cli status

# 运行连接测试
uv run python -m producer.cli test --quick
```

### 验证本地模型
```bash
# 检查本地模型路径
ls -la ./models/

# 测试本地模型加载
uv run python -c "from producer.adapters.voice.cosyvoice_adapter import CosyVoiceAdapter; print('CosyVoice OK')"
```

---

## ⚠️ **关键注意事项和错误修正**

> 本节用于汇总重要改动与防踩坑提示：
> - 删除了过时或不确定的免费额度与价格示例，统一以官网为准。
> - 修复了一个破损的代码块与若干Markdown格式问题。
> - 统一环境变量命名与 `.env.template` 一致。

### 🥈 **小团队方案**（月成本：¥80-200）

**适用场景**：

- 3-10人的创业团队
- 需要稳定可靠的AI服务
- 对成本和质量都有要求

```bash
# 多模型路由策略
DEEPSEEK_API_KEY=your_deepseek_key     # 主力模型
KIMI_API_KEY=your_kimi_key             # 长文本处理
GLM_API_KEY=your_glm_key               # 中文优化
OPENAI_API_KEY=your_openai_key         # 高质量兜底

# 图像：本地+云端混合
FLUX_SCHNELL_MODEL_PATH=./models/flux-schnell
STABILITY_API_KEY=your_stability_key

# 视频：Kling主力
KLING_API_KEY=your_kling_key

# 语音：混合方案
COSYVOICE_MODEL_PATH=./models/cosyvoice
ELEVENLABS_API_KEY=your_elevenlabs_key  # 高质量场景
```

#### 🏢 **企业级方案**（月成本：¥300-800）

**适用场景**：
- 大中型企业级应用
- 对可用性和性能有严格要求
- 需要多重备份和专业支持
```bash
# 全栈高可用配置
OPENAI_API_KEY=your_openai_key         # GPT-4o
ANTHROPIC_API_KEY=your_anthropic_key   # Claude-3.5-Sonnet
DEEPSEEK_API_KEY=your_deepseek_key     # 代码生成
KIMI_API_KEY=your_kimi_key             # 长文本
DOUBAO_API_KEY=your_doubao_key         # 成本优化

# 多重备份策略
KLING_API_KEY=your_kling_key
RUNWAY_API_KEY=your_runway_key
ELEVENLABS_API_KEY=your_elevenlabs_key
AZURE_SPEECH_KEY=your_azure_key
```

---

## 🔧 故障排除

### 常见问题

1. **API密钥无效**：
   - 检查密钥格式是否正确
   - 确认账户余额充足
   - 验证API权限设置

2. **本地模型加载失败**：
   - 检查模型文件完整性
   - 确认路径配置正确
   - 验证系统资源（内存、显存）

3. **网络连接问题**：
   - 检查网络连接
   - 配置代理设置（如需要）
   - 尝试不同的API端点

### 调试命令
```bash
# 查看详细日志
LOG_LEVEL=DEBUG uv run python -m producer.cli test

# 测试特定适配器
uv run python -m producer.cli test --adapter text
uv run python -m producer.cli test --adapter image
```

---

## 📚 学习资源

### 官方文档
- [OpenAI API文档](https://platform.openai.com/docs)
- [Dashscope文档](https://help.aliyun.com/zh/dashscope/)
- [Anthropic API文档](https://docs.anthropic.com/)
- [ElevenLabs文档](https://elevenlabs.io/docs)

### 开源项目
- [FLUX.1](https://github.com/black-forest-labs/flux)
- [CosyVoice](https://github.com/FunAudioLLM/CosyVoice)
- [Stable Video Diffusion](https://github.com/Stability-AI/generative-models)
- [ComfyUI](https://github.com/comfyanonymous/ComfyUI)

---

## 🎯 快速开始指南

### 📋 **第一步：选择适合的方案**

| 用户类型 | 推荐方案 | 月成本 | 主要特点 |
|----------|----------|--------|----------|
| 个人学习 | 极致性价比 | ¥15-40 | DeepSeek+豆包+本地模型 |
| 小团队 | 平衡质量 | ¥80-200 | 多模型路由+备份 |
| 企业用户 | 企业级 | ¥300-800 | 全栈高可用+多重备份 |

### 🚀 **第二步：快速配置**

1. **复制环境文件**：
   ```bash
   cp .env.template .env
   ```

2. **推荐配置（个人开发者）**：
   ```bash
   # 编辑.env文件，填入以下密钥（2025年8月最新推荐）
   GLM_API_KEY=your_glm_key              # 必需，极致性价比（¥0.1/1M）
   DEEPSEEK_API_KEY=your_deepseek_key    # 必需，综合性能强（¥1.9-7.7/1M）
   KLING_API_KEY=your_kling_key          # 必需，视频生成
   
   # 本地模型路径（推荐）
   FLUX_SCHNELL_MODEL_PATH=./models/flux-schnell
   COSYVOICE_MODEL_PATH=./models/cosyvoice
   ```

3. **验证配置**：
   ```bash
   uv run python -m producer.cli status
   ```

4. **开始制作**：
   ```bash
   uv run python -m producer.cli produce -t "测试剧本" -e "古代"
   ```

### 💡 **专家建议**

1. **优先级排序**（2025年8月更新）：
   - 🥇 **GLM-4-Flash**：必备，价格无敌（¥0.1/1M）
   - 🥈 **DeepSeek-V3**：核心，性能强劲（¥1.9-7.7/1M）
   - 🥉 **Claude-Haiku-3.5**：创意，写作专家（¥5.6-28/1M）
   - 🏅 **本地模型**：长期成本最优

2. **渐进式升级**（2025年8月路线图）：
   - 第1周：GLM-4-Flash + 本地模型（成本最低）
   - 第2周：添加DeepSeek-V3（提升性能）
   - 第3周：添加Claude-Haiku-3.5（增强创意）
   - 第4周：智能模型路由 + 成本优化

3. **成本控制技巧**（2025年8月优化策略）：
   - 开发阶段：GLM-4-Flash + 本地模型（¥0.1/1M）
   - 测试阶段：DeepSeek-V3（¥1.9-7.7/1M）
   - 生产阶段：智能路由（简单任务用GLM，复杂任务用Claude）
   - 创意阶段：Claude-Haiku-3.5（创意写作专用）

---

**🎉 恭喜！现在你拥有了2025年8月最新的AI模型配置方案！**

### 📈 **2025年8月重大价格变化总结**

- **GLM-4-Flash**：¥0.1/1M，成为新的性价比王者
- **Claude模型全面升级**：Sonnet-4、Opus-4.1、Haiku-3.5
- **GPT-5即将发布**：预计定价¥15-30/1M
- **DeepSeek价格稳定**：维持¥1.9-7.7/1M的高性价比
- **豆包相对劣势**：¥300/1M在新环境下不再具备优势
