#!/usr/bin/env python3
"""
历史短剧视频制作系统 - 命令行接口
提供简单易用的CLI工具来操作整个视频制作流程
"""

import asyncio
import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich import print as rprint

# 使用绝对导入，确保模块能正确加载
import sys
from pathlib import Path

# 只在必要时添加路径
if __name__ == '__main__' or 'producer.cli' in sys.modules:
    project_root = Path(__file__).parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from core.config import ConfigManager
from core.cost_control import CostController
from core.workflow import WorkflowEngine
from core.models import ScriptData

# 添加结果查看器导入
from producer.results_viewer import start_viewer

console = Console()

@click.group()
@click.version_option(version="1.0.0", prog_name="Producer")
def cli():
    """🎬 历史短剧视频制作系统 - 基于LangChain的混合优化方案"""
    pass

@cli.command()
@click.option('--title', '-t', required=True, help='剧本标题')
@click.option('--theme', default='历史剧情', help='剧本主题')
@click.option('--era', '-e', default='明朝', help='历史时代')
@click.option('--duration', '-d', default=180, help='视频时长（秒）')
@click.option('--output', '-o', default='./output', help='输出目录')
@click.option('--config', '-c', default='./config/config.yaml', help='配置文件路径')
@click.option('--budget', '-b', default=1.0, help='预算限制（美元）')
def produce(title: str, theme: str, era: str, duration: int, output: str, config: str, budget: float):
    """🎭 制作一个完整的历史短剧视频"""
    
    async def _produce():
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # 初始化系统
            task = progress.add_task("初始化系统...", total=None)
            try:
                config_manager = ConfigManager(config_path=config)
                cost_controller = CostController(config_manager)
                workflow = WorkflowEngine(config_manager, cost_controller)
                progress.update(task, description="✅ 系统初始化完成")
            except Exception as e:
                progress.update(task, description=f"❌ 系统初始化失败: {e}")
                return
            
            # 检查预算状态
            progress.update(task, description="检查预算状态...")
            budget_status = cost_controller.get_budget_status()
            if budget > budget_status.daily_remaining:
                progress.update(task, description=f"⚠️ 预算可能不足，剩余: ${budget_status.daily_remaining:.2f}")
            else:
                progress.update(task, description=f"✅ 预算充足，剩余: ${budget_status.daily_remaining:.2f}")
            
            # 创建剧本数据
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            script_data = ScriptData(
                script_id=f"script_{title.replace(' ', '_')}_{timestamp}",
                title=title,
                theme=theme,
                era=era,
                summary=f"一个关于{era}{theme}的历史短剧",
                characters=[],
                scenes=[],
                dialogues=[],
                media_cues=[],
                total_duration=duration
            )
            
            progress.update(task, description="开始执行工作流程...")
            
            # 执行工作流
            try:
                result = await workflow.execute_workflow(script_data)
                
                if result.status == 'completed':
                    progress.update(task, description="✅ 视频制作完成!")
                    
                    # 显示结果摘要
                    table = Table(title="制作结果摘要")
                    table.add_column("项目", style="cyan")
                    table.add_column("值", style="green")
                    
                    table.add_row("执行ID", result.execution_id)
                    table.add_row("总成本", f"${result.total_cost:.4f}")
                    table.add_row("完成步骤", str(len(result.completed_steps)))
                    table.add_row("失败步骤", str(len(result.failed_steps)))
                    
                    console.print(table)
                    
                    if result.completed_steps:
                        rprint("\n[green]✅ 完成的步骤:[/green]")
                        for step in result.completed_steps:
                            rprint(f"  • {step}")
                    
                else:
                    progress.update(task, description="❌ 视频制作失败")
                    rprint(f"[red]错误: {result.error_message}[/red]")
                    if result.failed_steps:
                        rprint(f"[red]失败步骤: {', '.join(result.failed_steps)}[/red]")
                        
            except Exception as e:
                progress.update(task, description=f"❌ 执行失败: {e}")
                rprint(f"[red]执行过程中发生错误: {e}[/red]")
    
    # 运行异步任务
    asyncio.run(_produce())

@cli.command()
@click.option('--config', '-c', default='./config/config.yaml', help='配置文件路径')
def test(config: str):
    """🧪 运行系统测试"""
    
    async def _test():
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            task = progress.add_task("运行测试...", total=None)
            
            try:
                # 导入测试模块
                from tests.test_workflow import test_individual_chains, test_basic_workflow
                
                # 测试各个链路
                progress.update(task, description="测试各个链路...")
                chain_result = await test_individual_chains()
                
                # 测试完整工作流
                progress.update(task, description="测试完整工作流...")
                workflow_result = await test_basic_workflow()
                
                # 显示结果
                table = Table(title="测试结果")
                table.add_column("测试项", style="cyan")
                table.add_column("结果", style="green")
                
                table.add_row("链路测试", "✅ 通过" if chain_result else "❌ 失败")
                table.add_row("工作流测试", "✅ 通过" if workflow_result and workflow_result.get('status') == 'completed' else "❌ 失败")
                
                console.print(table)
                progress.update(task, description="✅ 测试完成")
                
            except Exception as e:
                progress.update(task, description=f"❌ 测试失败: {e}")
                rprint(f"[red]测试过程中发生错误: {e}[/red]")
    
    asyncio.run(_test())

@cli.command()
@click.option('--config', '-c', default='./config/config.yaml', help='配置文件路径')
def status(config: str):
    """📊 显示系统状态和统计信息"""
    
    try:
        config_manager = ConfigManager(config_path=config)
        cost_controller = CostController(config_manager)
        workflow = WorkflowEngine(config_manager, cost_controller)
        
        # 获取统计信息
        stats = workflow.get_workflow_statistics()
        
        # 显示系统状态
        panel = Panel.fit(
            f"""
[bold cyan]系统状态[/bold cyan]

[green]✅ 系统运行正常[/green]
[blue]📁 配置文件: {config}[/blue]
[yellow]💰 预算控制: 启用[/yellow]
            """,
            title="Producer 状态"
        )
        console.print(panel)
        
        # 显示统计信息
        table = Table(title="工作流统计")
        table.add_column("指标", style="cyan")
        table.add_column("值", style="green")
        
        table.add_row("总执行次数", str(stats['total_executions']))
        table.add_row("成功次数", str(stats['completed']))
        table.add_row("失败次数", str(stats['failed']))
        table.add_row("运行中", str(stats['running']))
        table.add_row("成功率", f"{stats['success_rate']:.1%}")
        table.add_row("总成本", f"${stats['total_cost_usd']:.4f}")
        table.add_row("平均成本", f"${stats['average_cost_usd']:.4f}")
        table.add_row("平均时长", f"{stats['average_duration_minutes']:.1f}分钟")
        
        console.print(table)
        
    except Exception as e:
        rprint(f"[red]获取状态失败: {e}[/red]")

@cli.command()
@click.option('--host', default='localhost', help='服务器主机地址')
@click.option('--port', default=5000, type=int, help='服务器端口')
@click.option('--debug', is_flag=True, help='启用调试模式')
def results(host, port, debug):
    """启动测试结果查看器"""
    console.print(f"启动测试结果查看器: http://{host}:{port}")
    start_viewer(host, port, debug)


@cli.command()
def version():
    """📋 显示版本信息"""
    
    panel = Panel.fit(
        """
[bold cyan]Producer - 历史短剧视频制作系统[/bold cyan]

[green]版本: 1.0.0[/green]
[blue]架构: 混合优化方案（本地+云端）[/blue]
[yellow]技术栈: LangChain + LiteLLM[/yellow]

[dim]支持的服务:[/dim]
• 文本生成: GPT-4o-mini, Claude-3-Sonnet, 通义千问
• 图像生成: FLUX.1, SDXL-Lightning
• 视频生成: 可灵(Kling), SVD+ComfyUI
• 语音合成: CosyVoice, ElevenLabs
        """,
        title="版本信息"
    )
    console.print(panel)

@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', default='./output', help='输出目录')
@click.option('--config', '-c', default='./config/config.yaml', help='配置文件路径')
def batch(input_file: str, output: str, config: str):
    """📦 批量处理多个剧本（从JSON文件）"""
    
    import json
    
    async def _batch():
        try:
            # 读取批量配置文件
            with open(input_file, 'r', encoding='utf-8') as f:
                batch_config = json.load(f)
            
            scripts = batch_config.get('scripts', [])
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                
                task = progress.add_task(f"批量处理 {len(scripts)} 个剧本...", total=len(scripts))
                
                config_manager = ConfigManager(config_path=config)
                cost_controller = CostController(config_manager)
                workflow = WorkflowEngine(config_manager, cost_controller)
                
                results = []
                
                for i, script_config in enumerate(scripts):
                    progress.update(task, description=f"处理剧本 {i+1}/{len(scripts)}: {script_config.get('title', 'Unknown')}")
                    
                    # 创建剧本数据
                    script_data = ScriptData(
                        script_id=f"batch_{i}_{script_config.get('title', 'unknown').replace(' ', '_')}",
                        title=script_config.get('title', '未命名剧本'),
                        theme=script_config.get('theme', '历史剧情'),
                        era=script_config.get('era', '明朝'),
                        summary=script_config.get('summary', ''),
                        characters=[],
                        scenes=[],
                        dialogues=[],
                        media_cues=[],
                        total_duration=script_config.get('duration', 180)
                    )
                    
                    try:
                        result = await workflow.execute_workflow(script_data)
                        results.append({
                            'title': script_config.get('title'),
                            'status': result.status,
                            'cost': result.total_cost
                        })
                    except Exception as e:
                        results.append({
                            'title': script_config.get('title'),
                            'status': 'error',
                            'error': str(e)
                        })
                    
                    progress.update(task, advance=1)
                
                # 显示批量处理结果
                table = Table(title="批量处理结果")
                table.add_column("剧本", style="cyan")
                table.add_column("状态", style="green")
                table.add_column("成本", style="yellow")
                
                total_cost = 0
                success_count = 0
                
                for result in results:
                    status_icon = "✅" if result['status'] == 'completed' else "❌"
                    cost_str = f"${result.get('cost', 0):.4f}" if 'cost' in result else "N/A"
                    
                    table.add_row(
                        result['title'],
                        f"{status_icon} {result['status']}",
                        cost_str
                    )
                    
                    if result['status'] == 'completed':
                        success_count += 1
                        total_cost += result.get('cost', 0)
                
                console.print(table)
                
                # 显示摘要
                rprint("\n[green]批量处理完成![/green]")
                rprint(f"成功: {success_count}/{len(scripts)}")
                rprint(f"总成本: ${total_cost:.4f}")
                
        except Exception as e:
            rprint(f"[red]批量处理失败: {e}[/red]")
    
    asyncio.run(_batch())

if __name__ == '__main__':
    cli()
