#!/usr/bin/env python3
"""
统一查看器
整合测试结果查看器和剧本查看器功能，提供统一的Web界面
"""

import json
import http.server
import socketserver
import urllib.parse
from pathlib import Path
from typing import Dict, List, Any
import threading
import webbrowser
import sys

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入现有的管理器
from producer.results_viewer import ResultsManager, TestResult
from producer.script_viewer import ScriptViewerManager, ScriptProjectInfo


class UnifiedViewerHandler(http.server.SimpleHTTPRequestHandler):
    """统一查看器HTTP处理器"""
    
    def __init__(self, *args, **kwargs):
        self.results_manager = ResultsManager()
        self.script_manager = ScriptViewerManager()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        query = urllib.parse.parse_qs(parsed_path.query)
        
        # 主页 - 显示导航界面
        if path == '/' or path == '/home':
            self._serve_home_page()
        
        # 测试结果相关路由
        elif path == '/results' or path.startswith('/results/'):
            self._handle_results_routes(path)
        
        # 剧本查看相关路由
        elif path == '/scripts' or path.startswith('/scripts/'):
            self._handle_scripts_routes(path)
        
        # 项目详情页面路由（兼容性）
        elif path.startswith('/project/'):
            project_id = path.split('/')[-1]
            self._serve_project_page(project_id)
        
        # API路由
        elif path.startswith('/api/'):
            self._handle_api_routes(path)
        
        # 静态资源
        elif path.startswith('/static/'):
            self._serve_static(path)
        
        # 忽略开发工具请求（如 Vite 客户端）
        elif path.startswith('/@vite/') or path.startswith('/node_modules/'):
            self.send_response(204)  # No Content
            self.end_headers()
        
        else:
            self._serve_404()
    
    def _serve_home_page(self):
        """提供主页导航界面"""
        # 获取统计信息
        results_stats = self.results_manager.get_statistics()
        scripts_stats = self.script_manager.get_statistics()
        
        html = self._build_home_html(results_stats, scripts_stats)
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def _handle_results_routes(self, path):
        """处理测试结果相关路由"""
        if path == '/results':
            self._serve_results_page()
        else:
            self._serve_404()
    
    def _handle_scripts_routes(self, path):
        """处理剧本查看相关路由"""
        if path == '/scripts':
            self._serve_scripts_page()
        elif path.startswith('/scripts/project/'):
            project_id = path.split('/')[-1]
            self._serve_project_page(project_id)
        else:
            self._serve_404()
    
    def _handle_api_routes(self, path):
        """处理API路由"""
        if path.startswith('/api/results'):
            self._handle_results_api(path)
        elif path.startswith('/api/scripts'):
            self._handle_scripts_api(path)
        else:
            self._serve_404()
    
    def _handle_results_api(self, path):
        """处理测试结果API"""
        if path == '/api/results':
            results = self.results_manager.get_results()
            data = [{
                'filename': r.filename,
                'timestamp': r.timestamp.isoformat(),
                'is_summary': r.is_summary,
                'is_detailed': r.is_detailed
            } for r in results]
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
        
        elif path.startswith('/api/results/'):
            filename = path.split('/')[-1]
            result = self.results_manager.get_result_by_filename(filename)
            if result:
                self.send_response(200)
                self.send_header('Content-type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(result.data, ensure_ascii=False, indent=2).encode('utf-8'))
            else:
                self.send_response(404)
                self.end_headers()
        
        elif path == '/api/results/statistics':
            stats = self.results_manager.get_statistics()
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(stats, ensure_ascii=False).encode('utf-8'))
    
    def _handle_scripts_api(self, path):
        """处理剧本查看API"""
        if path == '/api/scripts':
            projects = self.script_manager.get_projects()
            data = [p.to_dict() for p in projects]
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))
        
        elif path.startswith('/api/scripts/project/'):
            project_id = path.split('/')[-1]
            project = self.script_manager.get_project_by_id(project_id)
            
            if not project:
                self.send_response(404)
                self.send_header('Content-type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "项目不存在"}, ensure_ascii=False).encode('utf-8'))
                return
            
            data = project.to_dict()
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))
        
        elif path == '/api/scripts/statistics':
            stats = self.script_manager.get_statistics()
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(stats, ensure_ascii=False).encode('utf-8'))
    
    def _serve_results_page(self):
        """提供测试结果页面"""
        stats = self.results_manager.get_statistics()
        results = self.results_manager.get_results()
        
        html = self._build_results_html(stats, results)
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def _serve_scripts_page(self):
        """提供剧本列表页面"""
        stats = self.script_manager.get_statistics()
        projects = self.script_manager.get_projects()
        
        html = self._build_scripts_html(stats, projects)
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def _serve_project_page(self, project_id: str):
        """提供单个项目详情页面"""
        project = self.script_manager.get_project_by_id(project_id)
        
        if not project:
            self._serve_404()
            return
        
        html = self._build_project_html(project)
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def _serve_static(self, path):
        """提供静态资源"""
        # 简单的静态文件服务
        self._serve_404()
    
    def _serve_404(self):
        """404页面"""
        html = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - Producer查看器</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; text-align: center; padding: 50px; }
        .error-container { max-width: 500px; margin: 0 auto; }
        .error-code { font-size: 6em; color: #007bff; margin: 0; }
        .error-message { font-size: 1.5em; color: #666; margin: 20px 0; }
        .back-link { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .back-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="error-container">
        <h1 class="error-code">404</h1>
        <p class="error-message">页面未找到</p>
        <a href="/" class="back-link">返回首页</a>
    </div>
</body>
</html>
        '''
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def _build_home_html(self, results_stats: Dict[str, Any], scripts_stats: Dict[str, Any]) -> str:
        """构建主页HTML"""
        return f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Producer查看器</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; color: #333; line-height: 1.6; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
        .header {{ background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; text-align: center; }}
        .header h1 {{ font-size: 2.5em; color: #007bff; margin-bottom: 10px; }}
        .header p {{ color: #666; font-size: 1.1em; }}
        .nav-cards {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .nav-card {{ background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s; }}
        .nav-card:hover {{ transform: translateY(-5px); box-shadow: 0 8px 15px rgba(0,0,0,0.15); }}
        .nav-card-header {{ padding: 20px; background: linear-gradient(135deg, #007bff, #0056b3); color: white; }}
        .nav-card-header h2 {{ font-size: 1.5em; margin-bottom: 5px; }}
        .nav-card-header p {{ opacity: 0.9; }}
        .nav-card-body {{ padding: 20px; }}
        .nav-card-stats {{ display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px; }}
        .stat-item {{ text-align: center; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .stat-label {{ color: #666; font-size: 0.9em; }}
        .nav-button {{ display: block; width: 100%; padding: 12px; background: #007bff; color: white; text-decoration: none; text-align: center; border-radius: 6px; font-weight: 500; transition: background 0.2s; }}
        .nav-button:hover {{ background: #0056b3; }}
        .scripts-card .nav-card-header {{ background: linear-gradient(135deg, #28a745, #1e7e34); }}
        .scripts-card .stat-number {{ color: #28a745; }}
        .scripts-card .nav-button {{ background: #28a745; }}
        .scripts-card .nav-button:hover {{ background: #1e7e34; }}
        .footer {{ text-align: center; color: #666; margin-top: 30px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Producer查看器</h1>
            <p>统一的测试结果和剧本查看平台</p>
        </div>
        
        <div class="nav-cards">
            <div class="nav-card">
                <div class="nav-card-header">
                    <h2>🧪 测试结果</h2>
                    <p>查看所有测试结果和统计信息</p>
                </div>
                <div class="nav-card-body">
                    <div class="nav-card-stats">
                        <div class="stat-item">
                            <div class="stat-number">{results_stats.get('total_tests', 0)}</div>
                            <div class="stat-label">总测试次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{results_stats.get('success_rate', 0):.1f}%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                    </div>
                    <a href="/results" class="nav-button">查看测试结果</a>
                </div>
            </div>
            
            <div class="nav-card scripts-card">
                <div class="nav-card-header">
                    <h2>📝 剧本管理</h2>
                    <p>查看和管理所有剧本项目</p>
                </div>
                <div class="nav-card-body">
                    <div class="nav-card-stats">
                        <div class="stat-item">
                            <div class="stat-number">{scripts_stats.get('total_projects', 0)}</div>
                            <div class="stat-label">总项目数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{scripts_stats.get('completed_projects', 0)}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>
                    <a href="/scripts" class="nav-button">查看剧本项目</a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Producer统一查看器 - 让数据查看更简单</p>
        </div>
    </div>
    
    <script>
        // 自动刷新统计信息
        setInterval(() => {{
            location.reload();
        }}, 60000);
    </script>
</body>
</html>
        '''
    
    def _build_results_html(self, stats: Dict[str, Any], results: List[TestResult]) -> str:
        """构建测试结果页面HTML"""
        # 使用现有的结果查看器模板
        try:
            template_path = Path(__file__).parent.parent / "templates" / "results_viewer.html"
            if template_path.exists():
                from jinja2 import Template
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                
                template = Template(template_content)
                return template.render(stats=stats, results=results, nav_home_url="/")
        except Exception as e:
            print(f"模板渲染失败: {e}")
        
        # 备用简单HTML
        result_items = []
        for result in results:
            type_class = "type-summary" if result.is_summary else "type-detailed"
            type_text = "摘要" if result.is_summary else "详细"
            
            item = f'''
            <div class="result-item" onclick="showDetails('{result.filename}')">
                <div class="result-header">
                    <span class="result-filename">{result.filename}</span>
                    <span class="result-date">{result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</span>
                </div>
                <div>
                    <span class="result-type {type_class}">{type_text}</span>
                    <small>点击查看详情</small>
                </div>
            </div>
            '''
            result_items.append(item)
        
        result_items_html = ''.join(result_items)
        
        return f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试结果 - Producer查看器</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; color: #333; line-height: 1.6; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
        .header {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }}
        .nav-link {{ display: inline-block; margin-bottom: 10px; color: #007bff; text-decoration: none; }}
        .nav-link:hover {{ text-decoration: underline; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }}
        .stat-card {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .results-list {{ background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; }}
        .result-item {{ padding: 15px; border-bottom: 1px solid #eee; cursor: pointer; transition: background-color 0.2s; }}
        .result-item:hover {{ background: #f8f9fa; }}
        .result-item:last-child {{ border-bottom: none; }}
        .result-header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px; }}
        .result-filename {{ font-weight: bold; color: #007bff; }}
        .result-date {{ color: #666; font-size: 0.9em; }}
        .result-type {{ display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-right: 5px; }}
        .type-summary {{ background: #d4edda; color: #155724; }}
        .type-detailed {{ background: #d1ecf1; color: #0c5460; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="nav-link">← 返回首页</a>
            <h1>🧪 测试结果查看器</h1>
            <p>自动扫描并展示所有测试结果</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{stats.get('total_tests', 0)}</div>
                <div>总测试次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats.get('success_rate', 0):.1f}%</div>
                <div>成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats.get('summary_files', 0)}</div>
                <div>摘要文件</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats.get('detailed_files', 0)}</div>
                <div>详细文件</div>
            </div>
        </div>
        
        <div class="results-list">
            {result_items_html}
        </div>
    </div>
    
    <script>
        function showDetails(filename) {{
            fetch(`/api/results/${{filename}}`)
                .then(response => response.json())
                .then(data => {{
                    alert('详细信息:\n' + JSON.stringify(data, null, 2));
                }})
                .catch(error => {{
                    alert('加载失败: ' + error);
                }});
        }}
        
        setInterval(() => {{
            location.reload();
        }}, 30000);
    </script>
</body>
</html>
        '''
    
    def _build_scripts_html(self, stats: Dict[str, Any], projects: List[ScriptProjectInfo]) -> str:
        """构建剧本列表页面HTML"""
        # 使用现有的剧本查看器模板
        try:
            template_path = Path(__file__).parent.parent / "templates" / "script_viewer.html"
            if template_path.exists():
                from jinja2 import Template
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                
                template = Template(template_content)
                template_data = {
                    'stats': stats,
                    'projects': [p.to_dict() for p in projects],
                    'nav_home_url': "/"
                }
                return template.render(**template_data)
        except Exception as e:
            print(f"模板渲染失败: {e}")
        
        # 备用简单HTML
        project_items = []
        for project in projects:
            item = f'''
            <div class="project-item">
                <div class="project-header">
                    <h3>{project.title}</h3>
                    <span class="project-status">{project.status_display}</span>
                </div>
                <div class="project-meta">
                    <span>主题: {project.theme}</span>
                    <span>时代: {project.era}</span>
                    <span>进度: {project.progress}%</span>
                </div>
                <div class="project-stats">
                    <span>角色: {project.characters_count}</span>
                    <span>场景: {project.scenes_count}</span>
                    <span>对话: {project.dialogues_count}</span>
                </div>
            </div>
            '''
            project_items.append(item)
        
        project_items_html = ''.join(project_items)
        
        return f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剧本管理 - Producer查看器</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; color: #333; line-height: 1.6; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
        .header {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }}
        .nav-link {{ display: inline-block; margin-bottom: 10px; color: #28a745; text-decoration: none; }}
        .nav-link:hover {{ text-decoration: underline; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }}
        .stat-card {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #28a745; }}
        .projects-list {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
        .project-item {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .project-header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }}
        .project-header h3 {{ color: #28a745; }}
        .project-status {{ padding: 4px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 0.8em; }}
        .project-meta {{ margin-bottom: 10px; color: #666; }}
        .project-meta span {{ margin-right: 15px; }}
        .project-stats span {{ margin-right: 15px; color: #28a745; font-weight: 500; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="nav-link">← 返回首页</a>
            <h1>📝 剧本管理</h1>
            <p>查看和管理所有剧本项目</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{stats.get('total_projects', 0)}</div>
                <div>总项目数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats.get('completed_projects', 0)}</div>
                <div>已完成</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats.get('in_progress_projects', 0)}</div>
                <div>进行中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{stats.get('draft_projects', 0)}</div>
                <div>草稿</div>
            </div>
        </div>
        
        <div class="projects-list">
            {project_items_html}
        </div>
    </div>
</body>
</html>
        '''
    
    def _build_project_html(self, project) -> str:
        """构建项目详情页面HTML"""
        return f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project.title} - Producer查看器</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; color: #333; line-height: 1.6; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
        .header {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }}
        .nav-link {{ display: inline-block; margin-bottom: 10px; color: #28a745; text-decoration: none; }}
        .nav-link:hover {{ text-decoration: underline; }}
        .project-info {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/scripts" class="nav-link">← 返回剧本列表</a>
            <h1>{project.title}</h1>
        </div>
        
        <div class="project-info">
            <h2>项目详情</h2>
            <p><strong>主题:</strong> {project.theme}</p>
            <p><strong>时代:</strong> {project.era}</p>
            <p><strong>状态:</strong> {project.status}</p>
            <p><strong>创建时间:</strong> {project.created_at}</p>
            <p><strong>更新时间:</strong> {project.updated_at}</p>
        </div>
    </div>
</body>
</html>
        '''


def start_unified_viewer(host='localhost', port=9000, debug=False):
    """启动统一查看器服务"""
    try:
        with socketserver.TCPServer((host, port), UnifiedViewerHandler) as httpd:
            print(f"🎬 Producer统一查看器已启动")
            print(f"📍 访问地址: http://{host}:{port}")
            print(f"🧪 测试结果: http://{host}:{port}/results")
            print(f"📝 剧本管理: http://{host}:{port}/scripts")
            print(f"💡 按 Ctrl+C 停止服务")
            
            if not debug:
                # 自动打开浏览器
                threading.Timer(1.0, lambda: webbrowser.open(f'http://{host}:{port}')).start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 统一查看器已停止")
    except Exception as e:
        print(f"❌ 启动统一查看器失败: {e}")


if __name__ == '__main__':
    import sys
    if len(sys.argv) > 1:
        host = sys.argv[1]
        port = int(sys.argv[2]) if len(sys.argv) > 2 else 9000
    else:
        host, port = 'localhost', 9000
    
    start_unified_viewer(host, port)