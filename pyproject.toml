[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "producer"
version = "0.1.0"
description = "LangChain-based historical short drama video automation system with hybrid optimization"
authors = [{name = "Matrix", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.11,<3.13"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core dependencies - Stable versions
    "langchain>=0.3.27,<0.4.0",
    "langchain-community>=0.3.27,<0.4.0",
    "litellm>=1.75.0,<2.0.0",
    "pydantic>=2.11.0,<3.0.0",
    "pydantic-settings>=2.10.0,<3.0.0",
    "pyyaml>=6.0.2",
    "requests>=2.32.3",
    "aiohttp>=3.12.0",
    "aiofiles>=24.1.0",
    # CLI and UI
    "click>=8.1.7",
    "rich>=14.0.0",
    # Media processing - Compatible versions
    "Pillow>=10.4.0,<12.0.0",
    "opencv-python>=*********",
    "moviepy>=2.2.1",
    "numpy>=1.25.0",
    # Audio processing
    "pydub>=0.25.1",
    "soundfile>=0.12.1",
    "numba>=0.60.0,<1.0.0",
    # Logging and monitoring
    "loguru>=0.7.2",
    # Environment management
    "python-dotenv>=1.0.1",
    "edge-tts>=7.2.0",
    "langchain-openai>=0.3.32",
    "langchainhub>=0.1.21",
    "httpx-sse>=0.4.1",
    # Web framework
    "flask>=3.0.0",
    "werkzeug>=3.0.0",
    "jinja2>=3.1.4",
    "markupsafe>=2.1.0",
    "itsdangerous>=2.1.0",
    "blinker>=1.7.0",
    # Security
    "pyjwt>=2.8.0",
    # File handling
    # Using standard library mimetypes instead of python-magic
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "httpx>=0.28.1",
    "fastapi>=0.116.1",
    "uvicorn>=0.34.0",
    "black>=25.1.0",
    "flake8>=7.1.1",
    "mypy>=1.14.0",
    "ruff>=0.9.0",
    "pre-commit>=4.0.1",
    "coverage>=7.6.0",
]

comfyui = [
    "torch>=2.5.1",
    "torchvision>=0.20.1",
    "transformers>=4.48.0",
    "diffusers>=0.34.0",
    "accelerate>=1.2.0",
    "xformers>=0.0.28",
]

local-models = [
    "huggingface-hub>=0.27.0",
    "safetensors>=0.4.6",
    "tokenizers>=0.21.0",
    "sentence-transformers>=3.3.0",
]

all = [
    "producer[dev,comfyui,local-models]",
]

[project.urls]
Homepage = "https://github.com/matrix/producer"
Repository = "https://github.com/matrix/producer"
Issues = "https://github.com/matrix/producer/issues"

[project.scripts]
producer = "producer.__main__:main"

[tool.setuptools.packages.find]
where = ["."]  # list of folders that contain the packages (["src"] by default)
include = ["producer*"]  # package names should match these glob patterns (["*"] by default)
exclude = ["tests*"]  # exclude packages matching these glob patterns

[tool.setuptools.package-dir]
"" = "."

[tool.black]
line-length = 88
target-version = ['py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".env",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]
