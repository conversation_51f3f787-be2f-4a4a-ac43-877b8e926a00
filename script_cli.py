#!/usr/bin/env python3
"""
剧本生成CLI工具

独立的剧本生成和管理工具，支持：
- 创建新剧本项目
- 生成大纲
- 继续完善剧本
- 管理剧本项目
- 导出剧本
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from core.config import ConfigManager
from core.script_generator import ScriptManager, ScriptGenerator


console = Console()


@click.group()
def cli():
    """Producer剧本生成工具"""
    pass


@cli.command()
@click.option('--title', required=True, help='剧本标题')
@click.option('--theme', required=True, help='剧本主题')
@click.option('--era', default='现代', help='时代背景')
@click.option('--duration', type=int, default=60, help='目标时长(秒)')
@click.option('--full', is_flag=True, help='生成完整剧本(包括场景和对话)')
def create(title: str, theme: str, era: str, duration: int, full: bool):
    """创建新的剧本项目"""
    asyncio.run(_create_script(title, theme, era, duration, full))


async def _create_script(title: str, theme: str, era: str, duration: int, full: bool):
    """创建剧本的异步实现"""
    try:
        config_manager = ConfigManager()
        script_manager = ScriptManager(config_manager)
        
        if full:
            # 生成完整剧本
            project = await script_manager.generate_complete_script(title, theme, era, duration)
            rprint(f"[green]✅ 完整剧本生成完成！[/green]")
        else:
            # 只生成大纲
            project = await script_manager.generate_outline_only(title, theme, era, duration)
            rprint(f"[green]✅ 剧本大纲生成完成！[/green]")
        
        # 显示项目信息
        _display_project_info(project)
        
        if not full:
            rprint(f"\n[yellow]💡 提示：使用 'script_cli.py continue {project.id}' 继续生成完整剧本[/yellow]")
        
    except Exception as e:
        rprint(f"[red]❌ 剧本生成失败: {str(e)}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('project_id')
def continue_script(project_id: str):
    """从大纲继续生成完整剧本"""
    asyncio.run(_continue_script(project_id))


async def _continue_script(project_id: str):
    """继续生成剧本的异步实现"""
    try:
        config_manager = ConfigManager()
        script_manager = ScriptManager(config_manager)
        
        project = await script_manager.continue_from_outline(project_id)
        
        rprint(f"[green]✅ 剧本生成完成！[/green]")
        _display_project_info(project)
        
    except Exception as e:
        rprint(f"[red]❌ 剧本生成失败: {str(e)}[/red]")
        sys.exit(1)


@cli.command()
def list_projects():
    """列出所有剧本项目"""
    asyncio.run(_list_projects())


async def _list_projects():
    """列出项目的异步实现"""
    try:
        config_manager = ConfigManager()
        generator = ScriptGenerator(config_manager)
        
        projects = await generator.list_projects()
        
        if not projects:
            rprint("[yellow]📝 暂无剧本项目[/yellow]")
            return
        
        # 创建表格
        table = Table(title="剧本项目列表")
        table.add_column("ID", style="cyan", width=12)
        table.add_column("标题", style="magenta")
        table.add_column("主题", style="green")
        table.add_column("时代", style="blue")
        table.add_column("状态", style="yellow")
        table.add_column("创建时间", style="dim")
        
        for project in projects:
            status_display = {
                "draft": "草稿",
                "outline_complete": "大纲完成",
                "scenes_complete": "场景完成", 
                "dialogues_complete": "对话完成",
                "final": "最终完成"
            }.get(project.status, project.status)
            
            table.add_row(
                project.id[:8] + "...",
                project.title,
                project.theme,
                project.era,
                status_display,
                project.created_at[:10]
            )
        
        console.print(table)
        
    except Exception as e:
        rprint(f"[red]❌ 获取项目列表失败: {str(e)}[/red]")


@cli.command()
@click.argument('project_id')
def show(project_id: str):
    """显示剧本项目详情"""
    asyncio.run(_show_project(project_id))


async def _show_project(project_id: str):
    """显示项目详情的异步实现"""
    try:
        config_manager = ConfigManager()
        generator = ScriptGenerator(config_manager)
        
        project = await generator.load_project(project_id)
        
        if not project:
            rprint(f"[red]❌ 项目 {project_id} 不存在[/red]")
            return
        
        _display_project_info(project, detailed=True)
        
    except Exception as e:
        rprint(f"[red]❌ 获取项目详情失败: {str(e)}[/red]")


@cli.command()
@click.argument('project_id')
@click.confirmation_option(prompt='确定要删除这个项目吗？')
def delete(project_id: str):
    """删除剧本项目"""
    asyncio.run(_delete_project(project_id))


async def _delete_project(project_id: str):
    """删除项目的异步实现"""
    try:
        config_manager = ConfigManager()
        generator = ScriptGenerator(config_manager)
        
        success = await generator.delete_project(project_id)
        
        if success:
            rprint(f"[green]✅ 项目 {project_id} 已删除[/green]")
        else:
            rprint(f"[red]❌ 项目 {project_id} 不存在[/red]")
        
    except Exception as e:
        rprint(f"[red]❌ 删除项目失败: {str(e)}[/red]")


@cli.command()
@click.argument('project_id')
def export(project_id: str):
    """导出剧本为文本文件"""
    asyncio.run(_export_script(project_id))


@cli.command()
@click.option('--host', default='localhost', help='服务器主机地址')
@click.option('--port', type=int, default=9000, help='服务器端口')
@click.option('--debug', is_flag=True, help='调试模式，不自动打开浏览器')
def viewer(host: str, port: int, debug: bool):
    """启动Producer统一查看器Web界面"""
    try:
        from producer.unified_viewer import start_unified_viewer
        
        console.print("\n🎬 启动Producer统一查看器...", style="bold blue")
        console.print(f"📍 主页地址: http://{host}:{port}", style="cyan")
        console.print(f"🧪 测试结果: http://{host}:{port}/results", style="cyan")
        console.print(f"📝 剧本管理: http://{host}:{port}/scripts", style="cyan")
        
        start_unified_viewer(host, port, debug)
        
    except ImportError as e:
        console.print(f"❌ 导入剧本查看器模块失败: {e}", style="bold red")
        console.print("请确保剧本查看器模块已正确安装", style="yellow")
    except Exception as e:
        console.print(f"❌ 启动剧本查看器失败: {e}", style="bold red")


async def _export_script(project_id: str):
    """导出剧本的异步实现"""
    try:
        config_manager = ConfigManager()
        generator = ScriptGenerator(config_manager)
        
        project = await generator.load_project(project_id)
        
        if not project:
            rprint(f"[red]❌ 项目 {project_id} 不存在[/red]")
            return
        
        if project.status not in ["dialogues_complete", "final"]:
            rprint(f"[yellow]⚠️ 项目状态为 {project.status}，建议完成对话生成后再导出[/yellow]")
        
        await generator._export_script(project)
        rprint(f"[green]✅ 剧本已导出[/green]")
        
    except Exception as e:
        rprint(f"[red]❌ 导出剧本失败: {str(e)}[/red]")


def _display_project_info(project, detailed: bool = False):
    """显示项目信息"""
    status_display = {
        "draft": "草稿",
        "outline_complete": "大纲完成",
        "scenes_complete": "场景完成",
        "dialogues_complete": "对话完成", 
        "final": "最终完成"
    }.get(project.status, project.status)
    
    info_text = f"""
[bold]项目ID:[/bold] {project.id}
[bold]标题:[/bold] {project.title}
[bold]主题:[/bold] {project.theme}
[bold]时代:[/bold] {project.era}
[bold]目标时长:[/bold] {project.target_duration}秒
[bold]状态:[/bold] {status_display}
[bold]创建时间:[/bold] {project.created_at}
[bold]更新时间:[/bold] {project.updated_at}
"""
    
    if detailed and project.script_data:
        info_text += f"""
[bold]剧情摘要:[/bold] {project.script_data.summary}
[bold]角色数量:[/bold] {len(project.script_data.characters)}
[bold]场景数量:[/bold] {len(project.script_data.scenes)}
"""
        
        if project.script_data.characters:
            info_text += "\n[bold]角色列表:[/bold]\n"
            for i, char in enumerate(project.script_data.characters, 1):
                info_text += f"  {i}. {char.name} - {char.description}\n"
        
        if project.script_data.scenes:
            info_text += "\n[bold]场景列表:[/bold]\n"
            for i, scene in enumerate(project.script_data.scenes, 1):
                info_text += f"  {i}. {scene.title} ({scene.location})\n"
    
    panel = Panel(info_text.strip(), title="项目信息", border_style="blue")
    console.print(panel)


if __name__ == "__main__":
    cli()
