#!/usr/bin/env python3
"""
大纲生成测试脚本
只执行大纲生成步骤，不需要配置媒体生成API密钥
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from core.config import ConfigManager
from core.chains.outline_chain import OutlineChain, OutlineRequest
from infra.logger import get_logger
import json


async def test_outline_generation(title: str, theme: str, era: str, duration: int):
    """测试大纲生成功能"""
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化大纲生成链路
        outline_chain = OutlineChain(config_manager)
        
        # 构建大纲生成请求
        outline_request = OutlineRequest(
            title=title,
            theme=theme,
            era=era,
            target_duration=duration,
            character_count=3,
            scene_count=5
        )
        
        print("🎬 开始生成大纲...")
        print(f"标题: {title}")
        print(f"主题: {theme}")
        print(f"时代: {era}")
        print(f"时长: {duration}秒")
        print("-" * 50)
        
        # 调用大纲生成
        outline_response = await outline_chain.generate_outline(outline_request)
        
        # 显示结果
        print("✅ 大纲生成完成!")
        print(f"\n📖 剧本标题: {outline_response.title}")
        print(f"📝 剧本摘要: {outline_response.summary}")
        print(f"🎭 角色数量: {len(outline_response.characters)}")
        print(f"🎬 场景数量: {len(outline_response.scenes)}")
        
        print("\n👥 角色列表:")
        for i, character in enumerate(outline_response.characters, 1):
            print(f"  {i}. {character.name} - {character.description}")
        
        print("\n🎬 场景列表:")
        for i, scene in enumerate(outline_response.scenes, 1):
            print(f"  {i}. {scene.title}")
            print(f"     地点: {scene.location}")
            print(f"     时间: {scene.time_period}")
            print(f"     描述: {scene.description[:100]}...")
            print()
        
        # 保存结果到文件
        output_dir = Path("output/outline_test")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        outline_file = output_dir / f"{title}_outline.json"
        with open(outline_file, 'w', encoding='utf-8') as f:
            json.dump({
                "title": outline_response.title,
                "summary": outline_response.summary,
                "characters": [char.dict() for char in outline_response.characters],
                "scenes": [scene.dict() for scene in outline_response.scenes],
                "estimated_cost": getattr(outline_response, 'estimated_cost', 0.0)
            }, f, ensure_ascii=False, indent=2)
        
        print(f"💾 大纲已保存到: {outline_file}")
        
        return True
        
    except Exception as e:
        logger = get_logger("outline_test")
        logger.error(f"❌ 大纲生成失败: {str(e)}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试大纲生成功能')
    parser.add_argument('--title', required=True, help='剧本标题')
    parser.add_argument('--theme', required=True, help='剧本主题')
    parser.add_argument('--era', default='现代', help='时代背景')
    parser.add_argument('--duration', type=int, default=60, help='目标时长(秒)')
    
    args = parser.parse_args()
    
    # 运行测试
    success = asyncio.run(test_outline_generation(
        args.title,
        args.theme, 
        args.era,
        args.duration
    ))
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
