# 基于LangChain的历史短剧视频制作系统 - 生产环境Docker配置
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsm6 \
    libxext6 \
    libfontconfig1 \
    libxrender1 \
    libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装uv包管理器
RUN pip install uv

# 安装Python依赖
RUN uv sync --frozen

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p output temp assets/music assets/sounds assets/characters assets/backgrounds

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口（如果需要API服务）
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import producer; print('OK')" || exit 1

# 默认命令
CMD ["uv", "run", "python", "-m", "producer.cli", "--help"]
