"""
日志配置模块 - 基于loguru的日志系统
实现工业级日志最佳实践，包括：
- 结构化日志输出
- 日志轮转和归档
- 异常捕获和堆栈跟踪
- 性能监控
- 多环境配置
"""

import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger
from pydantic_settings import BaseSettings


class LogSettings(BaseSettings):
    """日志配置设置"""
    # 日志级别
    log_level: str = "INFO"
    
    # 日志格式
    log_format: str = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 日志文件配置
    log_dir: str = "logs"
    log_file_name: str = "producer_{time:YYYY-MM-DD}.log"
    log_retention: str = "7 days"  # 日志保留时间
    log_rotation: str = "100 MB"    # 日志轮转大小
    
    # 调试配置
    debug_format: str = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<magenta>Process: {process.id}</magenta> | "
        "<magenta>Thread: {thread.id}</magenta> | "
        "<level>{message}</level>"
    )
    
    # 性能日志配置
    perf_log_file: str = "performance_{time:YYYY-MM-DD}.log"
    perf_format: str = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<yellow>PERF</yellow> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 错误日志配置
    error_log_file: str = "errors_{time:YYYY-MM-DD}.log"
    error_format: str = (
        "<red>{time:YYYY-MM-DD HH:mm:ss.SSS}</red> | "
        "<level>{level: <8}</level> | "
        "<red>ERROR</red> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>\n{exception}"
    )
    
    class Config:
        env_prefix = "LOG_"
        case_sensitive = False


class LoggerManager:
    """日志管理器 - 单例模式"""
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.settings = LogSettings()
            self._setup_logger()
            LoggerManager._initialized = True
    
    def _setup_logger(self):
        """设置日志系统"""
        # 移除默认处理器
        logger.remove()
        
        # 确保日志目录存在
        log_dir = Path(self.settings.log_dir)
        log_dir.mkdir(exist_ok=True)
        
        # 控制台输出
        logger.add(
            sys.stderr,
            format=self.settings.log_format,
            level=self.settings.log_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # 主日志文件
        logger.add(
            log_dir / self.settings.log_file_name,
            format=self.settings.log_format,
            level=self.settings.log_level,
            rotation=self.settings.log_rotation,
            retention=self.settings.log_retention,
            compression="zip",
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
        
        # 性能日志文件
        logger.add(
            log_dir / self.settings.perf_log_file,
            format=self.settings.perf_format,
            level="INFO",
            rotation=self.settings.log_rotation,
            retention=self.settings.log_retention,
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "PERF" in record["extra"]
        )
        
        # 错误日志文件
        logger.add(
            log_dir / self.settings.error_log_file,
            format=self.settings.error_format,
            level="ERROR",
            rotation=self.settings.log_rotation,
            retention=self.settings.log_retention,
            compression="zip",
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
    
    def get_logger(self, name: Optional[str] = None):
        """获取日志记录器"""
        if name:
            return logger.bind(name=name)
        return logger
    
    def configure_debug(self):
        """配置调试模式"""
        logger.remove()
        logger.add(
            sys.stderr,
            format=self.settings.debug_format,
            level="DEBUG",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    def configure_production(self):
        """配置生产模式"""
        logger.remove()
        logger.add(
            sys.stderr,
            format=self.settings.log_format,
            level="INFO",
            colorize=False,
            backtrace=False,
            diagnose=False
        )


# 全局日志管理器实例
log_manager = LoggerManager()


def get_logger(name: Optional[str] = None):
    """获取日志记录器的便捷函数"""
    return log_manager.get_logger(name)


def contextualize(**kwargs):
    """为全局日志记录器添加上下文信息"""
    global logger
    logger = logger.bind(**kwargs)
    return logger


def clear_contextualize():
    """清除全局日志记录器的上下文信息"""
    global logger
    logger.remove()
    log_manager._setup_logger()
    return logger


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, name: str = "performance"):
        self.name = name
        self.logger = get_logger(name).bind(PERF=True)
    
    def log_execution_time(self, operation: str, start_time: datetime, **kwargs):
        """记录执行时间"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.info(f"{operation} | duration={duration:.3f}s | {extra_info}")
    
    def log_api_call(self, api_name: str, duration: float, status: str, **kwargs):
        """记录API调用"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.info(f"API_CALL | {api_name} | duration={duration:.3f}s | status={status} | {extra_info}")
    
    def measure(self, operation_name: str):
        """上下文管理器，用于测量操作耗时"""
        return _PerformanceMeasure(self, operation_name)
    
    def record(self, operation_name: str, duration: float, **kwargs):
        """手动记录性能数据"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.info(f"{operation_name} | duration={duration:.3f}s | {extra_info}")


class _PerformanceMeasure:
    """性能测量上下文管理器"""
    
    def __init__(self, perf_logger: PerformanceLogger, operation_name: str):
        self.perf_logger = perf_logger
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        status = "error" if exc_type else "success"
        
        if exc_type:
            self.perf_logger.logger.error(
                f"{self.operation_name} | duration={duration:.3f}s | status={status} | error={str(exc_val)}"
            )
        else:
            self.perf_logger.logger.info(
                f"{self.operation_name} | duration={duration:.3f}s | status={status}"
            )


def log_function_execution(func):
    """函数执行时间装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.bind(PERF=True).info(
                f"FUNCTION | {func.__name__} | duration={duration:.3f}s | status=success"
            )
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.bind(PERF=True).error(
                f"FUNCTION | {func.__name__} | duration={duration:.3f}s | status=error | error={str(e)}"
            )
            raise
    
    return wrapper


def log_api_call(func):
    """API调用装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.bind(PERF=True).info(
                f"API | {func.__name__} | duration={duration:.3f}s | status=success"
            )
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.bind(PERF=True).error(
                f"API | {func.__name__} | duration={duration:.3f}s | status=error | error={str(e)}"
            )
            raise
    
    return wrapper


def setup_logging_environment():
    """设置日志环境"""
    # 根据环境变量配置日志级别
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        # 生产模式：同时输出到控制台和文件
        logger.remove()
        
        # 确保日志目录存在
        log_dir = Path(log_manager.settings.log_dir)
        log_dir.mkdir(exist_ok=True)
        
        # 控制台输出
        logger.add(
            sys.stderr,
            format=log_manager.settings.log_format,
            level="INFO",
            colorize=False,
            backtrace=False,
            diagnose=False
        )
        
        # 主日志文件
        logger.add(
            log_dir / log_manager.settings.log_file_name,
            format=log_manager.settings.log_format,
            level="INFO",
            rotation=log_manager.settings.log_rotation,
            retention=log_manager.settings.log_retention,
            compression="zip",
            encoding="utf-8",
            backtrace=False,
            diagnose=False
        )
        
        # 性能日志文件
        logger.add(
            log_dir / log_manager.settings.perf_log_file,
            format=log_manager.settings.perf_format,
            level="INFO",
            rotation=log_manager.settings.log_rotation,
            retention=log_manager.settings.log_retention,
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "PERF" in record["extra"]
        )
        
        # 错误日志文件
        logger.add(
            log_dir / log_manager.settings.error_log_file,
            format=log_manager.settings.error_format,
            level="ERROR",
            rotation=log_manager.settings.log_rotation,
            retention=log_manager.settings.log_retention,
            compression="zip",
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
    elif env == "development":
        log_manager.configure_debug()
    
    # 捕获未处理的异常
    def handle_exception(exc_type, exc_value, exc_traceback):
        logger.opt(exception=(exc_type, exc_value, exc_traceback)).error("未捕获的异常")
    
    sys.excepthook = handle_exception


# 初始化日志环境
setup_logging_environment()