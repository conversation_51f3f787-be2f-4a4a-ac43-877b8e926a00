#!/usr/bin/env python3
"""
视频制作CLI工具

支持两种工作流：
1. 完整制作：从头开始创建剧本并制作视频
2. 基于剧本制作：使用现有剧本制作视频
"""

import asyncio
import sys
from pathlib import Path
import click
from rich.console import Console
from rich import print as rprint

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from core.config import ConfigManager
from core.workflow import WorkflowEngine
from core.script_generator import ScriptManager
from core.data_models import ScriptData

console = Console()


@click.group()
def cli():
    """Producer视频制作工具"""
    pass


@cli.command()
@click.option('--title', required=True, help='剧本标题')
@click.option('--theme', required=True, help='剧本主题')
@click.option('--era', default='现代', help='时代背景')
@click.option('--duration', type=int, default=60, help='目标时长(秒)')
@click.option('--output-dir', help='输出目录')
@click.option('--config', help='配置文件路径')
def produce(title: str, theme: str, era: str, duration: int, output_dir: str, config: str):
    """制作历史短剧视频（完整流程：剧本+视频）"""
    asyncio.run(_produce_video(title, theme, era, duration, output_dir, config))


@cli.command()
@click.argument('script_id')
@click.option('--output-dir', help='输出目录')
@click.option('--config', help='配置文件路径')
def produce_from_script(script_id: str, output_dir: str, config: str):
    """基于现有剧本制作视频"""
    asyncio.run(_produce_from_script(script_id, output_dir, config))


async def _produce_video(title: str, theme: str, era: str, duration: int, output_dir: str, config: str):
    """完整视频制作流程"""
    try:
        rprint(f"[blue]🎬 开始制作视频: {title}[/blue]")
        
        # 初始化配置
        config_manager = ConfigManager()
        if config:
            # 如果指定了配置文件，可以在这里加载
            pass
        
        # 创建工作流引擎
        workflow_engine = WorkflowEngine(config_manager)
        
        # 创建脚本数据
        script_data = ScriptData(
            title=title,
            theme=theme,
            era=era,
            target_duration=duration
        )
        
        # 执行完整工作流
        execution = await workflow_engine.execute_workflow(script_data)
        
        # 显示结果
        if execution.status.value == "completed":
            rprint(f"[green]✅ 视频制作完成！[/green]")
            
            # 显示输出信息
            final_results = execution.step_results.get('finalize_output', {})
            if final_results.get('project_directory'):
                rprint(f"[cyan]📁 项目目录: {final_results['project_directory']}[/cyan]")
            
            compose_results = execution.step_results.get('compose_video', {})
            if compose_results.get('final_video_path'):
                rprint(f"[cyan]🎥 视频文件: {compose_results['final_video_path']}[/cyan]")
        else:
            rprint(f"[red]❌ 视频制作失败，状态: {execution.status.value}[/red]")
            if execution.error_message:
                rprint(f"[red]错误信息: {execution.error_message}[/red]")
        
    except Exception as e:
        rprint(f"[red]❌ 视频制作失败: {str(e)}[/red]")
        sys.exit(1)


async def _produce_from_script(script_id: str, output_dir: str, config: str):
    """基于现有剧本制作视频"""
    try:
        rprint(f"[blue]🎬 基于剧本 {script_id} 制作视频[/blue]")
        
        # 初始化配置
        config_manager = ConfigManager()
        if config:
            # 如果指定了配置文件，可以在这里加载
            pass
        
        # 获取剧本数据
        script_manager = ScriptManager(config_manager)
        script_data = await script_manager.get_script_for_video_production(script_id)
        
        if not script_data:
            rprint(f"[red]❌ 找不到剧本 {script_id} 或剧本未完成[/red]")
            return
        
        rprint(f"[green]✅ 找到剧本: {script_data.title}[/green]")
        
        # 创建工作流引擎
        workflow_engine = WorkflowEngine(config_manager)
        
        # 执行视频制作工作流（跳过剧本生成步骤）
        execution = await workflow_engine.execute_video_production_workflow(script_data)
        
        # 显示结果
        if execution.status.value == "completed":
            rprint(f"[green]✅ 视频制作完成！[/green]")
            
            # 显示输出信息
            final_results = execution.step_results.get('finalize_output', {})
            if final_results.get('project_directory'):
                rprint(f"[cyan]📁 项目目录: {final_results['project_directory']}[/cyan]")
            
            compose_results = execution.step_results.get('compose_video', {})
            if compose_results.get('final_video_path'):
                rprint(f"[cyan]🎥 视频文件: {compose_results['final_video_path']}[/cyan]")
        else:
            rprint(f"[red]❌ 视频制作失败，状态: {execution.status.value}[/red]")
            if execution.error_message:
                rprint(f"[red]错误信息: {execution.error_message}[/red]")
        
    except Exception as e:
        rprint(f"[red]❌ 视频制作失败: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    cli()
