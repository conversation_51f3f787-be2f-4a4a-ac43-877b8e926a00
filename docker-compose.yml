version: '3.8'

services:
  producer:
    build: .
    container_name: historical-drama-producer
    environment:
      # 文本生成API密钥
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
      
      # 媒体生成API密钥
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - KLING_API_KEY=${KLING_API_KEY}
      
      # 系统配置
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - CONFIG_PATH=/app/config/config.yaml
      
    volumes:
      # 配置文件
      - ./config:/app/config
      - ./templates:/app/templates
      
      # 输出目录
      - ./output:/app/output
      - ./temp:/app/temp
      
      # 媒体资源
      - ./assets:/app/assets
      
      # 日志
      - ./logs:/app/logs
      
    ports:
      - "8000:8000"  # API服务端口
      
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import producer; print('OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: producer-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 可选：PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: producer-postgres
    environment:
      - POSTGRES_DB=producer
      - POSTGRES_USER=producer
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-producer123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
