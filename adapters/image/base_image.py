"""图像生成适配器基础类

定义图像生成适配器的通用接口和功能。
"""

import asyncio
import base64
import io
from abc import abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
from PIL import Image

from adapters.base import APIAdapter, AdapterResult, AdapterConfig
from core.models import MediaType
from core.config import ConfigManager
from core.cost_control import CostController


class ImageQuality(Enum):
    """图像质量枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"


class ImageStyle(Enum):
    """图像风格枚举"""
    REALISTIC = "realistic"
    ANIME = "anime"
    CARTOON = "cartoon"
    ARTISTIC = "artistic"
    ABSTRACT = "abstract"
    VINTAGE = "vintage"
    FUTURISTIC = "futuristic"
    NATURAL = "natural"


@dataclass
class ImageGenerationRequest:
    """图像生成请求"""
    prompt: str
    negative_prompt: Optional[str] = None
    width: int = 1024
    height: int = 1024
    steps: int = 20
    guidance_scale: float = 7.5
    seed: Optional[int] = None
    num_images: int = 1
    style: Optional[str] = None
    aspect_ratio: Optional[str] = None
    output_format: str = "png"
    quality: int = 95
    

@dataclass
class ImageGenerationResponse:
    """图像生成响应"""
    images: List[bytes]  # 图像数据列表
    image_urls: Optional[List[str]] = None  # 图像URL列表（如果有）
    metadata: Dict[str, Any] = None
    model_name: str = ""
    generation_time: float = 0.0
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def save_images(self, output_dir: str, prefix: str = "generated") -> List[str]:
        """保存图像到本地文件"""
        import os
        from pathlib import Path
        
        saved_paths = []
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for i, image_data in enumerate(self.images):
            filename = f"{prefix}_{i+1}.png"
            file_path = output_path / filename
            
            # 将字节数据转换为PIL图像并保存
            image = Image.open(io.BytesIO(image_data))
            image.save(file_path, "PNG")
            saved_paths.append(str(file_path))
        
        return saved_paths


class ImageGenerationAdapter(APIAdapter):
    """图像生成适配器基础类"""
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.model_name = config.model or 'flux-pro'
        self.default_size = config.custom_params.get('default_size', '1024x1024')
        self.max_images_per_request = config.custom_params.get('max_images_per_request', 4)
        
    @property
    def media_type(self) -> MediaType:
        return MediaType.IMAGE
    
    @property
    def supported_formats(self) -> List[str]:
        return ['image/png', 'image/jpeg', 'image/webp']
    
    async def generate(self, prompt: str, **kwargs) -> AdapterResult:
        """生成图像内容"""
        try:
            # 构建请求
            request = self._build_request(prompt, **kwargs)
            
            # 验证请求参数
            if not self._validate_request(request):
                return AdapterResult(
                    success=False,
                    error_message="Invalid request parameters"
                )
            
            # 估算成本
            estimated_cost = self.estimate_cost(prompt, **kwargs)
            await self._check_budget(estimated_cost)
            
            # 执行生成
            response = await self._execute_with_retry(self._generate_image, request)
            
            if response.success:
                # 记录实际成本
                from core.cost_control import ServiceType
                actual_cost = self._calculate_actual_cost(response.data, request)
                await self.cost_controller.record_cost(
                    cost_usd=actual_cost,
                    operation="generate",
                    service_type=ServiceType.IMAGE_GENERATION,
                    service_name=self.model_name,
                    metadata={
                        'model': self.model_name,
                        'resolution': f"{request.width}x{request.height}",
                        'num_images': request.num_images,
                        'steps': request.steps
                    }
                )
                response.cost_usd = actual_cost
            
            return response
            
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"Image generation failed: {str(e)}"
            )
    
    def _build_request(self, prompt: str, **kwargs) -> ImageGenerationRequest:
        """构建图像生成请求"""
        # 解析尺寸
        size = kwargs.get('size', self.default_size)
        if isinstance(size, str) and 'x' in size:
            width, height = map(int, size.split('x'))
        else:
            width = kwargs.get('width', 1024)
            height = kwargs.get('height', 1024)
        
        return ImageGenerationRequest(
            prompt=prompt,
            negative_prompt=kwargs.get('negative_prompt'),
            width=width,
            height=height,
            steps=kwargs.get('steps', 20),
            guidance_scale=kwargs.get('guidance_scale', 7.5),
            seed=kwargs.get('seed'),
            num_images=min(kwargs.get('num_images', 1), self.max_images_per_request),
            style=kwargs.get('style'),
            aspect_ratio=kwargs.get('aspect_ratio'),
            output_format=kwargs.get('output_format', 'png'),
            quality=kwargs.get('quality', 95)
        )
    
    def _validate_request(self, request: ImageGenerationRequest) -> bool:
        """验证请求参数"""
        # 检查基本参数
        if not request.prompt or not request.prompt.strip():
            return False
        
        # 检查尺寸限制
        if request.width < 64 or request.width > 2048:
            return False
        if request.height < 64 or request.height > 2048:
            return False
        
        # 检查步数
        if request.steps < 1 or request.steps > 100:
            return False
        
        # 检查图像数量
        if request.num_images < 1 or request.num_images > self.max_images_per_request:
            return False
        
        return True
    
    @abstractmethod
    async def _generate_image(self, request: ImageGenerationRequest) -> AdapterResult:
        """执行实际的图像生成"""
        pass
    
    @abstractmethod
    def _calculate_actual_cost(self, response: ImageGenerationResponse, request: ImageGenerationRequest) -> float:
        """计算实际成本"""
        pass
    
    def estimate_cost(self, prompt: str, **kwargs) -> float:
        """估算生成成本"""
        # 获取基础参数
        num_images = min(kwargs.get('num_images', 1), self.max_images_per_request)
        steps = kwargs.get('steps', 20)
        
        # 解析尺寸
        size = kwargs.get('size', self.default_size)
        if isinstance(size, str) and 'x' in size:
            width, height = map(int, size.split('x'))
        else:
            width = kwargs.get('width', 1024)
            height = kwargs.get('height', 1024)
        
        # 计算像素数
        total_pixels = width * height * num_images
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 基础成本计算
        base_cost = pricing.get('base_cost_per_image', 0.01) * num_images
        
        # 根据分辨率调整成本
        resolution_multiplier = max(1.0, total_pixels / (1024 * 1024))  # 以1024x1024为基准
        
        # 根据步数调整成本
        steps_multiplier = max(1.0, steps / 20)  # 以20步为基准
        
        return base_cost * resolution_multiplier * steps_multiplier
    
    @abstractmethod
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        pass
    
    def _encode_image_to_base64(self, image_data: bytes) -> str:
        """将图像数据编码为base64字符串"""
        return base64.b64encode(image_data).decode('utf-8')
    
    def _decode_base64_to_image(self, base64_str: str) -> bytes:
        """将base64字符串解码为图像数据"""
        return base64.b64decode(base64_str)
    
    def _resize_image(self, image_data: bytes, target_width: int, target_height: int) -> bytes:
        """调整图像尺寸"""
        image = Image.open(io.BytesIO(image_data))
        resized_image = image.resize((target_width, target_height), Image.Resampling.LANCZOS)
        
        output = io.BytesIO()
        resized_image.save(output, format='PNG')
        return output.getvalue()
    
    async def validate_config(self) -> bool:
        """验证配置"""
        if not await super().validate_config():
            return False
        
        # 验证模型名称
        if not self.model_name:
            return False
        
        return True