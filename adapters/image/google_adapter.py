"""Google图像生成适配器

支持Gemini 2.0 Flash和Imagen模型的图像生成功能。
免费层级支持，成本极低的图像生成方案。
"""

import asyncio
import base64
import io
import os
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

import aiohttp
from PIL import Image

from .base_image import (
    ImageGenerationAdapter, 
    ImageGenerationRequest, 
    ImageGenerationResponse,
    AdapterResult
)
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController


class GoogleImageAdapter(ImageGenerationAdapter):
    """Google图像生成适配器
    
    支持以下模型：
    - gemini-2.0-flash (免费图像生成)
    - imagen-4.0-generate-001 (高质量图像生成)
    - imagen-3.0-generate-001 (标准图像生成)
    """
    
    SUPPORTED_MODELS = {
        "gemini-2.0-flash-exp": {
            "name": "Gemini 2.0 Flash Experimental",
            "endpoint": "generateContent",
            "free_tier": True,
            "max_resolution": "1024x1024",
            "price_per_image": 0.0,  # 免费层级
            "supports_batch": False,
            "response_format": "inline_data"
        },
        "gemini-2.0-flash-preview-image-generation": {
            "name": "Gemini 2.0 Flash Image Generation",
            "endpoint": "generateContent",
            "free_tier": True,
            "max_resolution": "1024x1024",
            "price_per_image": 0.0,  # 免费层级
            "supports_batch": False,
            "response_format": "inline_data"
        },
        "gemini-2.0-flash": {
            "name": "Gemini 2.0 Flash",
            "endpoint": "generateContent",
            "free_tier": True,
            "max_resolution": "1024x1024",
            "price_per_image": 0.0,  # 免费层级
            "supports_batch": False,
            "response_format": "inline_data"
        },
        "imagen-4.0-generate-preview-06-06": {
            "name": "Imagen 4 Generate Preview",
            "endpoint": "predict",
            "free_tier": False,
            "max_resolution": "2048x2048",
            "price_per_image": 0.02,  # $0.02 per image
            "supports_batch": True,
            "response_format": "base64"
        },
        "imagen-3.0-generate-002": {
            "name": "Imagen 3 Generate",
            "endpoint": "predict",
            "free_tier": False,
            "max_resolution": "1024x1024",
            "price_per_image": 0.03,  # $0.03 per image
            "supports_batch": True,
            "response_format": "base64"
        }
    }
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        
        if not self.api_key:
            raise ValueError("Google AI API key not found. Please set GOOGLE_AI_API_KEY or GEMINI_API_KEY environment variable.")
        
        # 设置默认模型（优先免费模型）
        if not self.model_name or self.model_name not in self.SUPPORTED_MODELS:
            self.model_name = "gemini-2.0-flash-exp"  # 使用实验版本
        
        self.model_config = self.SUPPORTED_MODELS[self.model_name]
        self.service_name = "google_image"
        
    async def _generate_image(self, request: ImageGenerationRequest) -> AdapterResult:
        """生成图像"""
        try:
            start_time = time.time()
            
            if self.model_name.startswith("gemini"):
                result = await self._generate_with_gemini(request)
            else:
                result = await self._generate_with_imagen(request)
            
            generation_time = time.time() - start_time
            
            if result.success:
                result.data.generation_time = generation_time
                result.data.model_name = self.model_name
            
            return result
            
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"Google image generation failed: {str(e)}"
            )
    
    async def _generate_with_gemini(self, request: ImageGenerationRequest) -> AdapterResult:
        """使用Gemini模型生成图像"""
        url = f"{self.base_url}/models/{self.model_name}:generateContent"
        
        headers = {
            "x-goog-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        # 构建Gemini请求
        payload = {
            "contents": [{
                "parts": [{
                    "text": self._build_gemini_prompt(request)
                }]
            }],
            "generationConfig": {
                "responseModalities": ["TEXT", "IMAGE"]
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    return AdapterResult(
                        success=False,
                        error_message=f"API request failed: {response.status} - {error_text}"
                    )
                
                data = await response.json()
                return self._parse_gemini_response(data, request)
    
    async def _generate_with_imagen(self, request: ImageGenerationRequest) -> AdapterResult:
        """使用Imagen模型生成图像"""
        url = f"{self.base_url}/models/{self.model_name}:predict"
        
        headers = {
            "x-goog-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        # 构建Imagen请求
        payload = {
            "instances": [{
                "prompt": request.prompt
            }],
            "parameters": {
                "sampleCount": min(request.num_images, 4),  # Imagen最多支持4张图片
            }
        }
        
        # 添加可选参数
        if request.aspect_ratio:
            payload["parameters"]["aspectRatio"] = request.aspect_ratio
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    return AdapterResult(
                        success=False,
                        error_message=f"API request failed: {response.status} - {error_text}"
                    )
                
                data = await response.json()
                return self._parse_imagen_response(data, request)
    
    def _build_gemini_prompt(self, request: ImageGenerationRequest) -> str:
        """构建Gemini提示词"""
        prompt = f"Generate an image: {request.prompt}"
        
        # 添加尺寸说明
        if request.width != 1024 or request.height != 1024:
            prompt += f" Image size should be {request.width}x{request.height}."
        
        # 添加风格说明
        if request.style:
            prompt += f" Style: {request.style}."
        
        # 添加负面提示
        if request.negative_prompt:
            prompt += f" Avoid: {request.negative_prompt}."
        
        return prompt
    
    def _parse_gemini_response(self, data: Dict, request: ImageGenerationRequest) -> AdapterResult:
        """解析Gemini响应"""
        try:
            candidates = data.get("candidates", [])
            if not candidates:
                return AdapterResult(
                    success=False,
                    error_message="No candidates in response"
                )
            
            images = []
            for candidate in candidates:
                content = candidate.get("content", {})
                parts = content.get("parts", [])
                
                for part in parts:
                    if "inlineData" in part:
                        inline_data = part["inlineData"]
                        image_data = base64.b64decode(inline_data["data"])
                        images.append(image_data)
            
            if not images:
                return AdapterResult(
                    success=False,
                    error_message="No images found in response"
                )
            
            response = ImageGenerationResponse(
                images=images,
                metadata={
                    "model": self.model_name,
                    "prompt": request.prompt,
                    "num_generated": len(images)
                }
            )
            
            return AdapterResult(success=True, data=response)
            
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"Failed to parse response: {str(e)}"
            )
    
    def _parse_imagen_response(self, data: Dict, request: ImageGenerationRequest) -> AdapterResult:
        """解析Imagen响应"""
        try:
            predictions = data.get("predictions", [])
            if not predictions:
                return AdapterResult(
                    success=False,
                    error_message="No predictions in response"
                )
            
            images = []
            for prediction in predictions:
                # Imagen返回base64编码的图像
                if "bytesBase64Encoded" in prediction:
                    image_data = base64.b64decode(prediction["bytesBase64Encoded"])
                    images.append(image_data)
                elif "imageBytes" in prediction:
                    image_data = base64.b64decode(prediction["imageBytes"])
                    images.append(image_data)
            
            if not images:
                return AdapterResult(
                    success=False,
                    error_message="No images found in response"
                )
            
            response = ImageGenerationResponse(
                images=images,
                metadata={
                    "model": self.model_name,
                    "prompt": request.prompt,
                    "num_generated": len(images)
                }
            )
            
            return AdapterResult(success=True, data=response)
            
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"Failed to parse response: {str(e)}"
            )
    
    def estimate_cost(self, prompt: str, **kwargs) -> float:
        """估算图像生成成本"""
        num_images = kwargs.get('num_images', 1)
        price_per_image = self.model_config["price_per_image"]
        return num_images * price_per_image
    
    def _calculate_actual_cost(self, response: ImageGenerationResponse, request: ImageGenerationRequest) -> float:
        """计算实际成本"""
        num_images = len(response.images)
        price_per_image = self.model_config["price_per_image"]
        return num_images * price_per_image
    
    async def validate_config(self) -> bool:
        """验证配置"""
        if not self.api_key:
            return False
            
        try:
            # 发送测试请求
            test_request = ImageGenerationRequest(
                prompt="A simple red circle on white background",
                num_images=1,
                width=512,
                height=512
            )
            result = await self._generate_image(test_request)
            return result.success
        except:
            return False
    
    async def _create_client(self):
        """创建API客户端（Google使用aiohttp，不需要单独的客户端）"""
        # Google API 使用 aiohttp 直接请求，不需要单独的客户端
        pass
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        return {
            "base_cost_per_image": self.model_config["price_per_image"]
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.model_config.copy()
    
    def list_available_models(self) -> List[str]:
        """列出可用模型"""
        return list(self.SUPPORTED_MODELS.keys())
    
    def is_free_tier_available(self) -> bool:
        """检查是否有免费层级可用"""
        return self.model_config.get("free_tier", False)
    
    def get_recommended_settings(self, use_case: str = "general") -> Dict[str, Any]:
        """获取推荐设置"""
        if use_case == "cost_optimized":
            return {
                "model": "gemini-2.0-flash",
                "num_images": 1,
                "size": "1024x1024"
            }
        elif use_case == "high_quality":
            return {
                "model": "imagen-4.0-ultra",
                "num_images": 1,
                "size": "2048x2048"
            }
        else:
            return {
                "model": "imagen-4.0-fast",
                "num_images": 1,
                "size": "1024x1024"
            }


# 工厂函数
def create_google_image_adapter(
    config: AdapterConfig, 
    config_manager: ConfigManager, 
    cost_controller: CostController
) -> GoogleImageAdapter:
    """创建Google图像生成适配器"""
    return GoogleImageAdapter(config, config_manager, cost_controller)


# 便捷函数
async def generate_image_with_google(
    prompt: str, 
    model: str = "gemini-2.0-flash",
    num_images: int = 1,
    size: str = "1024x1024",
    **kwargs
) -> ImageGenerationResponse:
    """便捷的Google图像生成函数"""
    config = AdapterConfig(
        service_name="google_image",
        model=model,
        api_url="https://generativelanguage.googleapis.com/v1beta"
    )
    
    # 需要实际的config_manager和cost_controller实例
    from core.config import ConfigManager
    from core.cost_control import CostController
    
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    adapter = create_google_image_adapter(config, config_manager, cost_controller)
    
    result = await adapter.generate(
        prompt=prompt,
        num_images=num_images,
        size=size,
        **kwargs
    )
    
    if result.success:
        return result.data
    else:
        raise Exception(f"Image generation failed: {result.error_message}")