"""适配器模块

包含各种媒体生成服务的适配器：
- 文本生成适配器
- 图像生成适配器
- 视频生成适配器
- 语音合成适配器
- 混合媒体管道
"""

from .base import BaseAdapter, AdapterConfig, AdapterResult
from .text import TextGenerationAdapter, QwenAdapter, GPTAdapter, ClaudeAdapter
# 图像生成适配器
from .image import FluxImageAdapter, SDXLLightningAdapter
from .voice import VoiceSynthesisAdapter
from .voice import CosyVoiceAdapter, EdgeTTSAdapter
from .video import VideoGenerationAdapter
from .video import KlingVideoAdapter, RunwayAdapter
# TODO: 待实现的适配器
# from .image_adapter import ImageGenerationAdapter
# from .hybrid_pipeline import HybridMediaPipeline

__all__ = [
    "BaseAdapter",
    "AdapterConfig",
    "AdapterResult",
    "TextGenerationAdapter",
    "QwenAdapter",
    "GPTAdapter",
    "ClaudeAdapter",
    "FluxImageAdapter",
    "SDXLLightningAdapter",
    "VoiceSynthesisAdapter",
    "CosyVoiceAdapter",
    "EdgeTTSAdapter",
    "VideoGenerationAdapter",
    "KlingVideoAdapter",
    "RunwayAdapter",
    # TODO: 待实现的适配器
    # "ImageGenerationAdapter",
    # "HybridMediaPipeline"
]