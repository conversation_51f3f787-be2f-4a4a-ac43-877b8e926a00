"""基础适配器模块

定义所有适配器的通用接口和数据结构。
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.models import MediaType, QualityLevel
from core.config import ConfigManager
from core.cost_control import CostController


class AdapterError(Exception):
    """适配器相关错误的基础异常类"""
    pass


class RateLimitError(AdapterError):
    """速率限制错误"""
    pass


class APIError(AdapterError):
    """API调用错误"""
    pass


class AdapterStatus(str, Enum):
    """适配器状态枚举"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    UNAVAILABLE = "unavailable"


@dataclass
class AdapterConfig:
    """适配器配置"""
    service_name: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: Optional[str] = None
    timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit: Optional[int] = None  # 每分钟请求数
    quality_level: QualityLevel = QualityLevel.HIGH
    custom_params: Dict[str, Any] = None
    extra_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}
        if self.extra_params is None:
            self.extra_params = {}


@dataclass
class AdapterResult:
    """适配器执行结果"""
    success: bool
    data: Optional[Any] = None
    error_message: Optional[str] = None
    cost_usd: float = 0.0
    duration_seconds: float = 0.0
    metadata: Dict[str, Any] = None
    quality_score: Optional[float] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseAdapter(ABC):
    """基础适配器抽象类"""
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        self.config = config
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.status = AdapterStatus.IDLE
        self.last_request_time: Optional[datetime] = None
        self.request_count = 0
        self.total_cost = 0.0
        
        # 添加logger和session属性
        import logging
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.session = None  # 将在子类中初始化
        
        # 速率限制相关
        self._request_times: List[datetime] = []
        self._lock = asyncio.Lock()
    
    @property
    @abstractmethod
    def media_type(self) -> MediaType:
        """返回适配器处理的媒体类型"""
        pass
    
    @property
    @abstractmethod
    def supported_formats(self) -> List[str]:
        """返回支持的输出格式"""
        pass
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> AdapterResult:
        """生成媒体内容"""
        pass
    
    @abstractmethod
    async def validate_config(self) -> bool:
        """验证配置是否有效"""
        pass
    
    @abstractmethod
    def estimate_cost(self, prompt: str, **kwargs) -> float:
        """估算生成成本"""
        pass
    
    def _get_requests_per_minute(self) -> Optional[int]:
        """解析配置中的速率限制，支持int或dict形式。
        返回每分钟允许的请求数，未设置则返回None。
        """
        rl = self.config.rate_limit
        if not rl:
            return None
        if isinstance(rl, int):
            return rl
        if isinstance(rl, dict):
            rpm = rl.get("requests_per_minute")
            try:
                return int(rpm) if rpm is not None else None
            except (TypeError, ValueError):
                return None
        return None

    async def _check_rate_limit(self) -> bool:
        """检查速率限制"""
        rpm = self._get_requests_per_minute()
        if not rpm:
            return True
        
        async with self._lock:
            now = datetime.now()
            # 清理一分钟前的请求记录
            cutoff_time = now.timestamp() - 60
            self._request_times = [
                req_time for req_time in self._request_times
                if req_time.timestamp() > cutoff_time
            ]
            
            # 检查是否超过速率限制
            if len(self._request_times) >= rpm:
                return False
            
            # 记录当前请求时间
            self._request_times.append(now)
            return True
    
    async def _wait_for_rate_limit(self):
        """等待速率限制重置"""
        if not self._get_requests_per_minute():
            return
        
        while not await self._check_rate_limit():
            await asyncio.sleep(1)
    
    async def _execute_with_retry(self, operation, *args, **kwargs) -> AdapterResult:
        """带重试的执行操作"""
        last_error = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # 等待速率限制
                await self._wait_for_rate_limit()
                
                # 执行操作
                start_time = datetime.now()
                result = await operation(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                # 更新统计信息
                self.request_count += 1
                self.last_request_time = start_time
                self.total_cost += result.cost_usd
                result.duration_seconds = duration
                
                # 成功返回
                if result.success:
                    return result
                # 如果返回标记失败，则视为异常进行重试/抛出
                from adapters.base import APIError
                raise APIError(result.error_message or "Operation failed")
                
            except Exception as e:
                last_error = e
                if attempt < self.config.max_retries:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    # 重试用尽，抛出最后的异常
                    raise last_error
        
        # 理论不可达，防御性返回
        from adapters.base import APIError
        raise APIError(f"Failed after {self.config.max_retries + 1} attempts: {str(last_error)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取适配器统计信息"""
        return {
            "service_name": self.config.service_name,
            "media_type": self.media_type.value,
            "status": self.status.value,
            "request_count": self.request_count,
            "total_cost_usd": self.total_cost,
            "average_cost_per_request": self.total_cost / self.request_count if self.request_count > 0 else 0,
            "last_request_time": self.last_request_time.isoformat() if self.last_request_time else None,
            "rate_limit": self.config.rate_limit,
            "current_rate": len(self._request_times)  # 当前分钟内的请求数
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 基础配置验证
            if not await self.validate_config():
                self.status = AdapterStatus.ERROR
                return False
            
            # 检查API可用性（子类可以重写此方法进行更详细的检查）
            self.status = AdapterStatus.IDLE
            return True
            
        except Exception:
            self.status = AdapterStatus.ERROR
            return False
    
    def reset_statistics(self):
        """重置统计信息"""
        self.request_count = 0
        self.total_cost = 0.0
        self.last_request_time = None
        self._request_times.clear()


class LocalAdapter(BaseAdapter):
    """本地适配器基类
    
    用于本地模型和工具的适配器。
    """
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.model_path: Optional[str] = None
        self.model_loaded = False
    
    @abstractmethod
    async def load_model(self) -> bool:
        """加载本地模型"""
        pass
    
    @abstractmethod
    async def unload_model(self):
        """卸载本地模型"""
        pass
    
    def estimate_cost(self, prompt: str, **kwargs) -> float:
        """本地模型通常没有API成本"""
        return 0.0
    
    async def validate_config(self) -> bool:
        """验证本地模型配置"""
        if not self.model_path:
            return False
        
        from pathlib import Path
        return Path(self.model_path).exists()


class APIAdapter(BaseAdapter):
    """API适配器基类
    
    用于云端API服务的适配器。
    """
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.client = None
    
    @abstractmethod
    async def _create_client(self):
        """创建API客户端"""
        pass
    
    async def validate_config(self) -> bool:
        """验证API配置"""
        if not self.config.api_key:
            return False
        
        try:
            await self._create_client()
            return True
        except Exception:
            return False
    
    async def _check_budget(self, estimated_cost: float) -> bool:
        """检查预算是否充足"""
        can_afford, reason = self.cost_controller.can_afford(estimated_cost)
        if not can_afford:
            raise Exception(f"Budget check failed: {reason}")
        return True