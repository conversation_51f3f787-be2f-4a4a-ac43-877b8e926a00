#!/usr/bin/env python3
"""
SVD (Stable Video Diffusion) 视频适配器
用于本地视频转场和动效生成
"""

import asyncio
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List
import json

from adapters.video.base_video import VideoGenerationAdapter, VideoGenerationRequest, VideoGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController

logger = logging.getLogger(__name__)


class SVDVideoAdapter(VideoGenerationAdapter):
    """SVD视频生成适配器 - 本地ComfyUI集成"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化SVD适配器
        
        Args:
            config: 配置字典，包含ComfyUI服务器信息
        """
        super().__init__(config)
        self.comfyui_url = config.get('comfyui_url', 'http://localhost:8188')
        self.workflow_path = config.get('svd_workflow_path', 'workflows/svd_workflow.json')
        self.max_frames = config.get('max_frames', 25)
        self.motion_bucket_id = config.get('motion_bucket_id', 127)
        self.fps = config.get('fps', 6)
        self.augmentation_level = config.get('augmentation_level', 0)
        
    async def generate_video(self, request: VideoGenerationRequest) -> VideoGenerationResponse:
        """
        生成视频
        
        Args:
            request: 视频生成请求
            
        Returns:
            VideoGenerationResponse: 生成结果
        """
        try:
            logger.info(f"开始SVD视频生成: {request.prompt[:50]}...")
            
            # 检查ComfyUI服务状态
            if not await self._check_comfyui_status():
                raise AdapterError("ComfyUI服务不可用")
            
            # 准备输入图像
            input_image_path = await self._prepare_input_image(request)
            
            # 构建工作流
            workflow = await self._build_svd_workflow(request, input_image_path)
            
            # 提交到ComfyUI队列
            prompt_id = await self._queue_prompt(workflow)
            
            # 等待生成完成
            result = await self._wait_for_completion(prompt_id)
            
            # 处理输出
            video_path = await self._process_output(result, prompt_id)
            
            # 计算成本（本地生成，主要是电力成本）
            estimated_cost = self._calculate_cost(request)
            
            return VideoGenerationResponse(
                video_path=video_path,
                duration=request.duration or 4.0,
                resolution=request.resolution or "512x512",
                fps=self.fps,
                cost=estimated_cost,
                metadata={
                    'adapter': 'svd',
                    'model': 'stable-video-diffusion',
                    'frames': self.max_frames,
                    'motion_bucket_id': self.motion_bucket_id,
                    'prompt_id': prompt_id
                }
            )
            
        except Exception as e:
            logger.error(f"SVD视频生成失败: {str(e)}")
            raise AdapterError(f"SVD视频生成失败: {str(e)}")
    
    async def generate_transition(self, start_image: str, end_image: str, duration: float = 2.0) -> VideoGenerationResponse:
        """
        生成转场视频
        
        Args:
            start_image: 起始图像路径
            end_image: 结束图像路径
            duration: 转场时长
            
        Returns:
            VideoGenerationResponse: 转场视频
        """
        try:
            logger.info("开始生成转场视频")
            
            # 创建转场请求
            request = VideoGenerationRequest(
                prompt="smooth transition between scenes",
                image_path=start_image,
                duration=duration,
                resolution="512x512",
                style="cinematic transition"
            )
            
            # 使用SVD生成转场
            response = await self.generate_video(request)
            
            # 标记为转场视频
            response.metadata['type'] = 'transition'
            response.metadata['start_image'] = start_image
            response.metadata['end_image'] = end_image
            
            return response
            
        except Exception as e:
            logger.error(f"转场视频生成失败: {str(e)}")
            raise AdapterError(f"转场视频生成失败: {str(e)}")
    
    async def _check_comfyui_status(self) -> bool:
        """检查ComfyUI服务状态"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.comfyui_url}/system_stats") as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def _prepare_input_image(self, request: VideoGenerationRequest) -> str:
        """准备输入图像"""
        if request.image_path and Path(request.image_path).exists():
            return request.image_path
        
        # 如果没有输入图像，需要先生成一个
        # 这里可以集成图像生成适配器
        raise AdapterError("SVD需要输入图像，但未提供有效的图像路径")
    
    async def _build_svd_workflow(self, request: VideoGenerationRequest, image_path: str) -> Dict[str, Any]:
        """构建SVD工作流"""
        # 基础SVD工作流模板
        workflow = {
            "3": {
                "inputs": {
                    "seed": request.seed or -1,
                    "steps": 20,
                    "cfg": 2.5,
                    "sampler_name": "euler",
                    "scheduler": "karras",
                    "denoise": 1.0,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "svd_xt.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "5": {
                "inputs": {
                    "width": 1024,
                    "height": 576,
                    "video_frames": self.max_frames,
                    "motion_bucket_id": self.motion_bucket_id,
                    "fps": self.fps,
                    "augmentation_level": self.augmentation_level,
                    "image": ["8", 0]
                },
                "class_type": "SVD_img2vid_Conditioning"
            },
            "6": {
                "inputs": {
                    "text": request.prompt or "high quality video",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "7": {
                "inputs": {
                    "text": "low quality, blurry, distorted",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "8": {
                "inputs": {
                    "image": image_path
                },
                "class_type": "LoadImage"
            },
            "9": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "10": {
                "inputs": {
                    "images": ["9", 0],
                    "fps": self.fps,
                    "loop_count": 0,
                    "filename_prefix": f"svd_output_{request.seed or 'random'}",
                    "format": "video/h264-mp4",
                    "pix_fmt": "yuv420p",
                    "crf": 20
                },
                "class_type": "VHS_VideoCombine"
            }
        }
        
        return workflow
    
    async def _queue_prompt(self, workflow: Dict[str, Any]) -> str:
        """提交工作流到ComfyUI队列"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                payload = {
                    "prompt": workflow,
                    "client_id": "svd_adapter"
                }
                
                async with session.post(f"{self.comfyui_url}/prompt", json=payload) as response:
                    if response.status != 200:
                        raise AdapterError(f"提交工作流失败: {response.status}")
                    
                    result = await response.json()
                    return result["prompt_id"]
                    
        except Exception as e:
            raise AdapterError(f"提交工作流失败: {str(e)}")
    
    async def _wait_for_completion(self, prompt_id: str, timeout: int = 300) -> Dict[str, Any]:
        """等待生成完成"""
        try:
            import aiohttp
            start_time = asyncio.get_event_loop().time()
            
            while True:
                if asyncio.get_event_loop().time() - start_time > timeout:
                    raise AdapterError("生成超时")
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.comfyui_url}/history/{prompt_id}") as response:
                        if response.status == 200:
                            history = await response.json()
                            if prompt_id in history:
                                return history[prompt_id]
                
                await asyncio.sleep(2)
                
        except Exception as e:
            raise AdapterError(f"等待生成完成失败: {str(e)}")
    
    async def _process_output(self, result: Dict[str, Any], prompt_id: str) -> str:
        """处理输出结果"""
        try:
            # 从ComfyUI结果中提取视频文件路径
            outputs = result.get("outputs", {})
            
            # 查找视频输出节点
            for node_id, node_output in outputs.items():
                if "gifs" in node_output:
                    # 视频文件信息
                    video_info = node_output["gifs"][0]
                    filename = video_info["filename"]
                    
                    # ComfyUI输出目录
                    output_dir = Path("ComfyUI/output")
                    video_path = output_dir / filename
                    
                    if video_path.exists():
                        return str(video_path)
            
            raise AdapterError("未找到生成的视频文件")
            
        except Exception as e:
            raise AdapterError(f"处理输出失败: {str(e)}")
    
    def _calculate_cost(self, request: VideoGenerationRequest) -> float:
        """计算生成成本（本地生成，主要是电力成本）"""
        # 基础电力成本估算
        base_cost = 0.01  # 基础成本
        
        # 根据帧数调整
        frame_cost = (self.max_frames / 25) * 0.005
        
        # 根据分辨率调整
        if request.resolution == "1024x576":
            resolution_multiplier = 1.5
        elif request.resolution == "512x512":
            resolution_multiplier = 1.0
        else:
            resolution_multiplier = 1.2
        
        total_cost = (base_cost + frame_cost) * resolution_multiplier
        return round(total_cost, 4)
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的输出格式"""
        return ["mp4", "gif", "webm"]
    
    def get_max_duration(self) -> float:
        """获取最大支持时长"""
        return float(self.max_frames / self.fps)
    
    def get_supported_resolutions(self) -> List[str]:
        """获取支持的分辨率"""
        return ["512x512", "1024x576", "768x768"]
