"""语音合成适配器基础类

定义语音合成的通用接口和功能。
"""

from abc import abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from decimal import Decimal

from adapters.base import APIAdapter, AdapterResult, AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController
from producer.adapters.base import APIError, RateLimitError  # ensure exceptions match tests


class VoiceGender(Enum):
    """语音性别"""
    MALE = "male"
    FEMALE = "female"
    NEUTRAL = "neutral"


class VoiceStyle(Enum):
    """语音风格"""
    NEUTRAL = "neutral"
    CHEERFUL = "cheerful"
    SAD = "sad"
    ANGRY = "angry"
    EXCITED = "excited"
    FRIENDLY = "friendly"
    HOPEFUL = "hopeful"
    SHOUTING = "shouting"
    TERRIFIED = "terrified"
    UNFRIENDLY = "unfriendly"
    WHISPERING = "whispering"


@dataclass
class VoiceSynthesisRequest:
    """语音合成请求"""
    text: str
    voice_id: str = "default"
    language: str = "zh-CN"
    gender: VoiceGender = VoiceGender.FEMALE
    style: VoiceStyle = VoiceStyle.NEUTRAL
    speed: float = 1.0  # 语速倍率 (0.5-2.0)
    pitch: float = 1.0  # 音调倍率 (0.5-2.0)
    volume: float = 1.0  # 音量倍率 (0.0-1.0)
    output_format: str = "mp3"  # 输出格式: mp3, wav, ogg
    sample_rate: int = 24000  # 采样率
    emotion_intensity: float = 1.0  # 情感强度 (0.0-2.0)
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class VoiceSynthesisResponse:
    """语音合成响应"""
    audio_data: str  # base64 字符串，测试期望
    audio_url: Optional[str] = None
    voice_id: str = "default"
    language: str = "zh-CN"
    duration: float = 0.0  # 音频时长(秒)
    sample_rate: int = 24000
    format: str = "mp3"
    generation_time: float = 0.0  # 生成耗时(秒)
    metadata: Optional[Dict[str, Any]] = None
    cost: Decimal = Decimal("0")


class VoiceSynthesisAdapter(APIAdapter):
    """语音合成适配器基础类"""
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        
        # 语音特定配置
        self.default_voice = config.model or "default"
        self.default_language = config.extra_params.get("language", "zh-CN")
        self.max_text_length = config.extra_params.get("max_text_length", 5000)
        self.supported_languages = config.extra_params.get("supported_languages", ["zh-CN", "en-US"])
        # 统计字段（测试会使用）
        self.stats: Dict[str, Any] = {}
    
    async def generate(self, request_data: Union[Dict[str, Any], VoiceSynthesisRequest, Any]) -> VoiceSynthesisResponse:
        """生成语音内容，返回 VoiceSynthesisResponse。发生错误时抛出 APIError/RateLimitError。"""
        try:
            # 解析请求
            if isinstance(request_data, VoiceSynthesisRequest) or (
                hasattr(request_data, "text") and hasattr(request_data, "voice_id")
            ):
                request = request_data
            else:
                request = self._parse_request(request_data)

            # 验证请求
            validation_result = await self._validate_request(request)
            if not validation_result.success:
                raise APIError(validation_result.error_message or "Invalid request")

            # 估算成本（同步，返回 Decimal）
            estimated_cost = self.estimate_cost(request)

            # 检查成本限制（与测试对齐：直接调用一次）
            if hasattr(self.cost_controller, 'check_cost_limit'):
                await self.cost_controller.check_cost_limit(estimated_cost)

            # 执行语音合成（带重试）
            result = await self._execute_with_retry(self._synthesize_voice, request)

            if not result.success or not result.data:
                raise APIError(result.error_message or "Voice synthesis failed")

            response: VoiceSynthesisResponse = result.data

            # 计算并记录实际成本
            actual_cost = Decimal(str(self._calculate_actual_cost(response, request)))
            response.cost = actual_cost

            if hasattr(self.cost_controller, 'record_cost'):
                await self.cost_controller.record_cost(
                    actual_cost
                )

            # 统计
            self.stats["total_requests"] = self.stats.get("total_requests", 0) + 1
            self.stats["total_cost"] = self.stats.get("total_cost", Decimal("0")) + actual_cost
            self.stats["total_duration"] = self.stats.get("total_duration", 0.0) + (response.duration or 0.0)

            return response

        except (RateLimitError, APIError):
            # 直接透传测试期望的异常类型
            raise
        except Exception as e:
            self.logger.error(f"Voice synthesis failed: {e}")
            raise APIError(str(e))
    
    def _parse_request(self, request_data: Dict[str, Any]) -> VoiceSynthesisRequest:
        """解析请求数据"""
        return VoiceSynthesisRequest(
            text=request_data.get("text", ""),
            voice_id=request_data.get("voice_id", self.default_voice),
            language=request_data.get("language", self.default_language),
            gender=VoiceGender(request_data.get("gender", "female")),
            style=VoiceStyle(request_data.get("style", "neutral")),
            speed=float(request_data.get("speed", 1.0)),
            pitch=float(request_data.get("pitch", 1.0)),
            volume=float(request_data.get("volume", 1.0)),
            output_format=request_data.get("output_format", "mp3"),
            sample_rate=int(request_data.get("sample_rate", 24000)),
            emotion_intensity=float(request_data.get("emotion_intensity", 1.0)),
            metadata=request_data.get("metadata")
        )
    
    async def _validate_request(self, request: VoiceSynthesisRequest) -> AdapterResult:
        """验证请求参数"""
        # 检查文本长度
        if not request.text or not request.text.strip():
            return AdapterResult(
                success=False,
                error_message="Text cannot be empty"
            )
        
        if len(request.text) > self.max_text_length:
            return AdapterResult(
                success=False,
                error_message=f"Text too long. Maximum length: {self.max_text_length}"
            )
        
        # 检查语言支持
        if request.language not in self.supported_languages:
            return AdapterResult(
                success=False,
                error_message=f"Unsupported language: {request.language}. Supported: {self.supported_languages}"
            )
        
        # 检查输出格式
        if request.output_format not in self.supported_formats:
            return AdapterResult(
                success=False,
                error_message=f"Unsupported format: {request.output_format}. Supported: {self.supported_formats}"
            )
        
        # 检查参数范围
        if not (0.5 <= request.speed <= 2.0):
            return AdapterResult(
                success=False,
                error_message="Speed must be between 0.5 and 2.0"
            )
        
        if not (0.5 <= request.pitch <= 2.0):
            return AdapterResult(
                success=False,
                error_message="Pitch must be between 0.5 and 2.0"
            )
        
        if not (0.0 <= request.volume <= 1.0):
            return AdapterResult(
                success=False,
                error_message="Volume must be between 0.0 and 1.0"
            )
        
        return AdapterResult(success=True)
    
    @abstractmethod
    async def _synthesize_voice(self, request: VoiceSynthesisRequest) -> AdapterResult:
        """执行语音合成（子类实现）"""
        pass
    
    @abstractmethod
    def _calculate_actual_cost(self, response: VoiceSynthesisResponse, request: VoiceSynthesisRequest) -> float:
        """计算实际成本（子类实现）"""
        pass
    
    def estimate_cost(self, request: VoiceSynthesisRequest) -> Decimal:
        """估算语音合成成本（同步，返回 Decimal，便于测试断言）"""
        text_length = len(request.text)

        if request.language.startswith("zh"):
            estimated_duration = text_length / 5.0
        else:
            word_count = len(request.text.split())
            estimated_duration = word_count / 3.0

        estimated_duration = estimated_duration / max(request.speed, 1e-6)
        base_cost = estimated_duration * 0.001
        return Decimal(str(max(base_cost, 0.001)))
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """获取可用语音列表（子类实现）"""
        return [
            {
                "voice_id": "default",
                "name": "Default Voice",
                "language": "zh-CN",
                "gender": "female",
                "styles": ["neutral"]
            }
        ]
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        return self.supported_languages.copy()
    
    def get_supported_styles(self) -> List[str]:
        """获取支持的语音风格列表"""
        return [style.value for style in VoiceStyle]
    
    async def validate_config(self) -> bool:
        """验证配置"""
        if not await super().validate_config():
            return False
        
        # 检查语音特定配置
        if self.max_text_length <= 0:
            return False
        
        if not self.supported_languages:
            return False
        
        return True
    
    def _estimate_duration(self, text: str, language: str, speed: float = 1.0) -> float:
        """估算音频时长"""
        if language.startswith("zh"):
            # 中文：约5字/秒
            base_duration = len(text) / 5.0
        else:
            # 英文：约3词/秒
            word_count = len(text.split())
            base_duration = word_count / 3.0
        
        # 根据语速调整
        return base_duration / speed
    
    def _validate_audio_format(self, audio_data: bytes, expected_format: str) -> bool:
        """验证音频格式"""
        if not audio_data:
            return False
        
        # 简单的格式检查
        if expected_format == "mp3":
            return audio_data.startswith(b'\xff\xfb') or audio_data.startswith(b'ID3')
        elif expected_format == "wav":
            return audio_data.startswith(b'RIFF') and b'WAVE' in audio_data[:12]
        elif expected_format == "ogg":
            return audio_data.startswith(b'OggS')
        
        return True  # 未知格式，假设有效
    
    def _convert_audio_format(self, audio_data: bytes, from_format: str, to_format: str) -> bytes:
        """转换音频格式（简单实现，实际项目中可能需要使用ffmpeg等工具）"""
        if from_format == to_format:
            return audio_data
        
        # 这里应该实现实际的音频格式转换
        # 为了简化，直接返回原数据
        self.logger.warning(f"Audio format conversion not implemented: {from_format} -> {to_format}")
        return audio_data