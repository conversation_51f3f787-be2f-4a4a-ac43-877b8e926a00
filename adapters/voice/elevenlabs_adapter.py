#!/usr/bin/env python3
"""
ElevenLabs 语音合成适配器
高质量语音合成服务，用于关键角色配音
"""

import asyncio
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List
import json
import os

from adapters.voice.base_voice import VoiceSynthesisAdapter, VoiceSynthesisRequest, VoiceSynthesisResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController

logger = logging.getLogger(__name__)


class ElevenLabsAdapter(VoiceSynthesisAdapter):
    """ElevenLabs语音合成适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化ElevenLabs适配器
        
        Args:
            config: 配置字典，包含API密钥和设置
        """
        super().__init__(config)
        self.api_key = config.get('api_key') or os.getenv('ELEVENLABS_API_KEY')
        self.base_url = config.get('base_url', 'https://api.elevenlabs.io/v1')
        self.default_voice_id = config.get('default_voice_id', 'pNInz6obpgDQGcFmaJgB')  # Adam voice
        self.model_id = config.get('model_id', 'eleven_multilingual_v2')
        self.optimize_streaming_latency = config.get('optimize_streaming_latency', 0)
        self.output_format = config.get('output_format', 'mp3_44100_128')
        
        if not self.api_key:
            raise AdapterError("ElevenLabs API密钥未配置")
    
    async def synthesize_speech(self, request: VoiceSynthesisRequest) -> VoiceSynthesisResponse:
        """
        合成语音
        
        Args:
            request: 语音合成请求
            
        Returns:
            VoiceSynthesisResponse: 合成结果
        """
        try:
            logger.info(f"开始ElevenLabs语音合成: {request.text[:50]}...")
            
            # 选择声音ID
            voice_id = request.voice_id or self.default_voice_id
            
            # 构建请求参数
            payload = {
                "text": request.text,
                "model_id": self.model_id,
                "voice_settings": {
                    "stability": request.stability or 0.5,
                    "similarity_boost": request.similarity_boost or 0.75,
                    "style": request.style or 0.0,
                    "use_speaker_boost": request.use_speaker_boost or True
                }
            }
            
            # 发送API请求
            audio_data = await self._make_api_request(voice_id, payload)
            
            # 保存音频文件
            output_path = await self._save_audio_file(audio_data, request)
            
            # 计算成本
            cost = self._calculate_cost(request.text)
            
            # 获取音频信息
            duration = await self._get_audio_duration(output_path)
            
            return VoiceSynthesisResponse(
                audio_path=output_path,
                duration=duration,
                sample_rate=44100,
                format="mp3",
                cost=cost,
                metadata={
                    'adapter': 'elevenlabs',
                    'voice_id': voice_id,
                    'model_id': self.model_id,
                    'character_count': len(request.text),
                    'language': request.language or 'auto-detect'
                }
            )
            
        except Exception as e:
            logger.error(f"ElevenLabs语音合成失败: {str(e)}")
            raise AdapterError(f"ElevenLabs语音合成失败: {str(e)}")
    
    async def clone_voice(self, voice_samples: List[str], voice_name: str, description: str = "") -> str:
        """
        克隆声音
        
        Args:
            voice_samples: 声音样本文件路径列表
            voice_name: 声音名称
            description: 声音描述
            
        Returns:
            str: 新创建的声音ID
        """
        try:
            logger.info(f"开始克隆声音: {voice_name}")
            
            # 准备文件上传
            files = []
            for i, sample_path in enumerate(voice_samples):
                if not Path(sample_path).exists():
                    raise AdapterError(f"声音样本文件不存在: {sample_path}")
                
                files.append(('files', (f'sample_{i}.wav', open(sample_path, 'rb'), 'audio/wav')))
            
            # 构建请求数据
            data = {
                'name': voice_name,
                'description': description,
                'labels': json.dumps({"accent": "chinese", "age": "middle_aged", "gender": "male"})
            }
            
            # 发送克隆请求
            voice_id = await self._clone_voice_request(files, data)
            
            # 关闭文件
            for _, (_, file_obj, _) in files:
                file_obj.close()
            
            logger.info(f"声音克隆成功，Voice ID: {voice_id}")
            return voice_id
            
        except Exception as e:
            logger.error(f"声音克隆失败: {str(e)}")
            raise AdapterError(f"声音克隆失败: {str(e)}")
    
    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """
        获取可用的声音列表
        
        Returns:
            List[Dict]: 声音信息列表
        """
        try:
            import aiohttp
            
            headers = {
                'xi-api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/voices", headers=headers) as response:
                    if response.status != 200:
                        raise AdapterError(f"获取声音列表失败: {response.status}")
                    
                    result = await response.json()
                    return result.get('voices', [])
                    
        except Exception as e:
            logger.error(f"获取声音列表失败: {str(e)}")
            raise AdapterError(f"获取声音列表失败: {str(e)}")
    
    async def _make_api_request(self, voice_id: str, payload: Dict[str, Any]) -> bytes:
        """发送API请求"""
        try:
            import aiohttp
            
            headers = {
                'xi-api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            # 添加优化参数
            params = {}
            if self.optimize_streaming_latency > 0:
                params['optimize_streaming_latency'] = str(self.optimize_streaming_latency)
            if self.output_format:
                params['output_format'] = self.output_format
            
            url = f"{self.base_url}/text-to-speech/{voice_id}"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers, params=params) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise AdapterError(f"API请求失败 ({response.status}): {error_text}")
                    
                    return await response.read()
                    
        except Exception as e:
            if isinstance(e, AdapterError):
                raise
            raise AdapterError(f"API请求失败: {str(e)}")
    
    async def _clone_voice_request(self, files: List, data: Dict[str, Any]) -> str:
        """发送声音克隆请求"""
        try:
            import aiohttp
            
            headers = {
                'xi-api-key': self.api_key
            }
            
            form_data = aiohttp.FormData()
            for key, value in data.items():
                form_data.add_field(key, value)
            
            for field_name, (filename, file_obj, content_type) in files:
                form_data.add_field(field_name, file_obj, filename=filename, content_type=content_type)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/voices/add", data=form_data, headers=headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise AdapterError(f"声音克隆请求失败 ({response.status}): {error_text}")
                    
                    result = await response.json()
                    return result['voice_id']
                    
        except Exception as e:
            if isinstance(e, AdapterError):
                raise
            raise AdapterError(f"声音克隆请求失败: {str(e)}")
    
    async def _save_audio_file(self, audio_data: bytes, request: VoiceSynthesisRequest) -> str:
        """保存音频文件"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3', prefix='elevenlabs_') as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            # 如果指定了输出路径，移动文件
            if request.output_path:
                output_path = Path(request.output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                import shutil
                shutil.move(temp_path, output_path)
                return str(output_path)
            
            return temp_path
            
        except Exception as e:
            raise AdapterError(f"保存音频文件失败: {str(e)}")
    
    async def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            # 使用pydub获取音频时长
            from pydub import AudioSegment
            audio = AudioSegment.from_mp3(audio_path)
            return len(audio) / 1000.0  # 转换为秒
            
        except ImportError:
            # 如果pydub不可用，返回估算值
            logger.warning("pydub不可用，使用估算音频时长")
            # 粗略估算：每个字符约0.1秒
            return len(self.last_text) * 0.1 if hasattr(self, 'last_text') else 1.0
        except Exception as e:
            logger.warning(f"获取音频时长失败: {str(e)}")
            return 1.0
    
    def _calculate_cost(self, text: str) -> float:
        """
        计算生成成本
        
        ElevenLabs按字符收费：
        - 免费额度：10,000字符/月
        - Starter: $5/月，30,000字符
        - Creator: $22/月，100,000字符
        - Pro: $99/月，500,000字符
        """
        character_count = len(text)
        
        # 按Creator计划计算：$22/100,000字符 = $0.00022/字符
        cost_per_character = 0.00022
        
        total_cost = character_count * cost_per_character
        return round(total_cost, 4)
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言"""
        return [
            'zh', 'zh-cn', 'zh-tw',  # 中文
            'en', 'en-us', 'en-gb',  # 英语
            'ja', 'ko',              # 日语、韩语
            'es', 'fr', 'de',        # 西班牙语、法语、德语
            'it', 'pt', 'ru',        # 意大利语、葡萄牙语、俄语
            'ar', 'hi', 'tr'         # 阿拉伯语、印地语、土耳其语
        ]
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的输出格式"""
        return [
            'mp3_44100_128',
            'mp3_44100_192',
            'pcm_16000',
            'pcm_22050',
            'pcm_24000',
            'pcm_44100',
            'ulaw_8000'
        ]
    
    def get_premium_voices(self) -> List[Dict[str, str]]:
        """获取推荐的高质量声音"""
        return [
            {
                'voice_id': 'pNInz6obpgDQGcFmaJgB',
                'name': 'Adam',
                'gender': 'male',
                'age': 'middle_aged',
                'accent': 'american',
                'description': '深沉、权威的男声，适合旁白'
            },
            {
                'voice_id': 'EXAVITQu4vr4xnSDxMaL',
                'name': 'Bella',
                'gender': 'female',
                'age': 'young',
                'accent': 'american',
                'description': '清晰、友好的女声，适合对话'
            },
            {
                'voice_id': 'ErXwobaYiN019PkySvjV',
                'name': 'Antoni',
                'gender': 'male',
                'age': 'young',
                'accent': 'american',
                'description': '年轻、活力的男声，适合角色配音'
            },
            {
                'voice_id': 'VR6AewLTigWG4xSOukaG',
                'name': 'Arnold',
                'gender': 'male',
                'age': 'middle_aged',
                'accent': 'american',
                'description': '成熟、稳重的男声，适合历史人物'
            }
        ]
