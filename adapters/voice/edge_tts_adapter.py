"""EdgeTTS语音合成适配器

通过Microsoft Edge TTS实现免费语音合成功能。
"""

from typing import Dict, Any, List
import logging
import base64
from decimal import Decimal

try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    logging.warning("edge-tts not available. Install with: pip install edge-tts")

from producer.adapters.voice.base_voice import VoiceSynthesisAdapter, VoiceSynthesisRequest, VoiceSynthesisResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController


class EdgeTTSAdapter(VoiceSynthesisAdapter):
    """EdgeTTS语音合成适配器"""
    
    def _create_client(self):
        """创建客户端（EdgeTTS不需要客户端）"""
        return None
    
    # 支持的语音列表（部分常用语音）
    SUPPORTED_VOICES = {
        # 中文语音
        "zh-CN-XiaoxiaoNeural": {
            "name": "晓晓",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "cheerful", "sad", "angry", "fearful", "disgruntled", "serious", "affectionate", "gentle", "embarrassed", "calm", "fearful"]
        },
        "zh-CN-YunxiNeural": {
            "name": "云希",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "cheerful", "sad", "angry", "fearful", "disgruntled", "serious", "depressed", "embarrassed", "calm", "fearful"]
        },
        "zh-CN-YunyangNeural": {
            "name": "云扬",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "customerservice"]
        },
        "zh-CN-XiaoyiNeural": {
            "name": "晓伊",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "affectionate", "angry", "cheerful", "disgruntled", "embarrassed", "fearful", "gentle", "sad", "serious"]
        },
        "zh-CN-YunjianNeural": {
            "name": "云健",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "sports_commentary", "sports_commentary_excited"]
        },
        "zh-CN-XiaochenNeural": {
            "name": "晓辰",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral"]
        },
        "zh-CN-XiaohanNeural": {
            "name": "晓涵",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "gentle", "affectionate", "embarrassed"]
        },
        "zh-CN-XiaomengNeural": {
            "name": "晓梦",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "chat"]
        },
        "zh-CN-XiaomoNeural": {
            "name": "晓墨",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "envious"]
        },
        "zh-CN-XiaoqiuNeural": {
            "name": "晓秋",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral"]
        },
        "zh-CN-XiaoruiNeural": {
            "name": "晓睿",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "calm", "fearful", "angry", "sad"]
        },
        "zh-CN-XiaoshuangNeural": {
            "name": "晓双",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "chat"]
        },
        "zh-CN-XiaoxuanNeural": {
            "name": "晓萱",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "gentle", "depressed"]
        },
        "zh-CN-XiaoyanNeural": {
            "name": "晓颜",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral"]
        },
        "zh-CN-XiaoyouNeural": {
            "name": "晓悠",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral"]
        },
        "zh-CN-XiaozhenNeural": {
            "name": "晓甄",
            "language": "zh-CN",
            "gender": "female",
            "styles": ["neutral", "angry", "cheerful", "disgruntled", "fearful", "sad", "serious"]
        },
        "zh-CN-YunfengNeural": {
            "name": "云枫",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "angry", "cheerful", "disgruntled", "fearful", "sad", "serious", "depressed"]
        },
        "zh-CN-YunhaoNeural": {
            "name": "云皓",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "advertisement_upbeat"]
        },
        "zh-CN-YunxiaNeural": {
            "name": "云夏",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "embarrassed"]
        },
        "zh-CN-YunyeNeural": {
            "name": "云野",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad"]
        },
        "zh-CN-YunzeNeural": {
            "name": "云泽",
            "language": "zh-CN",
            "gender": "male",
            "styles": ["neutral", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "embarrassed"]
        },
        
        # 英文语音
        "en-US-AriaNeural": {
            "name": "Aria",
            "language": "en-US",
            "gender": "female",
            "styles": ["neutral", "cheerful", "empathetic", "newscast", "angry", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]
        },
        "en-US-JennyNeural": {
            "name": "Jenny",
            "language": "en-US",
            "gender": "female",
            "styles": ["neutral", "assistant", "chat", "customerservice", "newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"]
        },
        "en-US-GuyNeural": {
            "name": "Guy",
            "language": "en-US",
            "gender": "male",
            "styles": ["neutral", "newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"]
        },
        "en-US-DavisNeural": {
            "name": "Davis",
            "language": "en-US",
            "gender": "male",
            "styles": ["neutral", "chat", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]
        },
        "en-US-JaneNeural": {
            "name": "Jane",
            "language": "en-US",
            "gender": "female",
            "styles": ["neutral", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]
        },
        "en-US-JasonNeural": {
            "name": "Jason",
            "language": "en-US",
            "gender": "male",
            "styles": ["neutral", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]
        },
        "en-US-NancyNeural": {
            "name": "Nancy",
            "language": "en-US",
            "gender": "female",
            "styles": ["neutral", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]
        },
        "en-US-TonyNeural": {
            "name": "Tony",
            "language": "en-US",
            "gender": "male",
            "styles": ["neutral", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]
        }
    }
    
    @property
    def media_type(self) -> str:
        """媒体类型"""
        return "voice"
    
    @property
    def supported_formats(self) -> List[str]:
        """支持的格式"""
        return ["wav", "mp3"]
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        
        # EdgeTTS不需要API密钥，是免费服务
        self.api_key = None
        
        # 验证语音模型
        if self.default_voice not in self.SUPPORTED_VOICES:
            self.logger.warning(f"Unknown voice: {self.default_voice}, using default")
            self.default_voice = "zh-CN-XiaoxiaoNeural"
        
        # 设置支持的语言
        self.supported_languages = ["zh-CN", "en-US"]
        self.max_text_length = 10000  # EdgeTTS支持较长文本
        
        if not EDGE_TTS_AVAILABLE:
            self.logger.warning("edge-tts not available. Install with: pip install edge-tts")
    
    async def _synthesize_voice(self, request: VoiceSynthesisRequest) -> AdapterResult:
        """执行EdgeTTS语音合成"""
        if not EDGE_TTS_AVAILABLE:
            return AdapterResult(
                success=False,
                error_message="edge-tts library not available. Install with: pip install edge-tts"
            )
        
        try:
            # 构建SSML
            ssml = self._build_ssml(request)
            
            # 创建TTS通信对象
            communicate = edge_tts.Communicate(ssml, request.voice_id)
            
            # 生成音频，兼容异步生成器与测试中的await后列表
            audio_bytes = b""
            stream_obj = communicate.stream()
            try:
                # 如果是异步可迭代
                __aiter__ = getattr(stream_obj, "__aiter__", None)
                if callable(__aiter__):
                    async for chunk in stream_obj:
                        if chunk.get("type") == "audio":
                            audio_bytes += chunk.get("data", b"")
                else:
                    # 可能是协程，await得到列表
                    from asyncio import iscoroutine
                    chunks = await stream_obj if iscoroutine(stream_obj) else stream_obj
                    for chunk in chunks or []:
                        if chunk.get("type") == "audio":
                            audio_bytes += chunk.get("data", b"")
            except TypeError:
                # 兜底：如果直接可迭代
                for chunk in (stream_obj or []):
                    if isinstance(chunk, dict) and chunk.get("type") == "audio":
                        audio_bytes += chunk.get("data", b"")
            
            if not audio_bytes:
                return AdapterResult(
                    success=False,
                    error_message="No audio data generated"
                )
            
            # 估算音频时长
            duration = self._estimate_duration(request.text, request.language, request.speed)
            
            # 创建响应对象
            response_data = VoiceSynthesisResponse(
                audio_data=base64.b64encode(audio_bytes).decode(),
                audio_url=None,  # EdgeTTS不提供URL
                voice_id=request.voice_id,
                language=request.language,
                duration=duration,
                sample_rate=request.sample_rate,
                format=request.output_format,
                generation_time=0,  # EdgeTTS生成很快
                metadata={
                    "ssml": ssml,
                    "file_size": len(audio_bytes),
                    "voice_name": self.SUPPORTED_VOICES.get(request.voice_id, {}).get("name", "Unknown")
                }
            )
            
            return AdapterResult(
                success=True,
                data=response_data
            )
            
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"EdgeTTS synthesis failed: {str(e)}"
            )
    
    def _build_ssml(self, request: VoiceSynthesisRequest) -> str:
        """构建SSML（Speech Synthesis Markup Language）"""
        # 映射风格
        style_mapping = {
            "neutral": "neutral",
            "cheerful": "cheerful",
            "sad": "sad",
            "angry": "angry",
            "excited": "excited",
            "friendly": "friendly",
            "hopeful": "hopeful",
            "shouting": "shouting",
            "terrified": "terrified",
            "unfriendly": "unfriendly",
            "whispering": "whispering"
        }
        
        voice_info = self.SUPPORTED_VOICES.get(request.voice_id, {})
        supported_styles = voice_info.get("styles", ["neutral"])
        
        # 选择支持的风格
        style = style_mapping.get(request.style.value, "neutral")
        if style not in supported_styles:
            style = "neutral"
        
        # 构建SSML
        ssml = (
            f'<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" '
            f'xmlns:mstts="https://www.w3.org/2001/mstts" xml:lang="{request.language}">'
        )
        ssml += f'<voice name="{request.voice_id}">'
        
        # 添加风格（如果支持）
        if style != "neutral" and style in supported_styles:
            ssml += f'<mstts:express-as style="{style}">'
        
        # 添加韵律控制
        ssml += (
            f'<prosody rate="{self._convert_speed(request.speed)}" '
            f'pitch="{self._convert_pitch(request.pitch)}" '
            f'volume="{self._convert_volume(request.volume)}">'
        )
        
        # 添加文本内容
        ssml += self._escape_xml(request.text)
        
        # 关闭标签
        ssml += '</prosody>'
        
        if style != "neutral" and style in supported_styles:
            ssml += '</mstts:express-as>'
        
        ssml += '</voice>'
        ssml += '</speak>'
        
        return ssml
    
    def _convert_speed(self, speed: float) -> str:
        """转换语速为SSML格式（测试期望为数值字符串，如 1.2 或 0.8）"""
        return f"{speed}"
    
    def _convert_pitch(self, pitch: float) -> str:
        """转换音调为百分比（1.0 为 0%，>1 正百分比，<1 负百分比）"""
        percent = int(round((pitch - 1.0) * 100))
        sign = "+" if percent >= 0 else ""
        return f"{sign}{percent}%"
    
    def _convert_volume(self, volume: float) -> str:
        """转换音量为百分比（1.0 为 100%）"""
        return f"{int(round(volume * 100))}%"
    
    def _escape_xml(self, text: str) -> str:
        """转义XML特殊字符"""
        return (text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace('"', "&quot;")
                   .replace("'", "&apos;"))
    
    def _calculate_actual_cost(self, response: VoiceSynthesisResponse, request: VoiceSynthesisRequest) -> float:
        """计算实际成本（EdgeTTS免费）"""
        return 0.0  # EdgeTTS是免费服务
    
    def estimate_cost(self, request: VoiceSynthesisRequest) -> Decimal:
        """估算语音合成成本（EdgeTTS免费）"""
        return Decimal("0")
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """获取可用语音列表"""
        voices = []
        for voice_id, voice_info in self.SUPPORTED_VOICES.items():
            voices.append({
                "id": voice_id,
                "name": voice_info["name"],
                "language": voice_info["language"],
                "gender": voice_info["gender"],
                "styles": voice_info["styles"],
                "cost_per_char": 0.0  # 免费
            })
        return voices
    
    async def validate_config(self) -> bool:
        """验证配置"""
        # EdgeTTS不需要API密钥；测试环境允许缺少库，通过 Mock 使用
        return True

    def get_supported_models(self) -> List[str]:
        """返回支持的模型标识"""
        return ["edge-tts"]
    
    async def get_all_voices(self) -> List[Dict[str, Any]]:
        """获取所有可用语音（从EdgeTTS服务获取最新列表）"""
        if not EDGE_TTS_AVAILABLE:
            return self.get_available_voices()
        
        try:
            voices = await edge_tts.list_voices()
            result = []
            
            for voice in voices:
                # 只返回中文和英文语音
                if voice["Locale"] in ["zh-CN", "en-US"]:
                    result.append({
                        "voice_id": voice["ShortName"],
                        "name": voice["FriendlyName"],
                        "language": voice["Locale"],
                        "gender": voice["Gender"].lower(),
                        "styles": voice.get("StyleList", ["neutral"]),
                        "cost_per_char": 0.0
                    })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get voice list from EdgeTTS: {e}")
            return self.get_available_voices()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        pass  # EdgeTTS不需要清理资源