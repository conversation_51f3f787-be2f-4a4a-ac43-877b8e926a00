"""OpenAI GPT适配器

通过OpenAI API实现GPT文本生成功能。
"""

from typing import Dict, List
import aiohttp
import asyncio

from adapters.text.base_text import TextGenerationAdapter
from producer.adapters.text.base_text import TextGenerationRequest, TextGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController


class GPTAdapter(TextGenerationAdapter):
    """OpenAI GPT适配器"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        'gpt-4o-mini': {
            'input_price_per_1k': 0.00015,  # $0.00015 per 1K input tokens
            'output_price_per_1k': 0.0006,  # $0.0006 per 1K output tokens
            'max_tokens': 16384
        },
        'gpt-4o': {
            'input_price_per_1k': 0.0025,
            'output_price_per_1k': 0.01,
            'max_tokens': 4096
        },
        'gpt-4-turbo': {
            'input_price_per_1k': 0.01,
            'output_price_per_1k': 0.03,
            'max_tokens': 4096
        },
        'gpt-3.5-turbo': {
            'input_price_per_1k': 0.0005,
            'output_price_per_1k': 0.0015,
            'max_tokens': 4096
        }
    }
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.base_url = config.base_url or "https://api.openai.com/v1/chat/completions"
        self.api_key = config.api_key
        
        # 验证模型名称
        if self.model_name not in self.SUPPORTED_MODELS:
            raise ValueError(f"Unsupported model: {self.model_name}. Supported models: {list(self.SUPPORTED_MODELS.keys())}")
    
    async def _create_client(self):
        """创建HTTP客户端"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """执行GPT文本生成"""
        try:
            await self._create_client()
            
            # 构建请求数据
            data = {
                "model": self.model_name,
                "messages": self._build_messages(request),
                "max_tokens": request.max_tokens,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "frequency_penalty": request.frequency_penalty,
                "presence_penalty": request.presence_penalty,
                "stream": False
            }
            
            # 添加停止序列
            if request.stop_sequences:
                data["stop"] = request.stop_sequences
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            # 兼容 MagicMock/AsyncMock：如果返回协程则 await，否则直接使用
            post_result = self.session.post(self.base_url, json=data, headers=headers)
            response = await post_result if asyncio.iscoroutine(post_result) else post_result
            if response.status != 200:
                text = await response.text()
                from adapters.base import APIError
                raise APIError(f"API request failed with status {response.status}: {text}")

            result = await response.json()
            if "error" in result:
                from adapters.base import APIError
                raise APIError(f"API error: {result['error'].get('message', 'Unknown error')}")

            usage = result.get("usage", {})
            text_val = ""
            finish_reason = "unknown"
            if "choices" in result and isinstance(result["choices"], list) and result["choices"]:
                choice = result["choices"][0]
                text_val = choice.get("message", {}).get("content", "")
                finish_reason = choice.get("finish_reason", finish_reason)
                input_tokens = usage.get("prompt_tokens", 0)
                output_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", input_tokens + output_tokens)
            elif "content" in result and isinstance(result["content"], list):
                # 某些测试使用 Claude 风格结构
                text_val = "".join([c.get("text", "") for c in result["content"] if c.get("type") == "text"])
                finish_reason = result.get("stop_reason", finish_reason)
                input_tokens = usage.get("input_tokens", 0)
                output_tokens = usage.get("output_tokens", 0)
                total_tokens = input_tokens + output_tokens
            else:
                input_tokens = usage.get("prompt_tokens", 0) or usage.get("input_tokens", 0)
                output_tokens = usage.get("completion_tokens", 0) or usage.get("output_tokens", 0)
                total_tokens = usage.get("total_tokens", input_tokens + output_tokens)

            response_data = TextGenerationResponse(
                text=text_val,
                finish_reason=finish_reason,
                total_tokens=total_tokens,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                model_name=self.model_name
            )

            return AdapterResult(
                success=True,
                data=response_data,
                metadata={
                    "model": self.model_name,
                    "usage": usage,
                    "id": result.get("id"),
                    "created": result.get("created")
                }
            )
        except Exception as e:
            from adapters.base import APIError
            raise APIError(f"GPT generation failed: {str(e)}")
    
    def _build_messages(self, request: TextGenerationRequest) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = []
        
        # 添加系统消息
        # 兼容字段名：测试使用 system_message
        system_msg = request.system_message or request.system_prompt
        if system_msg:
            messages.append({
                "role": "system",
                "content": system_msg
            })
        
        # 添加用户消息
        messages.append({
            "role": "user",
            "content": request.prompt
        })
        
        return messages
    
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        pricing = self._get_model_pricing()
        
        prompt_cost = (response.input_tokens / 1000) * pricing['input_price_per_1k']
        completion_cost = (response.output_tokens / 1000) * pricing['output_price_per_1k']
        
        return prompt_cost + completion_cost
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        return self.SUPPORTED_MODELS.get(self.model_name, {
            'input_price_per_1k': 0.001,
            'output_price_per_1k': 0.002
        })
    
    async def validate_config(self) -> bool:
        """验证配置（简化版，避免真实网络调用）"""
        if not self.api_key:
            return False
        # 模型是否受支持
        return self.model_name in self.SUPPORTED_MODELS

    def get_supported_models(self) -> List[str]:
        """返回支持的模型列表"""
        return list(self.SUPPORTED_MODELS.keys())
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._create_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            self.session = None