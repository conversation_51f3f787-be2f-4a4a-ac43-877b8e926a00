"""DeepSeek适配器

通过DeepSeek API实现文本生成功能。
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Union
from decimal import Decimal
import aiohttp

from adapters.text.base_text import TextGenerationAdapter, TextGenerationRequest, TextGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController


class DeepSeekAdapter(TextGenerationAdapter):
    """DeepSeek适配器"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        'deepseek-chat': {
            'input_price_per_1k': 0.0014,  # $0.14/1M tokens (约¥1.0)
            'output_price_per_1k': 0.0028,  # $0.28/1M tokens (约¥2.0)
            'max_tokens': 8192
        },
        'deepseek-coder': {
            'input_price_per_1k': 0.0014,
            'output_price_per_1k': 0.0028,
            'max_tokens': 8192
        }
    }
    
    def __init__(self, config: <PERSON><PERSON>er<PERSON>onfi<PERSON>, config_manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.base_url = config.base_url or "https://api.deepseek.com/chat/completions"
        self.api_key = config.api_key
        
        # 验证模型名称
        if self.model_name not in self.SUPPORTED_MODELS:
            available_models = list(self.SUPPORTED_MODELS.keys())
            raise ValueError(f"Unsupported DeepSeek model: {self.model_name}. Available models: {available_models}")
    
    async def _create_client(self):
        """创建HTTP客户端"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """生成文本"""
        try:
            # 构建请求数据
            await self._create_client()
            
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": request.system_prompt or "你是一个专业的编剧助手。"},
                    {"role": "user", "content": request.prompt}
                ],
                "max_tokens": request.max_tokens or self.SUPPORTED_MODELS[self.model_name]['max_tokens'],
                "temperature": request.temperature or 0.7,
                "stream": False
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            async with self.session.post(
                self.base_url,
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 解析响应
                    if 'choices' in data and len(data['choices']) > 0:
                        content = data['choices'][0]['message']['content']
                        
                        # 计算成本
                        usage = data.get('usage', {})
                        input_tokens = usage.get('prompt_tokens', 0)
                        output_tokens = usage.get('completion_tokens', 0)
                        
                        model_config = self.SUPPORTED_MODELS[self.model_name]
                        cost = (
                            input_tokens * model_config['input_price_per_1k'] / 1000 +
                            output_tokens * model_config['output_price_per_1k'] / 1000
                        )
                        
                        # 创建响应对象
                        text_response = TextGenerationResponse(
                            text=content,
                            finish_reason="stop",
                            total_tokens=input_tokens + output_tokens,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            model_name=self.model_name
                        )
                        
                        return AdapterResult(
                            success=True,
                            data=text_response,
                            cost_usd=cost,
                            metadata={
                                "model": self.model_name,
                                "input_tokens": input_tokens,
                                "output_tokens": output_tokens
                            }
                        )
                    else:
                        return AdapterResult(
                            success=False,
                            error_message="No content in DeepSeek response"
                        )
                else:
                    error_text = await response.text()
                    return AdapterResult(
                        success=False,
                        error_message=f"DeepSeek API error {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"DeepSeek adapter error: {str(e)}"
            )
    
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        model_config = self.SUPPORTED_MODELS.get(self.model_name, {})
        input_cost = response.input_tokens * model_config.get('input_price_per_1k', 0) / 1000
        output_cost = response.output_tokens * model_config.get('output_price_per_1k', 0) / 1000
        return input_cost + output_cost
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        if self.model_name in self.SUPPORTED_MODELS:
            return self.SUPPORTED_MODELS[self.model_name]
        return {
            'input_price_per_1k': 0.0,
            'output_price_per_1k': 0.0
        }
    
    def estimate_cost(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> Decimal:
        """估算生成成本"""
        if isinstance(prompt, TextGenerationRequest):
            text = prompt.prompt
            completion_tokens = prompt.max_tokens or self.default_max_tokens
        else:
            text = prompt
            completion_tokens = kwargs.get('max_tokens', self.default_max_tokens)
        
        # 确保text是字符串
        if not isinstance(text, str):
            text = str(text) if text is not None else ''
        
        # 估算token数量
        prompt_tokens = self._count_tokens(text)
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 计算成本（使用Decimal）
        prompt_cost = Decimal(prompt_tokens) / Decimal(1000) * Decimal(str(pricing.get('input_price_per_1k', 0.0)))
        completion_cost = Decimal(completion_tokens) / Decimal(1000) * Decimal(str(pricing.get('output_price_per_1k', 0.0)))
        
        return prompt_cost + completion_cost
    
    def _count_tokens(self, text: str) -> int:
        """估算token数量（简单实现）"""
        # 确保text是字符串
        if not isinstance(text, str):
            text = str(text) if text is not None else ''
            
        if not text:
            return 0
        # 这是一个简化的token计数，实际应该使用对应模型的tokenizer
        return int(len(text.split()) * 1.3)
    
    async def validate_config(self) -> bool:
        """验证配置"""
        if not self.api_key:
            return False
            
        try:
            # 发送测试请求
            test_request = TextGenerationRequest(
                prompt="测试",
                max_tokens=10
            )
            response = await self.generate(test_request)
            return response.success if hasattr(response, 'success') else True
        except:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model_name in self.SUPPORTED_MODELS:
            return self.SUPPORTED_MODELS[self.model_name]
        return {}
