"""链式文本适配器

将原来的链式调用封装为适配器接口，既保持统一接口又保留链式调用的优势。
"""

import json
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from decimal import Decimal

from adapters.text.base_text import TextGenerationAdapter, TextGenerationRequest, TextGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController, ServiceType


@dataclass
class ChainType:
    """链路类型枚举"""
    OUTLINE = "outline"
    SCENE = "scene"
    DIALOGUE = "dialogue"


class ChainTextAdapter(TextGenerationAdapter):
    """链式文本适配器
    
    将原来的链式调用封装为适配器接口，内部使用专门的链路处理不同任务，
    但对外提供统一的generate接口。
    """
    
    def __init__(self, config: AdapterConfig, config_manager: Config<PERSON>anager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化原来的链路
        self._initialize_chains()
    
    async def _create_client(self):
        """创建HTTP客户端"""
        # 链式适配器不需要创建HTTP客户端，因为它是通过其他链路工作的
        pass
    
    def _initialize_chains(self):
        """初始化各个链路"""
        try:
            # 导入原来的链路
            from core.chains.outline_chain import OutlineChain
            from core.chains.scene_chain import SceneChain
            from core.chains.dialogue_chain import DialogueChain
            
            # 初始化链路
            self.outline_chain = OutlineChain(self.config_manager, self.cost_controller)
            self.scene_chain = SceneChain(self.config_manager, self.cost_controller)
            self.dialogue_chain = DialogueChain(self.config_manager, self.cost_controller)
            
            self.logger.info("Successfully initialized all chains")
        except ImportError as e:
            self.logger.error(f"Failed to import chains: {e}")
            raise
    
    async def generate_outline(self, request: Dict[str, Any]) -> TextGenerationResponse:
        """生成大纲"""
        try:
            # 使用原来的outline_chain
            result = await self.outline_chain.generate_outline(
                title=request.get("title", ""),
                theme=request.get("theme", ""),
                era=request.get("era", "现代"),
                character_count=request.get("character_count", 3),
                scene_count=request.get("scene_count", 5)
            )
            
            # 转换为TextGenerationResponse
            return self._convert_to_response(result, ChainType.OUTLINE)
        except Exception as e:
            self.logger.error(f"Outline generation failed: {e}")
            raise
    
    async def generate_scene_details(self, request: Dict[str, Any]) -> TextGenerationResponse:
        """生成场景详情"""
        try:
            # 使用原来的scene_chain
            result = await self.scene_chain.generate_scene_details(
                scene_data=request.get("scene_data", {}),
                script_context=request.get("script_context", {})
            )
            
            # 转换为TextGenerationResponse
            return self._convert_to_response(result, ChainType.SCENE)
        except Exception as e:
            self.logger.error(f"Scene generation failed: {e}")
            raise
    
    async def generate_dialogue(self, request: Dict[str, Any]) -> TextGenerationResponse:
        """生成对话"""
        try:
            # 使用原来的dialogue_chain
            result = await self.dialogue_chain.generate_dialogue(
                scene_data=request.get("scene_data", {}),
                character_data=request.get("character_data", {}),
                dialogue_style=request.get("dialogue_style", {})
            )
            
            # 转换为TextGenerationResponse
            return self._convert_to_response(result, ChainType.DIALOGUE)
        except Exception as e:
            self.logger.error(f"Dialogue generation failed: {e}")
            raise
    
    async def generate(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> TextGenerationResponse:
        """统一的生成接口
        
        根据prompt或请求参数中的chain_type字段决定使用哪个链路
        """
        # 构建请求
        if isinstance(prompt, TextGenerationRequest):
            request = self._parse_request_from_prompt(prompt)
        else:
            request = self._parse_request_from_text(prompt, kwargs)
        
        # 根据chain_type调用相应的链路
        chain_type = request.get("chain_type", ChainType.OUTLINE)
        
        if chain_type == ChainType.OUTLINE:
            return await self.generate_outline(request)
        elif chain_type == ChainType.SCENE:
            return await self.generate_scene_details(request)
        elif chain_type == ChainType.DIALOGUE:
            return await self.generate_dialogue(request)
        else:
            raise ValueError(f"Unknown chain type: {chain_type}")
    
    def _parse_request_from_prompt(self, prompt: TextGenerationRequest) -> Dict[str, Any]:
        """从TextGenerationRequest解析请求参数"""
        # 尝试从prompt中解析JSON
        try:
            return json.loads(prompt.prompt)
        except json.JSONDecodeError:
            # 如果不是JSON，尝试从system_prompt中获取chain_type
            chain_type = prompt.system_prompt or ChainType.OUTLINE
            return {
                "chain_type": chain_type,
                "prompt": prompt.prompt,
                "max_tokens": prompt.max_tokens,
                "temperature": prompt.temperature
            }
    
    def _parse_request_from_text(self, prompt: str, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """从文本和kwargs解析请求参数"""
        # 尝试解析JSON
        try:
            return json.loads(prompt)
        except json.JSONDecodeError:
            # 如果不是JSON，从kwargs中获取参数
            chain_type = kwargs.get("chain_type", ChainType.OUTLINE)
            return {
                "chain_type": chain_type,
                "prompt": prompt,
                **kwargs
            }
    
    def _convert_to_response(self, result: Any, chain_type: str) -> TextGenerationResponse:
        """将链路结果转换为TextGenerationResponse"""
        # 根据不同的链路类型处理结果
        if chain_type == ChainType.OUTLINE:
            # 处理大纲结果
            if hasattr(result, 'dict'):
                result_dict = result.dict()
            else:
                result_dict = result
            
            text = json.dumps(result_dict, ensure_ascii=False, indent=2)
            
        elif chain_type == ChainType.SCENE:
            # 处理场景结果
            if hasattr(result, 'dict'):
                result_dict = result.dict()
            else:
                result_dict = result
            
            text = json.dumps(result_dict, ensure_ascii=False, indent=2)
            
        elif chain_type == ChainType.DIALOGUE:
            # 处理对话结果
            if hasattr(result, 'dict'):
                result_dict = result.dict()
            else:
                result_dict = result
            
            text = json.dumps(result_dict, ensure_ascii=False, indent=2)
        
        else:
            text = str(result)
        
        # 估算token数量
        input_tokens = self._count_tokens(text) // 2  # 粗略估算
        output_tokens = self._count_tokens(text)
        
        return TextGenerationResponse(
            text=text,
            finish_reason="stop",
            total_tokens=input_tokens + output_tokens,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            model_name=self.model_name
        )
    
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """实现基类的抽象方法"""
        try:
            # 解析请求
            req_dict = self._parse_request_from_prompt(request)
            
            # 调用相应的链路
            response = await self.generate(req_dict)
            
            return AdapterResult(
                success=True,
                data=response,
                error_message=None
            )
        except Exception as e:
            return AdapterResult(
                success=False,
                data=None,
                error_message=str(e)
            )
    
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 计算成本
        input_cost = (response.input_tokens / 1000) * pricing.get('input_price_per_1k', 0.0)
        output_cost = (response.output_tokens / 1000) * pricing.get('output_price_per_1k', 0.0)
        
        return input_cost + output_cost
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        # 这里应该根据实际使用的模型返回定价
        # 由于我们使用多个链路，可能需要根据chain_type选择不同的定价
        return {
            'input_price_per_1k': 0.001,
            'output_price_per_1k': 0.002
        }
    
    def estimate_cost(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> Decimal:
        """估算文本生成成本
        
        Args:
            prompt: 文本提示，可以是字符串或TextGenerationRequest对象
            **kwargs: 其他参数
            
        Returns:
            Decimal: 预估成本
        """
        # 处理不同类型的prompt
        if isinstance(prompt, TextGenerationRequest):
            text = prompt.prompt
            max_tokens = prompt.max_tokens or self.default_max_tokens
        else:
            text = prompt
            max_tokens = kwargs.get('max_tokens', self.default_max_tokens)
        
        # 确保text是字符串
        if not isinstance(text, str):
            text = str(text) if text is not None else ''
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 估算token数量
        prompt_tokens = self._count_tokens(text)
        
        # 计算成本
        prompt_cost = prompt_tokens * pricing['input_price_per_1k'] / 1000
        completion_cost = max_tokens * pricing['output_price_per_1k'] / 1000
        
        return Decimal(str(prompt_cost + completion_cost))
    
    def _count_tokens(self, text: str) -> int:
        """估算token数量（简单实现）"""
        if not text:
            return 0
        # 这是一个简化的token计数，实际应该使用对应模型的tokenizer
        return int(len(text.split()) * 1.3)
    
    async def validate_config(self) -> bool:
        """验证配置"""
        # 验证基础配置
        if not await super().validate_config():
            return False
        
        # 验证链路配置
        try:
            if hasattr(self, 'outline_chain'):
                await self.outline_chain.validate_config()
            if hasattr(self, 'scene_chain'):
                await self.scene_chain.validate_config()
            if hasattr(self, 'dialogue_chain'):
                await self.dialogue_chain.validate_config()
            return True
        except Exception as e:
            self.logger.error(f"Chain validation failed: {e}")
            return False