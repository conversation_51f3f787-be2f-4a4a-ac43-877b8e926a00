"""文本生成适配器基础类

定义文本生成适配器的通用接口和功能。
"""

from abc import abstractmethod
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from decimal import Decimal

from adapters.base import APIAdapter, AdapterResult, AdapterConfig
from core.cost_control import ServiceType
from core.models import MediaType
from core.config import ConfigManager
from core.cost_control import CostController


@dataclass
class TextGenerationRequest:
    """文本生成请求"""
    prompt: str
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 0.9
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop_sequences: Optional[List[str]] = None
    system_prompt: Optional[str] = None
    # 兼容测试所需字段
    model: Optional[str] = None
    system_message: Optional[str] = None
    stream: bool = False
    

@dataclass
class TextGenerationResponse:
    """文本生成响应（字段对齐测试断言）"""
    text: str
    finish_reason: str
    total_tokens: int
    input_tokens: int
    output_tokens: int
    model_name: str
    

class TextGenerationAdapter(APIAdapter):
    """文本生成适配器基础类"""
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.model_name = config.model or 'gpt-4o-mini'
        self.default_max_tokens = config.custom_params.get('max_tokens', 2048)
        
    @property
    def media_type(self) -> MediaType:
        return MediaType.TEXT
    
    @property
    def supported_formats(self) -> List[str]:
        return ['text/plain', 'text/markdown']
    
    async def generate(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> TextGenerationResponse:
        """生成文本内容"""
        try:
            # 构建请求
            request: TextGenerationRequest
            is_req = isinstance(prompt, TextGenerationRequest)
            if not is_req:
                try:
                    # 兼容 producer.adapters.text.base_text.TextGenerationRequest
                    from producer.adapters.text.base_text import TextGenerationRequest as PTextReq  # type: ignore
                    is_req = isinstance(prompt, PTextReq)  # type: ignore
                except Exception:
                    is_req = False
            if is_req:
                # 若为外部类实例，拷贝为当前类，避免后续类型不一致
                req_obj = prompt  # type: ignore
                request = TextGenerationRequest(
                    prompt=getattr(req_obj, 'prompt', ''),
                    max_tokens=getattr(req_obj, 'max_tokens', None),
                    temperature=getattr(req_obj, 'temperature', 0.7),
                    top_p=getattr(req_obj, 'top_p', 0.9),
                    frequency_penalty=getattr(req_obj, 'frequency_penalty', 0.0),
                    presence_penalty=getattr(req_obj, 'presence_penalty', 0.0),
                    stop_sequences=getattr(req_obj, 'stop_sequences', None),
                    system_prompt=getattr(req_obj, 'system_prompt', None),
                    model=getattr(req_obj, 'model', None),
                    system_message=getattr(req_obj, 'system_message', None),
                    stream=getattr(req_obj, 'stream', False),
                )
            else:
                request = self._build_request(prompt, **kwargs)
            
            # 估算成本
            estimated_cost = self.estimate_cost(request)
            # 与测试对齐：直接调用成本控制器的检查方法
            if hasattr(self.cost_controller, 'check_cost_limit'):
                await self.cost_controller.check_cost_limit(estimated_cost)
            
            # 执行生成
            adapter_result = await self._execute_with_retry(self._generate_text, request)
            
            if adapter_result.success:
                # 记录实际成本
                actual_cost = self._calculate_actual_cost(adapter_result.data)
                # 与测试对齐：record_cost 作为异步方法被调用
                await self.cost_controller.record_cost(
                    actual_cost,
                    "generate",
                    service_type=ServiceType.TEXT_GENERATION,
                    service_name=self.config.service_name,
                    tokens_used=adapter_result.data.total_tokens,
                    metadata={
                        'model': self.model_name,
                        'prompt_tokens': adapter_result.data.input_tokens,
                        'completion_tokens': adapter_result.data.output_tokens,
                        'total_tokens': adapter_result.data.total_tokens
                    }
                )
                adapter_result.data.cost_usd = actual_cost
                return adapter_result.data
            
            # 若失败，抛出异常让重试/调用方处理
            raise Exception(adapter_result.error_message or "Text generation failed")
            
        except Exception:
            raise
    
    def _build_request(self, prompt: str, **kwargs) -> TextGenerationRequest:
        """构建文本生成请求"""
        return TextGenerationRequest(
            prompt=prompt,
            max_tokens=kwargs.get('max_tokens', self.default_max_tokens),
            temperature=kwargs.get('temperature', 0.7),
            top_p=kwargs.get('top_p', 0.9),
            frequency_penalty=kwargs.get('frequency_penalty', 0.0),
            presence_penalty=kwargs.get('presence_penalty', 0.0),
            stop_sequences=kwargs.get('stop_sequences'),
            system_prompt=kwargs.get('system_prompt')
        )
    
    @abstractmethod
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """执行实际的文本生成"""
        pass
    
    @abstractmethod
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        pass
    
    def estimate_cost(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> Decimal:
        """估算生成成本，返回Decimal，并兼容传入TextGenerationRequest"""
        if isinstance(prompt, TextGenerationRequest):
            text = prompt.prompt
            max_tokens = prompt.max_tokens or self.default_max_tokens
        else:
            text = prompt
            max_tokens = kwargs.get('max_tokens', self.default_max_tokens)
        
        # 确保text是字符串
        if not isinstance(text, str):
            text = str(text) if text is not None else ''
        
        # 估算token数量（粗略）
        prompt_tokens = int(len((text or '').split()) * 1.3)
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 计算成本（使用Decimal）
        prompt_cost = Decimal(str((prompt_tokens / 1000) * pricing.get('input_price_per_1k', 0.0)))
        completion_cost = Decimal(str((max_tokens / 1000) * pricing.get('output_price_per_1k', 0.0)))
        
        return prompt_cost + completion_cost
    
    @abstractmethod
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        pass
    
    def _count_tokens(self, text: str) -> int:
        """估算token数量（简单实现）"""
        # 这是一个简化的token计数，实际应该使用对应模型的tokenizer
        return int(len(text.split()) * 1.3)
    
    async def validate_config(self) -> bool:
        """验证配置"""
        if not await super().validate_config():
            return False
        
        # 验证模型名称
        if not self.model_name:
            return False
        
        return True