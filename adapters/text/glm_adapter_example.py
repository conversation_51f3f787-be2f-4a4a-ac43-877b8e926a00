"""
LangChain GLM适配器使用示例

本文件展示了如何使用LangChainGLMAdapter的三种模式：
1. 基础模式：直接使用ChatOpenAI进行简单对话
2. 记忆模式：使用ConversationBufferMemory保持对话上下文
3. 代理模式：使用智能代理处理复杂任务
"""

import asyncio
import json
from adapters.text.glm_adapter import LangChainGLMAdapter
from adapters.text.base_text import TextGenerationRequest
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController


# 配置示例
CONFIG_EXAMPLES = {
    # 基础模式配置
    "basic_mode": {
        "model_name": "glm-4-flash",
        "api_key": "your_api_key_here",
        "base_url": "https://open.bigmodel.cn/api/paas/v4/",
        "mode": "basic"
    },
    
    # 记忆模式配置
    "memory_mode": {
        "model_name": "glm-4-flash",
        "api_key": "your_api_key_here",
        "base_url": "https://open.bigmodel.cn/api/paas/v4/",
        "mode": "memory",
        "memory_limit": 4000
    },
    
    # 代理模式配置
    "agent_mode": {
        "model_name": "glm-4-flash",
        "api_key": "your_api_key_here",
        "base_url": "https://open.bigmodel.cn/api/paas/v4/",
        "mode": "agent",
        "agent_iterations": 15
    }
}


async def basic_mode_example():
    """基础模式示例"""
    print("=== 基础模式示例 ===")
    
    # 创建配置
    config_dict = CONFIG_EXAMPLES["basic_mode"]
    config = AdapterConfig(**config_dict)
    
    # 创建适配器
    config_manager = ConfigManager()
    cost_controller = CostController()
    adapter = LangChainGLMAdapter(config, config_manager, cost_controller)
    
    # 创建请求
    request = TextGenerationRequest(
        prompt="请写一个关于勇敢的小兔子的短故事，不超过100字。",
        system_prompt="你是一个儿童故事作家，擅长创作简单有趣的故事。",
        max_tokens=200
    )
    
    # 生成文本
    result = await adapter.generate_text(request)
    
    # 输出结果
    if result.success:
        print(f"生成结果: {result.content}")
        print(f"使用Token数: {result.tokens_used}")
        print(f"成本: {result.cost}")
    else:
        print(f"生成失败: {result.error}")
    
    print("\n")


async def memory_mode_example():
    """记忆模式示例"""
    print("=== 记忆模式示例 ===")
    
    # 创建配置
    config_dict = CONFIG_EXAMPLES["memory_mode"]
    config = AdapterConfig(**config_dict)
    
    # 创建适配器
    config_manager = ConfigManager()
    cost_controller = CostController()
    adapter = LangChainGLMAdapter(config, config_manager, cost_controller)
    
    # 第一次对话
    request1 = TextGenerationRequest(
        prompt="你好，我叫小明，今年8岁，喜欢画画和踢足球。",
        system_prompt="你是一个友好的对话助手。",
        max_tokens=100
    )
    
    result1 = await adapter.generate_text(request1)
    if result1.success:
        print(f"用户: {request1.prompt}")
        print(f"助手: {result1.content}")
    
    # 第二次对话（模型应该记住用户信息）
    request2 = TextGenerationRequest(
        prompt="根据我的兴趣爱好，你能推荐一些适合我的活动吗？",
        max_tokens=150
    )
    
    result2 = await adapter.generate_text(request2)
    if result2.success:
        print(f"用户: {request2.prompt}")
        print(f"助手: {result2.content}")
    
    # 获取记忆摘要
    memory_summary = adapter.get_memory_summary()
    print(f"\n记忆摘要:\n{memory_summary}")
    
    print("\n")


async def agent_mode_example():
    """代理模式示例"""
    print("=== 代理模式示例 ===")
    
    # 创建配置
    config_dict = CONFIG_EXAMPLES["agent_mode"]
    config = AdapterConfig(**config_dict)
    
    # 创建适配器
    config_manager = ConfigManager()
    cost_controller = CostController()
    adapter = LangChainGLMAdapter(config, config_manager, cost_controller)
    
    # 创建请求（要求代理使用工具创建剧本）
    request = TextGenerationRequest(
        prompt="请创建一个关于友谊的短剧本，包含两个角色和一个场景。",
        system_prompt="你是一个剧本创作助手，擅长创建有趣的故事和角色。",
        max_tokens=500
    )
    
    # 生成文本
    result = await adapter.generate_text(request)
    
    # 输出结果
    if result.success:
        print(f"生成结果: {result.content}")
        print(f"使用Token数: {result.tokens_used}")
        print(f"成本: {result.cost}")
    else:
        print(f"生成失败: {result.error}")
    
    print("\n")


async def mode_switching_example():
    """模式切换示例"""
    print("=== 模式切换示例 ===")
    
    # 创建基础模式配置
    config_dict = CONFIG_EXAMPLES["basic_mode"]
    config = AdapterConfig(**config_dict)
    
    # 创建适配器
    config_manager = ConfigManager()
    cost_controller = CostController()
    adapter = LangChainGLMAdapter(config, config_manager, cost_controller)
    
    # 使用基础模式
    request = TextGenerationRequest(
        prompt="请写一句关于春天的诗句。",
        max_tokens=50
    )
    
    result = await adapter.generate_text(request)
    if result.success:
        print(f"基础模式结果: {result.content}")
    
    # 切换到记忆模式
    adapter.set_mode(adapter.MODE_MEMORY)
    
    # 使用记忆模式
    request2 = TextGenerationRequest(
        prompt="现在写一句关于夏天的诗句。",
        max_tokens=50
    )
    
    result2 = await adapter.generate_text(request2)
    if result2.success:
        print(f"记忆模式结果: {result2.content}")
    
    # 再次使用记忆模式（应该记住之前的对话）
    request3 = TextGenerationRequest(
        prompt="根据我们刚才的诗句，写一句关于秋天的诗句。",
        max_tokens=50
    )
    
    result3 = await adapter.generate_text(request3)
    if result3.success:
        print(f"记忆模式（有上下文）结果: {result3.content}")
    
    print("\n")


async def main():
    """主函数，运行所有示例"""
    print("LangChain GLM适配器使用示例\n")
    
    # 注意：请将"your_api_key_here"替换为实际的API密钥
    
    # 运行基础模式示例
    # await basic_mode_example()
    
    # 运行记忆模式示例
    # await memory_mode_example()
    
    # 运行代理模式示例
    # await agent_mode_example()
    
    # 运行模式切换示例
    # await mode_switching_example()
    
    print("请取消注释上述示例代码以运行相应的示例。")


if __name__ == "__main__":
    asyncio.run(main())