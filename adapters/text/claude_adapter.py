"""Anthropic Claude适配器

通过Anthropic API实现Claude文本生成功能。
"""

from typing import Dict, List, Union
from decimal import Decimal
import aiohttp

from adapters.text.base_text import TextGenerationAdapter
from producer.adapters.text.base_text import TextGenerationRequest, TextGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController


class ClaudeAdapter(TextGenerationAdapter):
    """Anthropic Claude适配器"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        'claude-3-5-sonnet-20241022': {
            'input_price_per_1k': 0.003,  # $0.003 per 1K input tokens
            'output_price_per_1k': 0.015,  # $0.015 per 1K output tokens
            'max_tokens': 8192
        },
        'claude-3-5-haiku-20241022': {
            'input_price_per_1k': 0.0008,
            'output_price_per_1k': 0.004,
            'max_tokens': 8192
        },
        'claude-3-opus-20240229': {
            'input_price_per_1k': 0.015,
            'output_price_per_1k': 0.075,
            'max_tokens': 4096
        },
        'claude-3-sonnet-20240229': {
            'input_price_per_1k': 0.003,
            'output_price_per_1k': 0.015,
            'max_tokens': 4096
        }
    }
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.base_url = config.base_url or "https://api.anthropic.com/v1/messages"
        self.api_key = config.api_key
        self.anthropic_version = config.custom_params.get('anthropic_version', '2023-06-01')
        
        # 验证模型名称
        if self.model_name not in self.SUPPORTED_MODELS:
            raise ValueError(f"Unsupported model: {self.model_name}. Supported models: {list(self.SUPPORTED_MODELS.keys())}")
    
    async def _create_client(self):
        """创建HTTP客户端"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """执行Claude文本生成"""
        try:
            await self._create_client()
            
            # 构建请求数据
            data = {
                "model": self.model_name,
                "max_tokens": request.max_tokens,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "messages": self._build_messages(request)
            }
            
            # 添加系统消息（Claude使用单独的system字段）
            system_msg = request.system_message or request.system_prompt
            if system_msg:
                data["system"] = system_msg
            
            # 添加停止序列
            if request.stop_sequences:
                data["stop_sequences"] = request.stop_sequences
            
            # 发送请求
            headers = {
                "x-api-key": self.api_key,
                "anthropic-version": self.anthropic_version,
                "content-type": "application/json"
            }
            
            post_result = self.session.post(self.base_url, json=data, headers=headers)
            import asyncio as _asyncio
            response = await post_result if _asyncio.iscoroutine(post_result) else post_result
            if response.status != 200:
                # 尝试解析JSON错误
                err_msg = None
                try:
                    err_json = await response.json()
                    err_msg = err_json.get('error', {}).get('message') or err_json.get('message')
                except Exception:
                    try:
                        err_msg = await response.text()
                    except Exception:
                        err_msg = str(getattr(response, 'text', ''))
                from adapters.base import APIError, RateLimitError
                if response.status == 429:
                    raise RateLimitError(f"Rate limited: {err_msg}")
                raise APIError(f"API request failed with status {response.status}: {err_msg}")

            # 流式处理
            if getattr(request, 'stream', False):
                # Claude SSE-like stream: content.iter_chunked yields bytes starting with 'data: {...}\n\n'
                text_acc = ""
                # 兼容测试中的 AsyncMock：直接调用 iter_chunked 属性可能是可调用返回异步生成器
                iter_fn = response.content.iter_chunked
                if callable(iter_fn):
                    try:
                        agen = iter_fn(1024)  # 常规aiohttp签名
                    except TypeError:
                        agen = iter_fn()  # 测试中的无参可调用
                else:
                    agen = iter_fn  # 已是异步生成器
                async for chunk in agen:
                    try:
                        line = chunk.decode('utf-8').strip()
                        if not line.startswith('data:'):
                            continue
                        import json as _json
                        payload = _json.loads(line[len('data:'):].strip())
                        if payload.get('type') == 'content_block_delta':
                            delta = payload.get('delta', {})
                            text_acc += delta.get('text', '')
                    except Exception:
                        continue
                usage = {"input_tokens": 0, "output_tokens": 0}
                response_data = TextGenerationResponse(
                    text=text_acc,
                    finish_reason="stop",
                    total_tokens=usage.get("input_tokens", 0) + usage.get("output_tokens", 0),
                    input_tokens=usage.get("input_tokens", 0),
                    output_tokens=usage.get("output_tokens", 0),
                    model_name=self.model_name
                )
                return AdapterResult(success=True, data=response_data, metadata={"model": self.model_name})

            result = await response.json()

            # 检查API响应
            if "error" in result:
                from adapters.base import APIError
                raise APIError(f"API error: {result['error'].get('message', 'Unknown error')}")

            # 解析响应
            content = result.get("content", [])
            usage = result.get("usage", {})

            # 提取文本内容
            text_content = ""
            for item in content:
                if item.get("type") == "text":
                    text_content += item.get("text", "")

            response_data = TextGenerationResponse(
                text=text_content,
                finish_reason=result.get("stop_reason", "unknown"),
                total_tokens=usage.get("input_tokens", 0) + usage.get("output_tokens", 0),
                input_tokens=usage.get("input_tokens", 0),
                output_tokens=usage.get("output_tokens", 0),
                model_name=self.model_name
            )

            return AdapterResult(
                success=True,
                data=response_data,
                metadata={
                    "model": self.model_name,
                    "usage": usage,
                    "id": result.get("id"),
                    "type": result.get("type")
                }
            )
                
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"Claude generation failed: {str(e)}"
            )
    
    def _build_messages(self, request: TextGenerationRequest) -> List[Dict[str, str]]:
        """构建消息列表（Claude不在messages中包含system消息）"""
        messages = []
        
        # 只添加用户消息（系统消息在单独的system字段中）
        messages.append({
            "role": "user",
            "content": request.prompt
        })
        
        return messages
    
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        pricing = self._get_model_pricing()
        
        prompt_cost = (response.input_tokens / 1000) * pricing['input_price_per_1k']
        completion_cost = (response.output_tokens / 1000) * pricing['output_price_per_1k']
        
        return prompt_cost + completion_cost
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        return self.SUPPORTED_MODELS.get(self.model_name, {
            'input_price_per_1k': 0.003,
            'output_price_per_1k': 0.015
        })
    
    def estimate_cost(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> Decimal:
        """估算文本生成成本
        
        Args:
            prompt: 文本提示，可以是字符串或TextGenerationRequest对象
            **kwargs: 其他参数
            
        Returns:
            Decimal: 预估成本
        """
        # 处理不同类型的prompt
        if isinstance(prompt, TextGenerationRequest):
            text = prompt.prompt
            max_tokens = prompt.max_tokens or self.default_max_tokens
        else:
            text = prompt
            max_tokens = kwargs.get('max_tokens', self.default_max_tokens)
        
        # 确保text是字符串
        if not isinstance(text, str):
            text = str(text) if text is not None else ''
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 估算token数量
        prompt_tokens = self._count_tokens(text)
        
        # 计算成本
        prompt_cost = prompt_tokens * pricing['input_price_per_1k'] / 1000
        completion_cost = max_tokens * pricing['output_price_per_1k'] / 1000
        
        return Decimal(str(prompt_cost + completion_cost))
    
    def _count_tokens(self, text: str) -> int:
        """估算token数量（简单实现）"""
        if not text:
            return 0
        # 这是一个简化的token计数，实际应该使用对应模型的tokenizer
        return int(len(text.split()) * 1.3)
    
    async def validate_config(self) -> bool:
        """验证配置（简化版）"""
        if not self.api_key:
            return False
        return self.model_name in self.SUPPORTED_MODELS

    def get_supported_models(self) -> List[str]:
        """返回支持的模型列表"""
        return list(self.SUPPORTED_MODELS.keys())
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._create_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            self.session = None