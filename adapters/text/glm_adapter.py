"""基于LangChain的GLM适配器

使用LangChain的ChatOpenAI类调用智谱AI GLM模型，提高代码的可读性和可维护性。
支持基础对话、记忆管理和智能代理功能，适用于剧本生成等复杂任务。
"""

import os
import time
import logging
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from decimal import Decimal

from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory
from langchain.chains import LLMChain, ConversationChain
from langchain.agents import AgentExecutor, create_react_agent
from langchain import hub
from langchain.tools import tool
from langchain.schema.runnable import RunnablePassthrough

from adapters.text.base_text import TextGenerationAdapter, TextGenerationRequest, TextGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController, ServiceType

# 创建logger实例
logger = logging.getLogger(__name__)


class LangChainGLMAdapter(TextGenerationAdapter):
    """基于LangChain的GLM文本生成适配器
    
    支持三种使用模式：
    1. 基础模式：直接使用ChatOpenAI进行简单对话
    2. 记忆模式：使用ConversationBufferMemory保持对话上下文
    3. 代理模式：使用智能代理处理复杂任务
    """
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        'glm-4-flash': {
            'input_price_per_1k': 0.0001,  # ¥0.1/1M tokens
            'output_price_per_1k': 0.0001,
            'max_tokens': 8192
        },
        'glm-4-plus': {
            'input_price_per_1k': 0.05,  # ¥50/1M tokens
            'output_price_per_1k': 0.05,
            'max_tokens': 8192
        },
        'glm-4-air': {
            'input_price_per_1k': 0.001,  # ¥1/1M tokens
            'output_price_per_1k': 0.001,
            'max_tokens': 8192
        },
        'glm-4-airx': {
            'input_price_per_1k': 0.01,  # ¥10/1M tokens
            'output_price_per_1k': 0.01,
            'max_tokens': 8192
        },
        'glm-4.5': {
            'input_price_per_1k': 0.001,  # ¥1/1M tokens (估算价格)
            'output_price_per_1k': 0.001,
            'max_tokens': 8192
        }
    }
    
    # 支持的模式
    MODE_BASIC = "basic"      # 基础模式
    MODE_MEMORY = "memory"    # 记忆模式
    MODE_AGENT = "agent"      # 代理模式
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.api_key = config.api_key
        self.base_url = config.base_url or "https://open.bigmodel.cn/api/paas/v4/"
        
        # 从配置中获取模式，默认为基础模式
        self.mode = getattr(config, 'mode', self.MODE_BASIC)
        
        # 验证模型名称
        if self.model_name not in self.SUPPORTED_MODELS:
            available_models = list(self.SUPPORTED_MODELS.keys())
            raise ValueError(f"不支持的GLM模型: {self.model_name}. 可用模型: {available_models}")
        
        # 初始化LangChain组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化LangChain组件"""
        # 创建基础ChatOpenAI实例
        self.llm = ChatOpenAI(
            temperature=0.7,
            model=self.model_name,
            openai_api_key=self.api_key,
            openai_api_base=self.base_url
        )
        
        # 根据模式初始化不同的组件
        if self.mode == self.MODE_MEMORY:
            # 记忆模式：初始化对话记忆
            self.memory = ConversationBufferMemory(
                memory_key="chat_history",
                return_messages=True,
                max_token_limit=4000  # 限制记忆长度
            )
            
            # 创建对话链
            self.conversation = ConversationChain(
                llm=self.llm,
                memory=self.memory,
                verbose=True
            )
            
        elif self.mode == self.MODE_AGENT:
            # 代理模式：初始化智能代理
            self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化智能代理"""
        # 注册剧本生成相关的工具
        self._register_tools()
        
        # 获取提示模板，使用try-except处理可能的网络问题
        try:
            prompt = hub.pull("hwchase17/react")
        except Exception as e:
            logger.warning(f"无法从hub获取提示模板: {str(e)}，使用默认模板")
            # 创建一个简单的默认提示模板
            from langchain.prompts import PromptTemplate
            prompt = PromptTemplate.from_template(
                """回答以下问题，你可以使用以下工具：

{tools}

工具使用格式：使用以下格式：

```
问题: 你必须回答的输入问题
思考: 你应该始终思考该做什么
行动: 要采取的行动，应该是[{tool_names}]之一
行动输入: 行动的输入
观察: 行动的结果
... (这个思考/行动/行动输入/观察可以重复N次)
思考: 我现在知道最终答案
最终答案: 原始输入问题的最终答案
```

开始！

问题: {input}
思考:{agent_scratchpad}"""
            )
        
        # 创建代理
        agent = create_react_agent(self.llm, self.tools, prompt)
        
        # 创建代理执行器
        self.agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            max_iterations=15,
            early_stopping_method="generate"
        )
    
    def _register_tools(self):
        """注册智能代理可用的工具"""
        self.tools = []
        
        @tool
        def create_character(name: str, description: str, personality: str) -> str:
            """创建角色信息"""
            return f"角色创建成功：{name} - {description}，性格特点：{personality}"
        
        @tool
        def create_scene(location: str, time_period: str, description: str) -> str:
            """创建场景信息"""
            return f"场景创建成功：{location}，{time_period} - {description}"
        
        @tool
        def generate_dialogue(character: str, emotion: str, context: str) -> str:
            """生成角色对白"""
            return f"{character}（{emotion}）：这是一个示例对白，基于上下文：{context}"
        
        @tool
        def outline_story(theme: str, characters: List[str], scenes: List[str]) -> str:
            """生成故事大纲"""
            return f"故事大纲生成成功，主题：{theme}，包含角色：{', '.join(characters)}，场景：{', '.join(scenes)}"
        
        # 添加工具到列表
        self.tools.extend([
            create_character,
            create_scene,
            generate_dialogue,
            outline_story
        ])
    
    async def _create_client(self):
        """创建HTTP客户端"""
        # GLM适配器使用LangChain的ChatOpenAI，不需要单独创建HTTP客户端
        # LangChain会在内部处理HTTP连接
        pass
    
    def set_mode(self, mode: str):
        """设置适配器模式"""
        if mode not in [self.MODE_BASIC, self.MODE_MEMORY, self.MODE_AGENT]:
            raise ValueError(f"不支持的模式: {mode}")
        
        self.mode = mode
        self._initialize_components()
    
    def get_memory_summary(self) -> str:
        """获取记忆摘要（仅记忆模式）"""
        if self.mode != self.MODE_MEMORY:
            return "当前不是记忆模式"
        
        try:
            # 获取记忆变量
            memory_variables = self.memory.load_memory_variables({})
            chat_history = memory_variables.get("chat_history", [])
            
            if not chat_history:
                return "无记忆内容"
            
            # 将消息列表转换为字符串
            summary = []
            for message in chat_history:
                if isinstance(message, SystemMessage):
                    summary.append(f"系统: {message.content}")
                elif isinstance(message, HumanMessage):
                    summary.append(f"用户: {message.content}")
                elif isinstance(message, AIMessage):
                    summary.append(f"助手: {message.content}")
            
            return "\n".join(summary)
        except Exception as e:
            return f"获取记忆摘要时出错: {str(e)}"
    
    def clear_memory(self):
        """清除记忆（仅记忆模式）"""
        if self.mode == self.MODE_MEMORY:
            self.memory.clear()
    
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """使用LangChain生成文本"""
        start_time = time.time()
        try:
            # 根据当前模式调用不同的生成方法
            if self.mode == self.MODE_BASIC:
                content = await self._generate_basic(request)
            elif self.mode == self.MODE_MEMORY:
                content = await self._generate_with_memory(request)
            elif self.mode == self.MODE_AGENT:
                content = await self._generate_with_agent(request)
            else:
                raise ValueError(f"不支持的模式: {self.mode}")
            
            # 计算耗时
            duration = time.time() - start_time
            
            # 估算token使用量（LangChain可能不直接提供token计数）
            input_tokens = self._count_tokens(request.prompt)
            if request.system_prompt:
                input_tokens += self._count_tokens(request.system_prompt)
            output_tokens = self._count_tokens(content)
            
            # 计算成本
            model_config = self.SUPPORTED_MODELS[self.model_name]
            cost = (
                input_tokens * model_config['input_price_per_1k'] / 1000 +
                output_tokens * model_config['output_price_per_1k'] / 1000
            )
            
            # 构建响应
            text_response = TextGenerationResponse(
                text=content,
                finish_reason="stop",  # LangChain通常返回"stop"
                total_tokens=input_tokens + output_tokens,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                model_name=self.model_name
            )
            
            return AdapterResult(
                success=True,
                data=text_response,
                cost_usd=cost,
                metadata={
                    "model": self.model_name,
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "duration": duration
                }
            )
            
        except Exception as e:
            return AdapterResult(
                success=False,
                error_message=f"LangChain GLM适配器错误: {str(e)}",
                metadata={
                    "duration": time.time() - start_time
                }
            )
    
    async def _generate_basic(self, request: TextGenerationRequest) -> str:
        """基础模式生成文本"""
        # 准备消息
        messages = []
        if request.system_prompt:
            messages.append(SystemMessage(content=request.system_prompt))
        messages.append(HumanMessage(content=request.prompt))
        
        # 调用模型
        response = await self.llm.ainvoke(messages)
        return response.content
    
    async def _generate_with_memory(self, request: TextGenerationRequest) -> str:
        """记忆模式生成文本"""
        # 如果有系统提示，先将其添加到记忆中
        if request.system_prompt:
            # 使用ConversationChain的predict方法添加系统提示
            await self.conversation.apredict(input=request.system_prompt)
        
        # 使用对话链生成回复
        response = await self.conversation.arun(request.prompt)
        return response
    
    async def _generate_with_agent(self, request: TextGenerationRequest) -> str:
        """代理模式生成文本"""
        # 构建代理任务描述
        task = request.prompt
        if request.system_prompt:
            task = f"{request.system_prompt}\n\n任务：{task}"
        
        # 调用代理执行任务
        response = await self.agent_executor.ainvoke({"input": task})
        return response.get("output", "")
    
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        model_config = self.SUPPORTED_MODELS.get(self.model_name, {})
        input_cost = response.input_tokens * model_config.get('input_price_per_1k', 0) / 1000
        output_cost = response.output_tokens * model_config.get('output_price_per_1k', 0) / 1000
        return input_cost + output_cost
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        if self.model_name in self.SUPPORTED_MODELS:
            return self.SUPPORTED_MODELS[self.model_name]
        return {
            'input_price_per_1k': 0.0,
            'output_price_per_1k': 0.0
        }
    
    def _count_tokens(self, text: str) -> int:
        """估算token数量（简单实现）"""
        if not text:
            return 0
        # 这是一个简化的token计数，实际应该使用对应模型的tokenizer
        return int(len(text.split()) * 1.3)
    
    async def validate_config(self) -> bool:
        """验证配置"""
        if not self.api_key:
            return False
            
        try:
            # 发送测试请求
            test_request = TextGenerationRequest(
                prompt="测试",
                max_tokens=10
            )
            response = await self.generate_text(test_request)
            return response.success if hasattr(response, 'success') else True
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model_name in self.SUPPORTED_MODELS:
            return self.SUPPORTED_MODELS[self.model_name]
        return {}
    
    def estimate_cost(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> Decimal:
        """估算生成成本"""
        if isinstance(prompt, TextGenerationRequest):
            req = prompt
            text = req.prompt
            completion_tokens = req.max_tokens or self.default_max_tokens
        else:
            text = prompt
            completion_tokens = kwargs.get('max_tokens', self.default_max_tokens)
        
        # 估算token数量
        prompt_tokens = self._count_tokens(text)
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 计算成本（使用Decimal）
        prompt_cost = Decimal(prompt_tokens) / Decimal(1000) * Decimal(str(pricing.get('input_price_per_1k', 0.0)))
        completion_cost = Decimal(completion_tokens) / Decimal(1000) * Decimal(str(pricing.get('output_price_per_1k', 0.0)))
        
        return prompt_cost + completion_cost
    
    @classmethod
    def get_config_schema(cls) -> Dict[str, Any]:
        """获取配置模式"""
        return {
            "type": "object",
            "properties": {
                "model_name": {
                    "type": "string",
                    "description": "GLM模型名称",
                    "enum": list(cls.SUPPORTED_MODELS.keys()),
                    "default": "glm-4-flash"
                },
                "api_key": {
                    "type": "string",
                    "description": "智谱AI API密钥"
                },
                "base_url": {
                    "type": "string",
                    "description": "API基础URL",
                    "default": "https://open.bigmodel.cn/api/paas/v4/"
                },
                "mode": {
                    "type": "string",
                    "description": "适配器模式",
                    "enum": [cls.MODE_BASIC, cls.MODE_MEMORY, cls.MODE_AGENT],
                    "default": cls.MODE_BASIC
                },
                "memory_limit": {
                    "type": "integer",
                    "description": "记忆模式下的最大token限制",
                    "default": 4000
                },
                "agent_iterations": {
                    "type": "integer",
                    "description": "代理模式下的最大迭代次数",
                    "default": 15
                }
            },
            "required": ["api_key"]
        }