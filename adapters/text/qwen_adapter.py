"""通义千问适配器

通过阿里云DashScope API实现通义千问文本生成功能。
"""

from typing import Dict, List, Union
from decimal import Decimal
import aiohttp
import asyncio

from adapters.text.base_text import TextGenerationAdapter
from producer.adapters.text.base_text import TextGenerationRequest, TextGenerationResponse
from adapters.base import AdapterConfig, AdapterResult
from core.config import ConfigManager
from core.cost_control import CostController


class QwenAdapter(TextGenerationAdapter):
    """通义千问适配器"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        'qwen2.5-72b-instruct': {
            'input_price_per_1k': 0.0008,  # $0.0008 per 1K input tokens
            'output_price_per_1k': 0.002,  # $0.002 per 1K output tokens
            'max_tokens': 8192
        },
        'qwen2.5-32b-instruct': {
            'input_price_per_1k': 0.0004,
            'output_price_per_1k': 0.001,
            'max_tokens': 8192
        },
        'qwen2.5-14b-instruct': {
            'input_price_per_1k': 0.0002,
            'output_price_per_1k': 0.0006,
            'max_tokens': 8192
        },
        'qwen2.5-7b-instruct': {
            'input_price_per_1k': 0.0001,
            'output_price_per_1k': 0.0002,
            'max_tokens': 8192
        },
        'qwen-plus': {
            'input_price_per_1k': 0.0001,
            'output_price_per_1k': 0.0002,
            'max_tokens': 8192
        },
        'qwen-max': {
            'input_price_per_1k': 0.0003,
            'output_price_per_1k': 0.0009,
            'max_tokens': 8192
        },
        'qwen-turbo': {
            'input_price_per_1k': 0.0001,
            'output_price_per_1k': 0.0002,
            'max_tokens': 8192
        }
    }
    
    def __init__(self, config: AdapterConfig, config_manager: ConfigManager, cost_controller: CostController):
        super().__init__(config, config_manager, cost_controller)
        self.base_url = config.base_url or "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.api_key = config.api_key
        
        # 验证模型名称
        if self.model_name not in self.SUPPORTED_MODELS:
            raise ValueError(f"Unsupported model: {self.model_name}. Supported models: {list(self.SUPPORTED_MODELS.keys())}")
    
    async def _create_client(self):
        """创建HTTP客户端"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _generate_text(self, request: TextGenerationRequest) -> AdapterResult:
        """执行通义千问文本生成"""
        try:
            await self._create_client()
            
            # 构建请求数据
            data = {
                "model": self.model_name,
                "input": {
                    "messages": self._build_messages(request)
                },
                "parameters": {
                    "max_tokens": request.max_tokens,
                    "temperature": request.temperature,
                    "top_p": request.top_p,
                    "repetition_penalty": 1.0 + request.frequency_penalty,
                    "result_format": "message"
                }
            }
            
            # 添加停止序列
            if request.stop_sequences:
                data["parameters"]["stop"] = request.stop_sequences
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            post_result = self.session.post(self.base_url, json=data, headers=headers)
            response = await post_result if asyncio.iscoroutine(post_result) else post_result
            if response.status != 200:
                # 优先尝试JSON错误消息，否则退回文本/字符串
                err_msg = None
                try:
                    err_json = await response.json()
                    err_msg = err_json.get('error', {}).get('message') or err_json.get('message')
                except Exception:
                    try:
                        err_msg = await response.text()
                    except Exception:
                        err_msg = str(getattr(response, 'text', ''))
                from producer.adapters.base import APIError, RateLimitError
                if response.status == 429:
                    raise RateLimitError(f"Rate limited: {err_msg}")
                raise APIError(f"API request failed with status {response.status}: {err_msg}")

            result = await response.json()

            # 检查API响应
            if result.get("code") and result["code"] != "200":
                from producer.adapters.base import APIError
                raise APIError(f"API error: {result.get('message', 'Unknown error')}")

            # 解析响应
            output = result.get("output", {})
            usage = result.get("usage", {})

            text_val = output.get("text")
            finish_reason = output.get("finish_reason", "unknown")
            if not text_val:
                choice0 = (output.get("choices") or [{}])[0]
                text_val = choice0.get("message", {}).get("content", "")
                finish_reason = choice0.get("finish_reason", finish_reason)

            response_data = TextGenerationResponse(
                text=text_val or "",
                finish_reason=finish_reason,
                total_tokens=usage.get("total_tokens", usage.get("input_tokens", 0) + usage.get("output_tokens", 0)),
                input_tokens=usage.get("input_tokens", 0),
                output_tokens=usage.get("output_tokens", 0),
                model_name=self.model_name
            )

            return AdapterResult(
                success=True,
                data=response_data,
                metadata={
                    "model": self.model_name,
                    "usage": usage,
                    "request_id": result.get("request_id")
                }
            )
                
        except Exception as e:
            from producer.adapters.base import APIError, RateLimitError
            if isinstance(e, (RateLimitError, APIError)):
                raise
            raise APIError(f"Qwen generation failed: {str(e)}")
    
    def _build_messages(self, request: TextGenerationRequest) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = []
        
        # 添加系统消息（优先 system_message）
        system_msg = request.system_message or request.system_prompt
        if system_msg:
            messages.append({
                "role": "system",
                "content": system_msg
            })
        
        # 添加用户消息
        messages.append({
            "role": "user",
            "content": request.prompt
        })
        
        return messages
    
    def _calculate_actual_cost(self, response: TextGenerationResponse) -> float:
        """计算实际成本"""
        pricing = self._get_model_pricing()
        
        prompt_cost = (response.input_tokens / 1000) * pricing['input_price_per_1k']
        completion_cost = (response.output_tokens / 1000) * pricing['output_price_per_1k']
        
        return prompt_cost + completion_cost
    
    def _get_model_pricing(self) -> Dict[str, float]:
        """获取模型定价信息"""
        return self.SUPPORTED_MODELS.get(self.model_name, {
            'input_price_per_1k': 0.001,
            'output_price_per_1k': 0.002
        })
    
    def estimate_cost(self, prompt: Union[str, TextGenerationRequest], **kwargs) -> Decimal:
        """估算文本生成成本
        
        Args:
            prompt: 文本提示，可以是字符串或TextGenerationRequest对象
            **kwargs: 其他参数
            
        Returns:
            Decimal: 预估成本
        """
        # 处理不同类型的prompt
        if isinstance(prompt, TextGenerationRequest):
            text = prompt.prompt
            max_tokens = prompt.max_tokens or self.default_max_tokens
        else:
            text = prompt
            max_tokens = kwargs.get('max_tokens', self.default_max_tokens)
        
        # 确保text是字符串
        if not isinstance(text, str):
            text = str(text) if text is not None else ''
        
        # 获取模型定价
        pricing = self._get_model_pricing()
        
        # 估算token数量
        prompt_tokens = self._count_tokens(text)
        
        # 计算成本
        prompt_cost = prompt_tokens * pricing['input_price_per_1k'] / 1000
        completion_cost = max_tokens * pricing['output_price_per_1k'] / 1000
        
        return Decimal(str(prompt_cost + completion_cost))
    
    def _count_tokens(self, text: str) -> int:
        """估算token数量（简单实现）"""
        if not text:
            return 0
        # 这是一个简化的token计数，实际应该使用对应模型的tokenizer
        return int(len(text.split()) * 1.3)
    
    async def validate_config(self) -> bool:
        """验证配置（简化版，避免真实网络调用）"""
        if not self.api_key:
            return False
        return self.model_name in self.SUPPORTED_MODELS

    def get_supported_models(self) -> List[str]:
        """返回支持的模型列表"""
        return list(self.SUPPORTED_MODELS.keys())
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._create_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            self.session = None