<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Producer测试结果查看器</title>
    <style>
        :root {
            --primary-color: #4285f4;
            --success-color: #34a853;
            --warning-color: #fbbc05;
            --danger-color: #ea4335;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --border-color: #dadce0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), #34a853);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        h1 {
            font-size: 2rem;
            font-weight: 500;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .success-rate {
            color: var(--success-color);
        }
        
        .recent-success-rate {
            color: var(--warning-color);
        }
        
        .avg-duration {
            color: var(--primary-color);
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .results-table th,
        .results-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .results-table th {
            background-color: #f1f3f4;
            font-weight: 600;
            color: #5f6368;
        }
        
        .results-table tr:last-child td {
            border-bottom: none;
        }
        
        .results-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-success {
            background-color: rgba(52, 168, 83, 0.1);
            color: var(--success-color);
        }
        
        .status-failure {
            background-color: rgba(234, 67, 53, 0.1);
            color: var(--danger-color);
        }
        
        .detail-btn {
            background: none;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .detail-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 500;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .detail-section {
            margin-bottom: 20px;
        }
        
        .detail-section h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .detail-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .detail-label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #5f6368;
        }
        
        .detail-value {
            font-size: 1.1rem;
        }
        
        .json-viewer {
            background-color: #f1f3f4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .results-table {
                font-size: 0.9rem;
            }
            
            .results-table th,
            .results-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>Producer测试结果查看器</h1>
            <div>轻松查看和分析测试结果</div>
        </div>
    </header>
    
    <div class="container">
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-label">总测试次数</div>
                <div class="stat-value" id="total-count">{{ stats.total_count or 0 }}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">成功率</div>
                <div class="stat-value success-rate" id="success-rate">
                    {{ "%.1f"|format((stats.success_rate or 0) * 100) }}%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-label">近期成功率</div>
                <div class="stat-value recent-success-rate" id="recent-success-rate">
                    {{ "%.1f"|format((stats.recent_success_rate or 0) * 100) }}%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-label">平均执行时间</div>
                <div class="stat-value avg-duration" id="avg-duration">
                    {{ "%.2f"|format(stats.avg_duration or 0) }}s
                </div>
            </div>
        </div>
        
        <div class="results-section">
            <h2>测试结果列表</h2>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>大纲生成</th>
                        <th>场景生成</th>
                        <th>对话生成</th>
                        <th>总耗时</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="results-body">
                    {% for result in results %}
                    <tr>
                        <td>{{ result.datetime.strftime('%Y-%m-%d %H:%M:%S') if result.datetime else result.timestamp }}</td>
                        <td>
                            <span class="status-badge {{ 'status-success' if result.outline_success else 'status-failure' }}">
                                {{ '成功' if result.outline_success else '失败' }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {{ 'status-success' if result.scene_success else 'status-failure' }}">
                                {{ '成功' if result.scene_success else '失败' }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {{ 'status-success' if result.dialogue_success else 'status-failure' }}">
                                {{ '成功' if result.dialogue_success else '失败' }}
                            </span>
                        </td>
                        <td>{{ "%.2f"|format(result.total_duration) }}s</td>
                        <td>
                            <span class="status-badge {{ 'status-success' if result.success else 'status-failure' }}">
                                {{ '全部成功' if result.success else '存在失败' }}
                            </span>
                        </td>
                        <td>
                            <button class="detail-btn" onclick="showDetail('{{ result.filename }}')">查看详情</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 详情模态框 -->
    <div id="detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">测试详情</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 详情内容将通过JavaScript加载 -->
            </div>
        </div>
    </div>
    
    <footer>
        <p>Producer测试结果查看器 - 让测试结果查看更简单</p>
    </footer>
    
    <script>
        // 全局变量
        let currentDetailData = null;
        
        // 显示详情
        function showDetail(filename) {
            fetch(`/api/result/${filename}`)
                .then(response => response.json())
                .then(data => {
                    currentDetailData = data;
                    renderDetailModal(data);
                    document.getElementById('detail-modal').style.display = 'flex';
                })
                .catch(error => {
                    console.error('获取详情失败:', error);
                    alert('获取详情失败');
                });
        }
        
        // 渲染详情模态框
        function renderDetailModal(data) {
            const modalBody = document.getElementById('modal-body');
            
            // 格式化时间戳
            const timestamp = data.timestamp;
            const formattedTime = formatTimestamp(timestamp);
            
            // 构建HTML内容
            let html = `
                <div class="detail-section">
                    <h3>基本信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">时间戳</div>
                            <div class="detail-value">${formattedTime}</div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加摘要信息
            if (data.summary) {
                html += `
                    <div class="detail-section">
                        <h3>测试摘要</h3>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">大纲生成</div>
                                <div class="detail-value" style="color: ${data.summary.outline_success ? '#34a853' : '#ea4335'}">
                                    ${data.summary.outline_success ? '成功' : '失败'}
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">场景生成</div>
                                <div class="detail-value" style="color: ${data.summary.scene_success ? '#34a853' : '#ea4335'}">
                                    ${data.summary.scene_success ? '成功' : '失败'}
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">对话生成</div>
                                <div class="detail-value" style="color: ${data.summary.dialogue_success ? '#34a853' : '#ea4335'}">
                                    ${data.summary.dialogue_success ? '成功' : '失败'}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // 添加详细信息
            if (data.details) {
                html += `
                    <div class="detail-section">
                        <h3>详细信息</h3>
                `;
                
                // 大纲详情
                if (data.details.outline) {
                    const outline = data.details.outline;
                    html += `
                        <div class="detail-item">
                            <div class="detail-label">大纲生成详情</div>
                            <div class="detail-value">
                                执行时间: ${outline.duration ? outline.duration.toFixed(2) + 's' : 'N/A'}<br>
                                角色数量: ${outline.characters_count || 'N/A'}<br>
                                场景数量: ${outline.scenes_count || 'N/A'}
                            </div>
                        </div>
                    `;
                }
                
                // 场景详情
                if (data.details.scene) {
                    const scene = data.details.scene;
                    html += `
                        <div class="detail-item">
                            <div class="detail-label">场景生成详情</div>
                            <div class="detail-value">
                                执行时间: ${scene.duration ? scene.duration.toFixed(2) + 's' : 'N/A'}<br>
                                镜头数量: ${scene.shots_count || 'N/A'}<br>
                                总时长: ${scene.total_duration || 'N/A'}s
                            </div>
                        </div>
                    `;
                }
                
                // 对话详情
                if (data.details.dialogue) {
                    const dialogue = data.details.dialogue;
                    html += `
                        <div class="detail-item">
                            <div class="detail-label">对话生成详情</div>
                            <div class="detail-value">
                                执行时间: ${dialogue.duration ? dialogue.duration.toFixed(2) + 's' : 'N/A'}<br>
                                对话行数: ${dialogue.lines_count || 'N/A'}<br>
                                总时长: ${dialogue.total_duration || 'N/A'}s
                            </div>
                        </div>
                    `;
                }
                
                html += `</div>`;
            }
            
            // 添加原始JSON数据
            html += `
                <div class="detail-section">
                    <h3>原始数据</h3>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;
            
            modalBody.innerHTML = html;
        }
        
        // 格式化时间戳
        function formatTimestamp(timestamp) {
            if (!timestamp) return 'N/A';
            
            // 将YYYYMMDD_HHMMSS格式转换为更易读的格式
            const match = timestamp.match(/^(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})$/);
            if (match) {
                const [, year, month, day, hour, minute, second] = match;
                return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            }
            return timestamp;
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('detail-modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('detail-modal');
            if (event.target === modal) {
                closeModal();
            }
        }
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>