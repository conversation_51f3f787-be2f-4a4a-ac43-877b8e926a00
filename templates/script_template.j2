# 历史短剧剧本模板

## 剧本信息
**标题**: {{ title }}
**类型**: {{ genre }}
**时长**: {{ duration }}分钟
**朝代**: {{ dynasty }}
**主题**: {{ theme }}

## 角色设定
{% for character in characters %}
### {{ character.name }}
{% if character.title %}- **称号**: {{ character.title }}{% endif %}
- **身份**: {{ character.social_status }}
- **时代**: {{ character.era }}
- **性格**: {{ character.personality | join(', ') if character.personality is iterable and character.personality is not string else character.personality }}
- **外貌**: {{ character.appearance }}
- **声音**: {{ character.voice_style }}
- **描述**: {{ character.description }}
{% endfor %}

## 场景大纲
{% for scene in scenes %}
### 第{{ loop.index }}场：{{ scene.title }}
**场景ID**: {{ scene.scene_id }}
**地点**: {{ scene.location }}
**时间**: {{ scene.time_period }}
{% if scene.weather %}- **天气**: {{ scene.weather }}{% endif %}
**氛围**: {{ scene.atmosphere }}
**背景描述**: {{ scene.background_description }}
**光照**: {{ scene.lighting }}
**镜头角度**: {{ scene.camera_angle }}
**时长**: {{ scene.duration }}秒
{% endfor %}

## 对白要求
- 符合{{ dynasty }}朝代语言特色
- 体现角色性格差异
- 推进剧情发展
- 增强戏剧张力
- 适合{{ duration }}分钟时长

## 视觉风格
- **色调**: {{ visual_style.color_tone }}
- **构图**: {{ visual_style.composition }}
- **服装风格**: {{ visual_style.costume_style }}
- **场景风格**: {{ visual_style.scene_style }}

## 音效要求
- **背景音乐**: {{ audio_style.background_music }}
- **音效**: {{ audio_style.sound_effects }}
- **配音风格**: {{ audio_style.voice_style }}