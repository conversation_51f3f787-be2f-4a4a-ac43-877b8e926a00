<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剧本查看器 - Producer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
        }

        .completion-rate {
            color: #28a745;
        }

        .projects-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem 2rem;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .projects-grid {
            display: grid;
            gap: 1rem;
            padding: 1.5rem;
        }

        .project-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .project-card:hover {
            background: #e9ecef;
            border-color: #667eea;
            transform: translateY(-1px);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .project-theme {
            color: #666;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-final {
            background-color: #d4edda;
            color: #155724;
        }

        .status-dialogues_complete {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-scenes_complete {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-outline_complete {
            background-color: #f0e6ff;
            color: #6f42c1;
        }

        .status-draft {
            background-color: #f8d7da;
            color: #721c24;
        }

        .project-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .meta-item {
            text-align: center;
        }

        .meta-label {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .meta-value {
            font-weight: 600;
            color: #333;
        }

        .project-progress {
            margin-top: 1rem;
        }

        .progress-bar {
            background-color: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.5rem;
            text-align: center;
        }

        .project-actions {
            margin-top: 1rem;
            text-align: right;
        }

        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .empty-state p {
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }

            .project-meta {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <h1>🎬 剧本查看器</h1>
            <p>查看和管理您的剧本项目</p>
        </div>
    </header>

    <div class="container">
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-label">总项目数</div>
                <div class="stat-value" id="total-projects">{{ stats.total_projects or 0 }}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">已完成</div>
                <div class="stat-value" id="completed-projects">{{ stats.completed_projects or 0 }}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">完成率</div>
                <div class="stat-value completion-rate" id="completion-rate">
                    {{ "%.1f"|format((stats.completion_rate or 0) * 100) }}%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-label">平均角色数</div>
                <div class="stat-value" id="avg-characters">
                    {{ "%.1f"|format(stats.avg_characters or 0) }}
                </div>
            </div>
        </div>

        <div class="projects-section">
            <div class="section-header">
                <h2>剧本项目</h2>
                <p>点击项目卡片查看详细信息</p>
            </div>
            
            {% if projects %}
            <div class="projects-grid">
                {% for project in projects %}
                <div class="project-card" onclick="window.location.href='/project/{{ project.id }}'">
                    <div class="project-header">
                        <div>
                            <div class="project-title">{{ project.title }}</div>
                            <div class="project-theme">{{ project.theme }} · {{ project.era }}</div>
                        </div>
                        <span class="status-badge status-{{ project.status }}">{{ project.status_display }}</span>
                    </div>
                    
                    <div class="project-meta">
                        <div class="meta-item">
                            <div class="meta-label">角色数</div>
                            <div class="meta-value">{{ project.characters_count }}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">场景数</div>
                            <div class="meta-value">{{ project.scenes_count }}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">对话数</div>
                            <div class="meta-value">{{ project.dialogues_count }}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">时长</div>
                            <div class="meta-value">{{ project.target_duration }}s</div>
                        </div>
                    </div>
                    
                    <div class="project-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ project.progress }}%"></div>
                        </div>
                        <div class="progress-text">完成度: {{ project.progress }}%</div>
                    </div>
                    
                    <div class="project-actions">
                        <a href="/project/{{ project.id }}" class="btn" onclick="event.stopPropagation()">查看详情</a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <h3>📝 暂无剧本项目</h3>
                <p>您还没有创建任何剧本项目。使用 script_cli.py 创建您的第一个剧本吧！</p>
                <a href="#" class="btn" onclick="location.reload()">刷新页面</a>
            </div>
            {% endif %}
        </div>
    </div>

    <button class="refresh-btn" onclick="location.reload()" title="刷新页面">
        🔄
    </button>

    <script>
        // 自动刷新功能
        let autoRefresh = false;
        
        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            if (autoRefresh) {
                setInterval(() => {
                    if (autoRefresh) {
                        location.reload();
                    }
                }, 30000); // 30秒刷新一次
            }
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                location.reload();
            }
        });
        
        // 项目卡片动画
        document.querySelectorAll('.project-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>