"""路由系统

实现RESTful路由管理、参数解析和中间件支持。
"""

import re
from typing import Dict, List, Callable, Optional, Any, Tuple
from urllib.parse import parse_qs, urlparse
from .exceptions import NotFoundError, MethodNotAllowedError


class Route:
    """路由定义"""
    
    def __init__(self, path: str, handler: Callable, methods: List[str] = None, name: str = None):
        self.path = path
        self.handler = handler
        self.methods = methods or ['GET']
        self.name = name or f"{handler.__name__}_{id(self)}"
        
        # 编译路径模式
        self.pattern, self.param_names = self._compile_path(path)
    
    def _compile_path(self, path: str) -> Tuple[re.Pattern, List[str]]:
        """编译路径为正则表达式"""
        param_names = []
        pattern = path
        
        # 处理路径参数 {param}
        def replace_param(match):
            param_name = match.group(1)
            param_names.append(param_name)
            return r'([^/]+)'
        
        pattern = re.sub(r'\{([^}]+)\}', replace_param, pattern)
        
        # 确保精确匹配
        pattern = f'^{pattern}$'
        
        return re.compile(pattern), param_names
    
    def match(self, path: str, method: str) -> Optional[Dict[str, str]]:
        """匹配路径和方法"""
        if method not in self.methods:
            return None
        
        match = self.pattern.match(path)
        if not match:
            return None
        
        # 提取路径参数
        params = {}
        for i, param_name in enumerate(self.param_names):
            params[param_name] = match.group(i + 1)
        
        return params
    
    def __str__(self) -> str:
        return f"Route({self.path}, {self.methods}, {self.name})"


class RouteGroup:
    """路由组
    
    增加对父 Router 的引用以便在组内添加路由时同步注册到 Router。
    """
    
    def __init__(self, prefix: str = '', middleware: List[Callable] = None, router: 'Router' = None):
        """初始化路由组
        
        Args:
            prefix: 路由前缀
            middleware: 路由组中间件列表
            router: 父级 Router 引用，用于同步注册到全局路由表
        """
        self.prefix = prefix.rstrip('/')
        self.middleware = middleware or []
        self.routes: List[Route] = []
        self._router = router
    
    def add_route(self, path: str, handler: Callable, methods: List[str] = None, name: str = None):
        """添加路由
        
        同步将路由注册到父 Router，确保 Router.resolve 能找到该路由。
        """
        full_path = f"{self.prefix}{path}" if path != '/' else self.prefix or '/'
        route = Route(full_path, handler, methods, name)
        self.routes.append(route)
        # 同步注册到父 Router，避免仅存在于组内导致 Router.resolve 找不到
        if hasattr(self, '_router') and self._router is not None:
            self._router.routes.append(route)
        return route

    def get(self, path: str, name: str = None):
        """GET路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, ['GET'], name)
            return handler
        return decorator
    
    def post(self, path: str, name: str = None):
        """POST路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, ['POST'], name)
            return handler
        return decorator
    
    def put(self, path: str, name: str = None):
        """PUT路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, ['PUT'], name)
            return handler
        return decorator
    
    def delete(self, path: str, name: str = None):
        """DELETE路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, ['DELETE'], name)
            return handler
        return decorator
    
    def route(self, path: str, methods: List[str] = None, name: str = None):
        """通用路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, methods, name)
            return handler
        return decorator


class Router:
    """路由器"""
    
    def __init__(self):
        self.routes: List[Route] = []
        self.groups: List[RouteGroup] = []
        self.middleware: List[Callable] = []
    
    def add_route(self, path: str, handler: Callable, methods: List[str] = None, name: str = None) -> Route:
        """添加路由"""
        route = Route(path, handler, methods, name)
        self.routes.append(route)
        return route
    
    def add_group(self, group: RouteGroup):
        """添加路由组
        
        继续保留对现有 group.routes 的扩展，以兼容外部先构建再注册的场景。
        """
        self.groups.append(group)
        self.routes.extend(group.routes)
    
    def group(self, prefix: str = '', middleware: List[Callable] = None) -> RouteGroup:
        """创建路由组
        
        传入自身引用，确保后续对组内的 add_route 会同步更新到 Router.routes。
        """
        group = RouteGroup(prefix, middleware, router=self)
        self.add_group(group)
        return group
    
    def add_middleware(self, middleware: Callable):
        """添加全局中间件"""
        self.middleware.append(middleware)
    
    def get(self, path: str, name: str = None):
        """GET路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, ['GET'], name)
            return handler
        return decorator
    
    def post(self, path: str, name: str = None):
        """POST路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, ['POST'], name)
            return handler
        return decorator
    
    def route(self, path: str, methods: List[str] = None, name: str = None):
        """通用路由装饰器"""
        def decorator(handler: Callable):
            self.add_route(path, handler, methods, name)
            return handler
        return decorator

    def resolve(self, path: str, method: str) -> Tuple[Callable, Dict[str, str], List[Callable]]:
        """解析路由"""
        # 查找匹配的路由
        matched_route = None
        path_params = {}
        allowed_methods = set()
        
        for route in self.routes:
            params = route.match(path, method)
            if params is not None:
                matched_route = route
                path_params = params
                break
            
            # 收集允许的方法（用于405错误）
            if route.pattern.match(path):
                allowed_methods.update(route.methods)
        
        if matched_route is None:
            if allowed_methods:
                raise MethodNotAllowedError(f"方法 {method} 不被允许，允许的方法: {', '.join(sorted(allowed_methods))}")
            else:
                raise NotFoundError(f"路径 {path} 未找到")
        
        # 收集中间件
        middleware_list = self.middleware.copy()
        
        # 添加路由组中间件
        for group in self.groups:
            if any(route in group.routes for route in [matched_route]):
                middleware_list.extend(group.middleware)
        
        return matched_route.handler, path_params, middleware_list
    
    def url_for(self, name: str, **params) -> str:
        """根据路由名称生成URL"""
        for route in self.routes:
            if route.name == name:
                url = route.path
                for param_name, param_value in params.items():
                    url = url.replace(f'{{{param_name}}}', str(param_value))
                return url
        
        raise ValueError(f"未找到名为 '{name}' 的路由")
    
    def get_routes(self) -> List[Route]:
        """获取所有路由"""
        return self.routes.copy()
    
    def print_routes(self):
        """打印所有路由（调试用）"""
        print("注册的路由:")
        for route in self.routes:
            print(f"  {route}")


class RequestParser:
    """请求解析器"""
    
    @staticmethod
    def parse_query_params(query_string: str) -> Dict[str, Any]:
        """解析查询参数"""
        if not query_string:
            return {}
        
        params = parse_qs(query_string, keep_blank_values=True)
        
        # 简化单值参数
        result = {}
        for key, values in params.items():
            if len(values) == 1:
                result[key] = values[0]
            else:
                result[key] = values
        
        return result
    
    @staticmethod
    def parse_url(url: str) -> Tuple[str, Dict[str, Any]]:
        """解析URL"""
        parsed = urlparse(url)
        path = parsed.path
        query_params = RequestParser.parse_query_params(parsed.query)
        return path, query_params
    
    @staticmethod
    def parse_content_type(content_type: str) -> Tuple[str, Dict[str, str]]:
        """解析Content-Type头"""
        if not content_type:
            return '', {}
        
        parts = content_type.split(';')
        media_type = parts[0].strip()
        
        params = {}
        for part in parts[1:]:
            if '=' in part:
                key, value = part.split('=', 1)
                params[key.strip()] = value.strip().strip('"')
        
        return media_type, params