"""Web应用异常处理系统

定义统一的异常类型和错误处理机制。
"""

import traceback
from typing import Dict, Any, Optional


class WebException(Exception):
    """Web应用基础异常类"""
    
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error': True,
            'message': self.message,
            'status_code': self.status_code,
            'details': self.details
        }
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}: {self.message} (status: {self.status_code})"


class BadRequestError(WebException):
    """400 错误请求"""
    
    def __init__(self, message: str = "请求参数错误", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 400, details)


class UnauthorizedError(WebException):
    """401 未授权"""
    
    def __init__(self, message: str = "未授权访问", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 401, details)


class ForbiddenError(WebException):
    """403 禁止访问"""
    
    def __init__(self, message: str = "禁止访问", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 403, details)


class NotFoundError(WebException):
    """404 资源未找到"""
    
    def __init__(self, message: str = "请求的资源不存在", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 404, details)


class MethodNotAllowedError(WebException):
    """405 方法不允许"""
    
    def __init__(self, message: str = "请求方法不允许", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 405, details)


class InternalServerError(WebException):
    """500 内部服务器错误"""
    
    def __init__(self, message: str = "内部服务器错误", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 500, details)


class ServiceUnavailableError(WebException):
    """503 服务不可用"""
    
    def __init__(self, message: str = "服务暂时不可用", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 503, details)


class ValidationError(BadRequestError):
    """数据验证错误"""
    
    def __init__(self, field: str, message: str, value: Any = None):
        details = {
            'field': field,
            'value': value,
            'validation_error': True
        }
        super().__init__(f"字段 '{field}' 验证失败: {message}", details)


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, debug: bool = False):
        self.debug = debug
    
    def handle_exception(self, exc: Exception) -> Dict[str, Any]:
        """处理异常并返回响应数据"""
        if isinstance(exc, WebException):
            return self._handle_web_exception(exc)
        else:
            return self._handle_generic_exception(exc)
    
    def _handle_web_exception(self, exc: WebException) -> Dict[str, Any]:
        """处理Web异常"""
        response_data = exc.to_dict()
        
        if self.debug:
            response_data['traceback'] = traceback.format_exc()
        
        return response_data
    
    def _handle_generic_exception(self, exc: Exception) -> Dict[str, Any]:
        """处理通用异常"""
        response_data = {
            'error': True,
            'message': '内部服务器错误',
            'status_code': 500,
            'details': {}
        }
        
        if self.debug:
            response_data['details'] = {
                'exception_type': exc.__class__.__name__,
                'exception_message': str(exc),
                'traceback': traceback.format_exc()
            }
        
        return response_data
    
    def format_error_response(self, error_data: Dict[str, Any]) -> str:
        """格式化错误响应为HTML"""
        status_code = error_data.get('status_code', 500)
        message = error_data.get('message', '未知错误')
        
        if self.debug and 'traceback' in error_data:
            traceback_html = f"<pre>{error_data['traceback']}</pre>"
        else:
            traceback_html = ""
        
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>错误 {status_code}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .error-container {{ max-width: 800px; margin: 0 auto; }}
        .error-code {{ font-size: 48px; color: #e74c3c; margin-bottom: 20px; }}
        .error-message {{ font-size: 24px; color: #2c3e50; margin-bottom: 30px; }}
        .error-details {{ background: #f8f9fa; padding: 20px; border-radius: 5px; }}
        pre {{ background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 5px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">{status_code}</div>
        <div class="error-message">{message}</div>
        {traceback_html}
    </div>
</body>
</html>
        """.strip()