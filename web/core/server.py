"""Web服务器核心

整合路由、中间件和异常处理的HTTP服务器实现。
"""

import json
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs

from .router import Router, RequestParser
from .middleware import MiddlewareManager, Request, Response, LoggingMiddleware, SecurityMiddleware
from .exceptions import ExceptionHandler, WebException, NotFoundError


class WebRequestHandler(BaseHTTPRequestHandler):
    """Web请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 保持已有的web_server引用，避免被覆盖为None
        if not hasattr(self, 'web_server'):
            self.web_server = None
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        self._handle_request()
    
    def do_POST(self):
        """处理POST请求"""
        self._handle_request()
    
    def do_PUT(self):
        """处理PUT请求"""
        self._handle_request()
    
    def do_DELETE(self):
        """处理DELETE请求"""
        self._handle_request()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求"""
        self._handle_request()
    
    def _handle_request(self):
        """统一请求处理"""
        try:
            # 检查web_server是否正确设置
            if self.web_server is None:
                raise RuntimeError("WebServer实例未正确设置")
            
            # 解析URL和查询参数
            path, query_params = RequestParser.parse_url(self.path)

            # 忽略开发工具注入的请求（如 Vite 客户端、React Refresh 等），避免 404 异常与无意义日志
            dev_tool_prefixes = ('/@vite/', '/@react-refresh', '/__vite', '/node_modules/')
            if path == '/@vite/client' or path.startswith(dev_tool_prefixes):
                response = Response('', 204, content_type='text/plain; charset=utf-8')
                self._send_response(response)
                return
            
            # 解析路由
            handler, path_params, middleware_list = self.web_server.router.resolve(path, self.command)
            
            # 创建请求对象
            request = Request(self, path_params, query_params)
            
            # 创建中间件管理器并添加路由特定中间件
            middleware_manager = MiddlewareManager()
            
            # 添加全局中间件
            for middleware in self.web_server.middleware_manager.middleware_list:
                middleware_manager.add(middleware)
            
            # 添加路由特定中间件
            for middleware in middleware_list:
                if hasattr(middleware, 'process_request'):
                    middleware_manager.add(middleware)
            
            # 处理请求中间件
            response = middleware_manager.process_request(request)
            
            if response is None:
                # 调用路由处理器
                response = handler(request)
                
                # 确保返回Response对象
                if not isinstance(response, Response):
                    if isinstance(response, str):
                        response = Response(response)
                    elif isinstance(response, dict):
                        response = Response.json(response)
                    else:
                        response = Response(str(response))
            
            # 处理响应中间件
            response = middleware_manager.process_response(request, response)
            
            # 发送响应
            self._send_response(response)
            
        except Exception as e:
            self._handle_exception(e)
    
    def _handle_exception(self, exc: Exception):
        """处理异常"""
        # 打印详细错误信息到控制台
        import traceback
        print(f"\n=== 服务器错误 ===")
        print(f"错误类型: {type(exc).__name__}")
        print(f"错误信息: {str(exc)}")
        print(f"错误堆栈:")
        print(traceback.format_exc())
        print(f"=== 错误结束 ===\n")
        
        try:
            if hasattr(self.web_server, 'exception_handler'):
                error_data = self.web_server.exception_handler.handle_exception(exc)
                
                if isinstance(exc, WebException):
                    status_code = exc.status_code
                else:
                    status_code = 500
                
                # 根据Accept头决定响应格式
                accept = self.headers.get('Accept', '')
                
                if 'application/json' in accept:
                    # JSON响应
                    content = json.dumps(error_data, ensure_ascii=False, indent=2)
                    content_type = 'application/json; charset=utf-8'
                else:
                    # HTML响应
                    content = self.web_server.exception_handler.format_error_response(error_data)
                    content_type = 'text/html; charset=utf-8'
                
                response = Response(content, status_code, content_type=content_type)
                self._send_response(response)
            else:
                # 备用错误处理
                response = Response(f'Internal Server Error: {str(exc)}', 500, content_type='text/plain; charset=utf-8')
                self._send_response(response)
            
        except Exception as e:
            # 最后的异常处理
            self.send_error(500, f"Internal Server Error: {str(e)}")
    
    def _send_response(self, response: Response):
        """发送HTTP响应"""
        # 发送状态码
        self.send_response(response.status_code)
        
        # 发送响应头
        for name, value in response.headers.items():
            if name == 'Set-Cookie':
                # 处理多个Cookie
                if isinstance(value, list):
                    for cookie in value:
                        self.send_header(name, cookie)
                else:
                    self.send_header(name, value)
            else:
                self.send_header(name, value)
        
        # 结束响应头
        self.end_headers()
        
        # 发送响应体
        if response.content:
            if isinstance(response.content, str):
                content_bytes = response.content.encode('utf-8')
            else:
                content_bytes = response.content
            
            self.wfile.write(content_bytes)
    
    def log_message(self, format, *args):
        """重写日志方法，使用自定义日志器"""
        if hasattr(self, 'web_server') and self.web_server and hasattr(self.web_server, 'logger'):
            self.web_server.logger.info(f"{self.address_string()} - {format % args}")
        else:
            super().log_message(format, *args)


class WebServer:
    """Web服务器"""
    
    def __init__(self, config):
        self.config = config
        self.router = Router()
        self.middleware_manager = MiddlewareManager()
        
        # 初始化异常处理器
        self.exception_handler = ExceptionHandler()
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 添加默认中间件
        self._add_default_middleware()
        
        # HTTP服务器
        self.http_server = None
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger('web_server')
        logger.setLevel(getattr(logging, self.config.logging.level))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(self.config.logging.format)
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _add_default_middleware(self):
        """添加默认中间件"""
        # 日志中间件
        self.middleware_manager.add(LoggingMiddleware(self.logger))
        
        # 安全中间件
        self.middleware_manager.add(SecurityMiddleware())
    
    def add_middleware(self, middleware):
        """添加中间件"""
        self.middleware_manager.add(middleware)
    
    def get_router(self) -> Router:
        """获取路由器"""
        return self.router
    
    def create_request_handler_class(self):
        """创建请求处理器类"""
        web_server = self
        
        class RequestHandler(WebRequestHandler):
            def __init__(self, *args, **kwargs):
                self.web_server = web_server
                super().__init__(*args, **kwargs)
        
        return RequestHandler
    
    def start(self, host: str = None, port: int = None):
        """启动服务器"""
        host = host or self.config.HOST
        port = port or self.config.PORT
        
        # 创建HTTP服务器
        handler_class = self.create_request_handler_class()
        self.http_server = HTTPServer((host, port), handler_class)
        
        self.logger.info(f"🚀 {self.config.name} v{self.config.version} 启动")
        self.logger.info(f"📍 服务地址: http://{host}:{port}")
        self.logger.info(f"🔧 调试模式: {'开启' if self.config.server.debug else '关闭'}")
        
        if self.config.server.debug:
            self.router.print_routes()
        
        try:
            self.http_server.serve_forever()
        except KeyboardInterrupt:
            self.logger.info("\n⏹️  服务器停止")
            self.stop()
    
    def stop(self):
        """停止服务器"""
        if self.http_server:
            self.http_server.shutdown()
            self.http_server.server_close()
            self.logger.info("✅ 服务器已停止")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            'status': 'healthy',
            'app_name': self.config.name,
            'version': self.config.version,
            'debug': self.config.server.debug,
            'routes_count': len(self.router.get_routes())
        }