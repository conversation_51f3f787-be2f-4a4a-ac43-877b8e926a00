"""中间件系统

实现请求/响应处理管道，支持认证、日志、错误处理等功能。
"""

import time
import json
from typing import Callable, Dict, Any, Optional, List
from abc import ABC, abstractmethod
from http.server import BaseHTTPRequestHandler


class Request:
    """请求对象"""
    
    def __init__(self, handler: BaseHTTPRequestHandler, path_params: Dict[str, str] = None, query_params: Dict[str, Any] = None):
        self.handler = handler
        self.method = handler.command
        self.path = handler.path
        self.headers = dict(handler.headers)
        self.path_params = path_params or {}
        self.query_params = query_params or {}
        self._body = None
        self._json = None
    
    @property
    def body(self) -> bytes:
        """获取请求体"""
        if self._body is None:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                self._body = self.handler.rfile.read(content_length)
            else:
                self._body = b''
        return self._body
    
    @property
    def json(self) -> Optional[Dict[str, Any]]:
        """解析JSON请求体"""
        if self._json is None:
            content_type = self.headers.get('Content-Type', '')
            if 'application/json' in content_type:
                try:
                    self._json = json.loads(self.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    self._json = None
        return self._json
    
    def get_param(self, name: str, default: Any = None) -> Any:
        """获取参数（路径参数优先）"""
        return self.path_params.get(name, self.query_params.get(name, default))
    
    def get_header(self, name: str, default: str = None) -> Optional[str]:
        """获取请求头"""
        return self.headers.get(name, default)


class Response:
    """响应对象"""
    
    def __init__(self, content: str = '', status_code: int = 200, headers: Dict[str, str] = None, content_type: str = 'text/html; charset=utf-8'):
        self.content = content
        self.status_code = status_code
        self.headers = headers or {}
        self.content_type = content_type
        
        # 设置默认头
        if 'Content-Type' not in self.headers:
            self.headers['Content-Type'] = content_type
    
    def set_header(self, name: str, value: str):
        """设置响应头"""
        self.headers[name] = value
    
    def set_cookie(self, name: str, value: str, max_age: int = None, path: str = '/', secure: bool = False, http_only: bool = True):
        """设置Cookie"""
        cookie = f"{name}={value}; Path={path}"
        if max_age is not None:
            cookie += f"; Max-Age={max_age}"
        if secure:
            cookie += "; Secure"
        if http_only:
            cookie += "; HttpOnly"
        
        # 支持多个Cookie
        if 'Set-Cookie' in self.headers:
            if isinstance(self.headers['Set-Cookie'], list):
                self.headers['Set-Cookie'].append(cookie)
            else:
                self.headers['Set-Cookie'] = [self.headers['Set-Cookie'], cookie]
        else:
            self.headers['Set-Cookie'] = cookie
    
    @classmethod
    def json(cls, data: Any, status_code: int = 200, headers: Dict[str, str] = None):
        """创建JSON响应"""
        content = json.dumps(data, ensure_ascii=False, indent=2)
        return cls(content, status_code, headers, 'application/json; charset=utf-8')
    
    @classmethod
    def redirect(cls, url: str, status_code: int = 302):
        """创建重定向响应"""
        headers = {'Location': url}
        return cls('', status_code, headers)
    
    @classmethod
    def not_found(cls, message: str = '页面未找到'):
        """创建404响应"""
        content = f"""
<!DOCTYPE html>
<html>
<head><title>404 - 页面未找到</title></head>
<body>
    <h1>404 - 页面未找到</h1>
    <p>{message}</p>
</body>
</html>
        """.strip()
        return cls(content, 404)


class Middleware(ABC):
    """中间件基类"""
    
    @abstractmethod
    def process_request(self, request: Request) -> Optional[Response]:
        """处理请求
        
        返回None继续处理，返回Response直接响应
        """
        pass
    
    @abstractmethod
    def process_response(self, request: Request, response: Response) -> Response:
        """处理响应"""
        pass


class LoggingMiddleware(Middleware):
    """日志中间件"""
    
    def __init__(self, logger=None):
        import logging
        self.logger = logger or logging.getLogger(__name__)
    
    def process_request(self, request: Request) -> Optional[Response]:
        """记录请求日志"""
        request.start_time = time.time()
        self.logger.info(f"{request.method} {request.path} - 开始处理")
        return None
    
    def process_response(self, request: Request, response: Response) -> Response:
        """记录响应日志"""
        duration = time.time() - getattr(request, 'start_time', time.time())
        self.logger.info(f"{request.method} {request.path} - {response.status_code} ({duration:.3f}s)")
        return response


class CORSMiddleware(Middleware):
    """CORS中间件"""
    
    def __init__(self, allow_origins: List[str] = None, allow_methods: List[str] = None, allow_headers: List[str] = None):
        self.allow_origins = allow_origins or ['*']
        self.allow_methods = allow_methods or ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
        self.allow_headers = allow_headers or ['Content-Type', 'Authorization']
    
    def process_request(self, request: Request) -> Optional[Response]:
        """处理CORS预检请求"""
        if request.method == 'OPTIONS':
            response = Response('', 200)
            self._add_cors_headers(response)
            return response
        return None
    
    def process_response(self, request: Request, response: Response) -> Response:
        """添加CORS头"""
        self._add_cors_headers(response)
        return response
    
    def _add_cors_headers(self, response: Response):
        """添加CORS响应头"""
        response.set_header('Access-Control-Allow-Origin', ', '.join(self.allow_origins))
        response.set_header('Access-Control-Allow-Methods', ', '.join(self.allow_methods))
        response.set_header('Access-Control-Allow-Headers', ', '.join(self.allow_headers))
        response.set_header('Access-Control-Max-Age', '86400')


class SecurityMiddleware(Middleware):
    """安全中间件"""
    
    def process_request(self, request: Request) -> Optional[Response]:
        """安全检查"""
        # 这里可以添加安全检查逻辑
        return None
    
    def process_response(self, request: Request, response: Response) -> Response:
        """添加安全头"""
        response.set_header('X-Content-Type-Options', 'nosniff')
        response.set_header('X-Frame-Options', 'DENY')
        response.set_header('X-XSS-Protection', '1; mode=block')
        response.set_header('Referrer-Policy', 'strict-origin-when-cross-origin')
        return response


class CompressionMiddleware(Middleware):
    """压缩中间件"""
    
    def __init__(self, min_size: int = 1024):
        self.min_size = min_size
    
    def process_request(self, request: Request) -> Optional[Response]:
        """检查客户端是否支持压缩"""
        return None
    
    def process_response(self, request: Request, response: Response) -> Response:
        """压缩响应内容"""
        accept_encoding = request.get_header('Accept-Encoding', '')
        
        if ('gzip' in accept_encoding and 
            len(response.content.encode('utf-8')) >= self.min_size and
            'gzip' not in response.headers.get('Content-Encoding', '')):
            
            try:
                import gzip
                compressed_content = gzip.compress(response.content.encode('utf-8'))
                response.content = compressed_content.decode('latin-1')  # 保持字符串格式
                response.set_header('Content-Encoding', 'gzip')
                response.set_header('Content-Length', str(len(compressed_content)))
            except Exception:
                # 压缩失败时保持原内容
                pass
        
        return response


class MiddlewareManager:
    """中间件管理器"""
    
    def __init__(self):
        self.middleware_list: List[Middleware] = []
    
    def add(self, middleware: Middleware):
        """添加中间件"""
        self.middleware_list.append(middleware)
    
    def process_request(self, request: Request) -> Optional[Response]:
        """处理请求中间件"""
        for middleware in self.middleware_list:
            response = middleware.process_request(request)
            if response is not None:
                return response
        return None
    
    def process_response(self, request: Request, response: Response) -> Response:
        """处理响应中间件（逆序）"""
        for middleware in reversed(self.middleware_list):
            response = middleware.process_response(request, response)
        return response