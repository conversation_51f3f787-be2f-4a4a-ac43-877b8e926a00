"""剧本控制器

提供剧本项目查看、管理和详情展示功能。
"""

import json
from typing import Dict, Any, List, Optional
from controllers.base import BaseController
from core.middleware import Request, Response
from core.exceptions import NotFoundError, BadRequestError
from templates.engine import TemplateEngine
from templates.context import TemplateContext

# 导入现有的管理器
try:
    from producer.script_viewer import ScriptViewerManager, ScriptProjectInfo
except ImportError:
    # 如果导入失败，使用模拟类
    class ScriptProjectInfo:
        def __init__(self, project_id, title, theme='', era='', status='draft', 
                     created_at=None, updated_at=None):
            self.project_id = project_id
            self.title = title
            self.theme = theme
            self.era = era
            self.status = status
            self.created_at = created_at
            self.updated_at = updated_at
        
        def to_dict(self):
            return {
                'project_id': self.project_id,
                'title': self.title,
                'theme': self.theme,
                'era': self.era,
                'status': self.status,
                'created_at': self.created_at,
                'updated_at': self.updated_at
            }
    
    class ScriptViewerManager:
        def get_statistics(self):
            return {
                'total_projects': 0, 
                'completed_projects': 0, 
                'in_progress_projects': 0, 
                'draft_projects': 0
            }
        
        def get_projects(self):
            return []
        
        def get_project_by_id(self, project_id):
            return None


class ScriptsController(BaseController):
    """剧本控制器
    
    负责处理剧本项目的查看、管理和详情展示。
    """
    
    def __init__(self, config=None):
        super().__init__(config)
        self.template_engine = TemplateEngine(config)
        self.template_context = TemplateContext(config)
        self.script_manager = ScriptViewerManager()
        
        # 设置模板变量
        self.template_context.set_global('section', 'scripts')
        self.template_context.set_global('section_title', '剧本管理')
    
    def index(self, request: Request) -> Response:
        """剧本项目列表页面
        
        展示所有剧本项目的列表和统计信息。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: 包含剧本项目列表的HTML响应
        """
        try:
            # 获取分页参数
            pagination = self.get_pagination_params(request, default_page_size=12)
            
            # 获取筛选参数
            filter_status = request.get_param('status', 'all')  # all, draft, in_progress, completed
            search_query = request.get_param('search', '')
            sort_by = request.get_param('sort', 'updated_at')  # updated_at, created_at, title
            sort_order = request.get_param('order', 'desc')  # asc, desc
            
            # 获取数据
            stats = self.script_manager.get_statistics()
            all_projects = self.script_manager.get_projects()
            
            # 应用筛选和排序
            filtered_projects = self._filter_and_sort_projects(
                all_projects, filter_status, search_query, sort_by, sort_order
            )
            
            # 应用分页
            total_count = len(filtered_projects)
            start_idx = pagination['offset']
            end_idx = start_idx + pagination['page_size']
            paginated_projects = filtered_projects[start_idx:end_idx]
            
            # 计算分页信息
            total_pages = (total_count + pagination['page_size'] - 1) // pagination['page_size']
            
            # 准备模板上下文
            context = {
                'page_title': '剧本管理',
                'page_description': '查看和管理所有剧本项目',
                'stats': stats,
                'projects': paginated_projects,
                'filter_status': filter_status,
                'search_query': search_query,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'pagination': {
                    'current_page': pagination['page'],
                    'total_pages': total_pages,
                    'page_size': pagination['page_size'],
                    'total_count': total_count,
                    'has_prev': pagination['page'] > 1,
                    'has_next': pagination['page'] < total_pages
                },
                'status_options': [
                    {'value': 'all', 'label': '全部状态', 'active': filter_status == 'all'},
                    {'value': 'draft', 'label': '草稿', 'active': filter_status == 'draft'},
                    {'value': 'in_progress', 'label': '进行中', 'active': filter_status == 'in_progress'},
                    {'value': 'completed', 'label': '已完成', 'active': filter_status == 'completed'}
                ],
                'sort_options': [
                    {'value': 'updated_at', 'label': '更新时间', 'active': sort_by == 'updated_at'},
                    {'value': 'created_at', 'label': '创建时间', 'active': sort_by == 'created_at'},
                    {'value': 'title', 'label': '标题', 'active': sort_by == 'title'}
                ]
            }
            
            # 渲染模板
            html_content = self.template_engine.render('pages/scripts/index.html', context)
            
            return Response(
                content=html_content,
                content_type='text/html; charset=utf-8',
                status_code=200
            )
            
        except Exception as e:
            return self._render_error_page('加载剧本项目失败', str(e), 500)
    
    def detail(self, request: Request) -> Response:
        """剧本项目详情页面
        
        展示单个剧本项目的详细信息。
        
        Args:
            request: HTTP请求对象，应包含project_id参数
            
        Returns:
            Response: 包含剧本项目详情的HTML响应
        """
        try:
            # 获取项目ID参数
            project_id = request.get_param('project_id')
            if not project_id:
                raise BadRequestError('缺少project_id参数')
            
            # 获取项目信息
            project = self.script_manager.get_project_by_id(project_id)
            if not project:
                raise NotFoundError(f'剧本项目 {project_id} 不存在')
            
            # 准备模板上下文
            context = {
                'page_title': f'{project.title} - 剧本详情',
                'project': project,
                'project_data': self._format_project_data(project),
                'status_display': self._get_status_display(project.status),
                'breadcrumbs': [
                    {'title': '首页', 'url': '/'},
                    {'title': '剧本管理', 'url': '/scripts'},
                    {'title': project.title, 'url': None}
                ]
            }
            
            # 渲染模板
            html_content = self.template_engine.render('pages/scripts/detail.html', context)
            
            return Response(
                content=html_content,
                content_type='text/html; charset=utf-8',
                status_code=200
            )
            
        except (BadRequestError, NotFoundError) as e:
            return self._render_error_page('请求错误', str(e), e.status_code if hasattr(e, 'status_code') else 400)
        except Exception as e:
            return self._render_error_page('加载剧本项目详情失败', str(e), 500)
    
    def api_list(self, request: Request) -> Response:
        """剧本项目API - 列表
        
        返回JSON格式的剧本项目列表。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: JSON格式的剧本项目列表
        """
        try:
            projects = self.script_manager.get_projects()
            data = [p.to_dict() for p in projects]
            
            return self.success_response(data, '获取剧本项目列表成功')
            
        except Exception as e:
            return self.error_response(
                message='获取剧本项目列表失败',
                details=str(e),
                status_code=500
            )
    
    def api_detail(self, request: Request) -> Response:
        """剧本项目API - 详情
        
        返回JSON格式的单个剧本项目详情。
        
        Args:
            request: HTTP请求对象，应包含project_id参数
            
        Returns:
            Response: JSON格式的剧本项目详情
        """
        try:
            project_id = request.get_param('project_id')
            if not project_id:
                raise BadRequestError('缺少project_id参数')
            
            project = self.script_manager.get_project_by_id(project_id)
            if not project:
                raise NotFoundError(f'剧本项目 {project_id} 不存在')
            
            return self.success_response(project.to_dict(), '获取剧本项目详情成功')
            
        except (BadRequestError, NotFoundError) as e:
            return self.error_response(
                message=str(e),
                status_code=e.status_code if hasattr(e, 'status_code') else 400
            )
        except Exception as e:
            return self.error_response(
                message='获取剧本项目详情失败',
                details=str(e),
                status_code=500
            )
    
    def api_statistics(self, request: Request) -> Response:
        """剧本项目API - 统计信息
        
        返回JSON格式的剧本项目统计信息。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: JSON格式的统计信息
        """
        try:
            stats = self.script_manager.get_statistics()
            return self.success_response(stats, '获取统计信息成功')
            
        except Exception as e:
            return self.error_response(
                message='获取统计信息失败',
                details=str(e),
                status_code=500
            )
    
    def _filter_and_sort_projects(self, projects: List[ScriptProjectInfo], 
                                  filter_status: str, search_query: str,
                                  sort_by: str, sort_order: str) -> List[ScriptProjectInfo]:
        """筛选和排序剧本项目
        
        Args:
            projects: 原始项目列表
            filter_status: 状态筛选 (all, draft, in_progress, completed)
            search_query: 搜索关键词
            sort_by: 排序字段
            sort_order: 排序顺序 (asc, desc)
            
        Returns:
            List[ScriptProjectInfo]: 筛选和排序后的项目列表
        """
        filtered = projects
        
        # 按状态筛选
        if filter_status != 'all':
            filtered = [p for p in filtered if p.status == filter_status]
        
        # 按搜索关键词筛选
        if search_query:
            search_query = search_query.lower()
            filtered = [p for p in filtered if 
                       search_query in p.title.lower() or 
                       search_query in p.theme.lower() or
                       search_query in p.era.lower()]
        
        # 排序
        reverse = sort_order == 'desc'
        
        if sort_by == 'title':
            filtered.sort(key=lambda p: p.title.lower(), reverse=reverse)
        elif sort_by == 'created_at':
            filtered.sort(key=lambda p: p.created_at or '', reverse=reverse)
        elif sort_by == 'updated_at':
            filtered.sort(key=lambda p: p.updated_at or '', reverse=reverse)
        
        return filtered
    
    def _format_project_data(self, project: ScriptProjectInfo) -> str:
        """格式化项目数据为可读的JSON字符串
        
        Args:
            project: 项目信息对象
            
        Returns:
            str: 格式化后的JSON字符串
        """
        try:
            return json.dumps(project.to_dict(), ensure_ascii=False, indent=2)
        except Exception:
            return str(project.to_dict())
    
    def _get_status_display(self, status: str) -> Dict[str, str]:
        """获取状态显示信息
        
        Args:
            status: 状态值
            
        Returns:
            Dict[str, str]: 包含显示文本和CSS类的字典
        """
        status_map = {
            'draft': {'text': '草稿', 'class': 'status-draft'},
            'in_progress': {'text': '进行中', 'class': 'status-progress'},
            'completed': {'text': '已完成', 'class': 'status-completed'}
        }
        
        return status_map.get(status, {'text': status, 'class': 'status-unknown'})
    
    def _render_error_page(self, title: str, message: str, status_code: int) -> Response:
        """渲染错误页面
        
        Args:
            title: 错误标题
            message: 错误消息
            status_code: HTTP状态码
            
        Returns:
            Response: 错误页面响应
        """
        context = {
            'page_title': title,
            'error_message': message,
            'status_code': status_code,
            'breadcrumbs': [
                {'title': '首页', 'url': '/'},
                {'title': '剧本管理', 'url': '/scripts'},
                {'title': '错误', 'url': None}
            ]
        }
        
        error_html = self.template_engine.render('pages/error.html', context)
        
        return Response(
            content=error_html,
            content_type='text/html; charset=utf-8',
            status_code=status_code
        )