"""Web界面控制器

提供HTML页面渲染和Web界面功能。
"""

import os
import json
from typing import Dict, Any, Optional
from controllers.base import BaseController
from core.exceptions import NotFoundError
from templates.engine import TemplateEngine
from templates.context import TemplateContext

# 导入其他控制器以实现兼容性重定向
from .home import HomeController
from .results import ResultsController
from .scripts import ScriptsController


class WebController(BaseController):
    """Web界面控制器"""
    
    def __init__(self, config=None, base_path: str = None):
        super().__init__(config)
        self.base_path = base_path or os.getcwd()
        self.template_engine = TemplateEngine(config)
        self.template_context = TemplateContext(config)
        
        # 设置全局模板变量
        self.template_context.set_global('app_name', 'Producer File Manager')
        self.template_context.set_global('app_version', '1.0.0')
    
    def index(self, request) -> str:
        """首页"""
        try:
            # 获取当前路径
            current_path = self.get_query_param(request, 'path', '')
            safe_path = self._get_safe_path(current_path)
            
            # 获取目录内容
            if os.path.exists(safe_path) and os.path.isdir(safe_path):
                files = self._get_directory_contents(safe_path)
                breadcrumbs = self._get_breadcrumbs(current_path)
            else:
                files = []
                breadcrumbs = []
            
            # 模板上下文
            context = {
                'current_path': current_path,
                'safe_path': safe_path,
                'files': files,
                'breadcrumbs': breadcrumbs,
                'total_files': len(files),
                'page_title': 'File Manager',
                'show_hidden': self.get_query_param(request, 'show_hidden', 'false') == 'true'
            }
            
            return self.render_template('index.html', context)
            
        except Exception as e:
            return self.render_error_page('服务器内部错误', str(e), 500)
    
    def file_viewer(self, request) -> str:
        """文件查看器"""
        try:
            file_path = self.get_query_param(request, 'path')
            if not file_path:
                return self.render_error_page('错误', '缺少文件路径参数', 400)
            
            safe_path = self._get_safe_path(file_path)
            
            if not os.path.exists(safe_path):
                return self.render_error_page('文件不存在', f'文件 {file_path} 不存在', 404)
            
            if not os.path.isfile(safe_path):
                return self.render_error_page('错误', '指定路径不是文件', 400)
            
            # 获取文件信息
            file_info = self.format_file_info(safe_path)
            
            # 判断文件类型和是否可预览
            file_type = self._get_file_type(safe_path)
            can_preview = self._can_preview_file(safe_path)
            content = ''
            
            if can_preview and file_type == 'text':
                content = self._get_file_content(safe_path)
            
            # 模板上下文
            context = {
                'file_info': file_info,
                'file_path': file_path,
                'file_type': file_type,
                'can_preview': can_preview,
                'content': content,
                'page_title': f'查看文件 - {os.path.basename(file_path)}',
                'parent_path': os.path.dirname(file_path)
            }
            
            return self.render_template('file_viewer.html', context)
            
        except Exception as e:
            return self.render_error_page('服务器内部错误', str(e), 500)
    
    def upload_page(self, request) -> str:
        """文件上传页面"""
        try:
            target_path = self.get_query_param(request, 'path', '')
            safe_path = self._get_safe_path(target_path)
            
            if not os.path.exists(safe_path) or not os.path.isdir(safe_path):
                return self.render_error_page('错误', '目标目录不存在', 400)
            
            # 模板上下文
            context = {
                'target_path': target_path,
                'safe_path': safe_path,
                'page_title': '文件上传',
                'max_file_size': 100 * 1024 * 1024,  # 100MB
                'allowed_extensions': self._get_allowed_extensions()
            }
            
            return self.render_template('upload.html', context)
            
        except Exception as e:
            return self.render_error_page('服务器内部错误', str(e), 500)
    
    def settings_page(self, request) -> str:
        """设置页面"""
        try:
            # 获取当前设置
            settings = {
                'show_hidden_files': False,
                'default_view': 'list',
                'items_per_page': 50,
                'auto_refresh': False,
                'theme': 'light'
            }
            
            # 模板上下文
            context = {
                'settings': settings,
                'page_title': '设置',
                'themes': ['light', 'dark', 'auto'],
                'view_modes': ['list', 'grid', 'table']
            }
            
            return self.render_template('settings.html', context)
            
        except Exception as e:
            return self.render_error_page('服务器内部错误', str(e), 500)
    
    def about_page(self, request) -> str:
        """关于页面"""
        try:
            import platform
            import sys
            
            # 系统信息
            system_info = {
                'app_name': 'Producer File Manager',
                'app_version': '1.0.0',
                'python_version': sys.version,
                'platform': platform.platform(),
                'architecture': platform.architecture()[0]
            }
            
            # 模板上下文
            context = {
                'system_info': system_info,
                'page_title': '关于',
                'features': [
                    '文件浏览和管理',
                    'RESTful API接口',
                    '响应式Web界面',
                    '文件预览功能',
                    '系统监控',
                    '多格式支持'
                ]
            }
            
            return self.render_template('about.html', context)
            
        except Exception as e:
            return self.render_error_page('服务器内部错误', str(e), 500)
    
    def search_page(self, request) -> str:
        """搜索页面"""
        try:
            query = self.get_query_param(request, 'q', '')
            search_path = self.get_query_param(request, 'path', '')
            
            results = []
            if query:
                # 执行搜索
                safe_path = self._get_safe_path(search_path)
                results = self._search_files(safe_path, query)
            
            # 模板上下文
            context = {
                'query': query,
                'search_path': search_path,
                'results': results,
                'total_results': len(results),
                'page_title': f'搜索结果 - {query}' if query else '搜索'
            }
            
            return self.render_template('search.html', context)
            
        except Exception as e:
            return self.render_error_page('服务器内部错误', str(e), 500)
    
    def render_template(self, template_name: str, context: Dict[str, Any] = None) -> str:
        """渲染模板"""
        if context is None:
            context = {}
        
        # 合并全局上下文
        full_context = self.template_context.get_context()
        full_context.update(context)
        
        # 添加请求相关信息
        full_context.update({
            'current_time': self.template_context.format_datetime(),
            'base_url': '/',
            'static_url': '/static/'
        })
        
        return self.template_engine.render(template_name, full_context)
    
    def render_error_page(self, title: str, message: str, status_code: int = 500) -> str:
        """渲染错误页面"""
        context = {
            'error_title': title,
            'error_message': message,
            'status_code': status_code,
            'page_title': f'错误 {status_code}'
        }
        
        return self.render_template('error.html', context)
    
    def _get_safe_path(self, path: str) -> str:
        """获取安全路径"""
        if not path:
            return self.base_path
        
        # 移除危险字符
        path = path.replace('..', '').replace('//', '/')
        
        # 构建绝对路径
        if os.path.isabs(path):
            full_path = path
        else:
            full_path = os.path.join(self.base_path, path.lstrip('/'))
        
        # 规范化路径
        full_path = os.path.normpath(full_path)
        
        # 确保路径在基础目录内
        if not full_path.startswith(self.base_path):
            return self.base_path
        
        return full_path
    
    def _get_directory_contents(self, path: str) -> list:
        """获取目录内容"""
        files = []
        
        try:
            for item in sorted(os.listdir(path)):
                item_path = os.path.join(path, item)
                file_info = self.format_file_info(item_path)
                if file_info:
                    files.append(file_info)
        except PermissionError:
            pass
        
        # 按类型和名称排序（目录在前）
        files.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
        
        return files
    
    def _get_breadcrumbs(self, path: str) -> list:
        """获取面包屑导航"""
        breadcrumbs = [{'name': '首页', 'path': ''}]
        
        if path:
            parts = path.strip('/').split('/')
            current_path = ''
            
            for part in parts:
                if part:
                    current_path = f"{current_path}/{part}" if current_path else part
                    breadcrumbs.append({
                        'name': part,
                        'path': current_path
                    })
        
        return breadcrumbs
    
    def _get_file_type(self, file_path: str) -> str:
        """获取文件类型"""
        if not os.path.isfile(file_path):
            return 'directory'
        
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # 文本文件
        text_extensions = ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.yml', '.yaml']
        if ext in text_extensions:
            return 'text'
        
        # 图片文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        if ext in image_extensions:
            return 'image'
        
        # 视频文件
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']
        if ext in video_extensions:
            return 'video'
        
        # 音频文件
        audio_extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg']
        if ext in audio_extensions:
            return 'audio'
        
        return 'binary'
    
    def _can_preview_file(self, file_path: str) -> bool:
        """判断文件是否可以预览"""
        if not os.path.isfile(file_path):
            return False
        
        # 检查文件大小（避免预览过大文件）
        if os.path.getsize(file_path) > 1024 * 1024:  # 1MB
            return False
        
        file_type = self._get_file_type(file_path)
        return file_type in ['text', 'image']
    
    def _get_file_content(self, file_path: str, max_length: int = 10000) -> str:
        """获取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(max_length)
                if len(content) == max_length:
                    content += '\n\n... (文件内容过长，已截断)'
                return content
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read(max_length)
                    if len(content) == max_length:
                        content += '\n\n... (文件内容过长，已截断)'
                    return content
            except UnicodeDecodeError:
                return '无法读取文件内容（编码不支持）'
        except Exception as e:
            return f'读取文件时出错：{str(e)}'
    
    def _search_files(self, search_path: str, query: str) -> list:
        """搜索文件"""
        results = []
        query_lower = query.lower()
        
        try:
            for root, dirs, files in os.walk(search_path):
                # 搜索目录
                for dir_name in dirs:
                    if query_lower in dir_name.lower():
                        dir_path = os.path.join(root, dir_name)
                        file_info = self.format_file_info(dir_path)
                        if file_info:
                            # 计算相对路径
                            rel_path = os.path.relpath(dir_path, self.base_path)
                            file_info['relative_path'] = rel_path
                            results.append(file_info)
                
                # 搜索文件
                for file_name in files:
                    if query_lower in file_name.lower():
                        file_path = os.path.join(root, file_name)
                        file_info = self.format_file_info(file_path)
                        if file_info:
                            # 计算相对路径
                            rel_path = os.path.relpath(file_path, self.base_path)
                            file_info['relative_path'] = rel_path
                            results.append(file_info)
                
                # 限制结果数量
                if len(results) >= 100:
                    break
        
        except Exception:
            pass
        
        return results
    
    def _get_allowed_extensions(self) -> list:
        """获取允许的文件扩展名"""
        return [
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
            '.mp4', '.avi', '.mov', '.mp3', '.wav',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx',
            '.zip', '.rar', '.7z', '.tar', '.gz'
        ]
    
    def health_check(self, request) -> str:
        """健康检查接口
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: JSON格式的健康状态
        """
        try:
            health_data = {
                'status': 'healthy',
                'timestamp': self.template_context.format_datetime(),
                'version': '1.0.0',
                'services': {
                    'web': 'running',
                    'templates': 'available',
                    'file_system': 'accessible'
                }
            }
            
            return json.dumps(health_data, ensure_ascii=False, indent=2)
            
        except Exception as e:
            error_data = {
                'status': 'error',
                'message': '健康检查失败',
                'details': str(e),
                'timestamp': self.template_context.format_datetime()
            }
            return json.dumps(error_data, ensure_ascii=False, indent=2)
    
    def unified_viewer_compat(self, request) -> str:
        """统一查看器兼容性接口
        
        提供与原unified_viewer.py的兼容性，根据请求参数重定向到相应的新接口。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: 重定向页面或兼容性页面
        """
        try:
            # 获取请求参数
            action = self.get_query_param(request, 'action', 'home')
            view_type = self.get_query_param(request, 'type', 'html')
            
            # 根据action参数决定重定向目标
            if action == 'results' or action == 'test_results':
                if view_type == 'json':
                    redirect_url = '/api/results'
                else:
                    redirect_url = '/results'
            elif action == 'scripts' or action == 'script_projects':
                if view_type == 'json':
                    redirect_url = '/api/scripts'
                else:
                    redirect_url = '/scripts'
            elif action == 'health':
                redirect_url = '/api/health'
            else:
                # 默认重定向到首页
                redirect_url = '/'
            
            # 构建重定向页面
            context = {
                'page_title': 'Unified Viewer - 兼容性重定向',
                'redirect_url': redirect_url,
                'action': action,
                'view_type': view_type,
                'available_endpoints': [
                    {'url': '/', 'description': '主页 - 文件管理器'},
                    {'url': '/results', 'description': '测试结果管理'},
                    {'url': '/scripts', 'description': '剧本项目管理'},
                    {'url': '/api/health', 'description': '系统健康检查'},
                    {'url': '/upload', 'description': '文件上传'},
                    {'url': '/search', 'description': '文件搜索'}
                ],
                'migration_info': {
                    'old_endpoint': '/unified_viewer',
                    'new_framework': 'Producer Web Framework',
                    'migration_date': self.template_context.format_datetime()
                }
            }
            
            return self.render_template('compat/unified_viewer.html', context)
            
        except Exception as e:
            return self.render_error_page(
                '兼容性接口错误',
                f'处理兼容性请求时出错: {str(e)}',
                500
            )