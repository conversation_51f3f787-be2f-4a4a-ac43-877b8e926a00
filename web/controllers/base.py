"""基础控制器

提供所有控制器的通用功能和基础方法。
"""

from typing import Dict, Any, Optional
from core.middleware import Request, Response
from core.exceptions import BadRequestError, ValidationError


class BaseController:
    """基础控制器类"""
    
    def __init__(self, config=None):
        self.config = config
    
    def validate_required_params(self, request: Request, required_params: list) -> Dict[str, Any]:
        """验证必需参数"""
        params = {}
        missing_params = []
        
        for param in required_params:
            value = request.get_param(param)
            if value is None or value == '':
                missing_params.append(param)
            else:
                params[param] = value
        
        if missing_params:
            raise BadRequestError(f"缺少必需参数: {', '.join(missing_params)}")
        
        return params
    
    def validate_param_type(self, value: Any, param_name: str, expected_type: type, required: bool = True) -> Any:
        """验证参数类型"""
        if value is None:
            if required:
                raise ValidationError(param_name, "参数不能为空")
            return None
        
        if expected_type == int:
            try:
                return int(value)
            except (ValueError, TypeError):
                raise ValidationError(param_name, "必须是整数", value)
        
        elif expected_type == float:
            try:
                return float(value)
            except (ValueError, TypeError):
                raise ValidationError(param_name, "必须是数字", value)
        
        elif expected_type == bool:
            if isinstance(value, str):
                return value.lower() in ('true', '1', 'yes', 'on')
            return bool(value)
        
        elif expected_type == str:
            return str(value)
        
        else:
            if not isinstance(value, expected_type):
                raise ValidationError(param_name, f"类型错误，期望 {expected_type.__name__}", value)
            return value
    
    def get_pagination_params(self, request: Request, default_page_size: int = 20, max_page_size: int = 100) -> Dict[str, int]:
        """获取分页参数"""
        page = self.validate_param_type(
            request.get_param('page', 1), 
            'page', 
            int, 
            required=False
        )
        
        page_size = self.validate_param_type(
            request.get_param('page_size', default_page_size), 
            'page_size', 
            int, 
            required=False
        )
        
        # 验证范围
        if page < 1:
            raise ValidationError('page', "页码必须大于0", page)
        
        if page_size < 1:
            raise ValidationError('page_size', "页面大小必须大于0", page_size)
        
        if page_size > max_page_size:
            raise ValidationError('page_size', f"页面大小不能超过{max_page_size}", page_size)
        
        return {
            'page': page,
            'page_size': page_size,
            'offset': (page - 1) * page_size
        }
    
    def success_response(self, data: Any = None, message: str = "操作成功") -> Response:
        """成功响应"""
        response_data = {
            'success': True,
            'message': message,
            'data': data
        }
        return Response.json(response_data)
    
    def error_response(self, message: str, status_code: int = 400, details: Dict[str, Any] = None) -> Response:
        """错误响应"""
        response_data = {
            'success': False,
            'message': message,
            'details': details or {}
        }
        return Response.json(response_data, status_code)
    
    def paginated_response(self, items: list, total: int, page: int, page_size: int, message: str = "获取成功") -> Response:
        """分页响应"""
        total_pages = (total + page_size - 1) // page_size
        
        response_data = {
            'success': True,
            'message': message,
            'data': {
                'items': items,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        }
        return Response.json(response_data)
    
    def render_template(self, template_name: str, context: Dict[str, Any] = None) -> Response:
        """渲染模板"""
        # 这里将在模板系统实现后完善
        context = context or {}
        
        # 临时实现：返回简单HTML
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{context.get('title', 'Producer Web')}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <h1>模板: {template_name}</h1>
    <pre>{context}</pre>
</body>
</html>
        """.strip()
        
        return Response(html_content)
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头
        forwarded_for = request.get_header('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.get_header('X-Real-IP')
        if real_ip:
            return real_ip
        
        # 返回直连IP
        return request.handler.client_address[0]
    
    def get_user_agent(self, request: Request) -> str:
        """获取用户代理"""
        return request.get_header('User-Agent', 'Unknown')
    
    def is_ajax_request(self, request: Request) -> bool:
        """判断是否为AJAX请求"""
        return (
            request.get_header('X-Requested-With') == 'XMLHttpRequest' or
            'application/json' in request.get_header('Accept', '')
        )
    
    def redirect(self, url: str, status_code: int = 302) -> Response:
        """重定向"""
        return Response.redirect(url, status_code)
    
    def not_found(self, message: str = "请求的资源不存在") -> Response:
        """404响应"""
        return Response.not_found(message)