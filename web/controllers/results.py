"""测试结果控制器

提供测试结果查看、统计和详情展示功能。
"""

import json
from typing import Dict, Any, List, Optional
from controllers.base import BaseController
from core.middleware import Request, Response
from core.exceptions import NotFoundError, BadRequestError
from templates.engine import TemplateEngine
from templates.context import TemplateContext

# 导入现有的管理器
try:
    from producer.results_viewer import ResultsManager, TestResult
except ImportError:
    # 如果导入失败，使用模拟类
    class TestResult:
        def __init__(self, filename, timestamp, is_summary=False, is_detailed=False, data=None):
            self.filename = filename
            self.timestamp = timestamp
            self.is_summary = is_summary
            self.is_detailed = is_detailed
            self.data = data or {}
    
    class ResultsManager:
        def get_statistics(self):
            return {'total_tests': 0, 'success_rate': 0.0, 'summary_files': 0, 'detailed_files': 0}
        
        def get_results(self):
            return []
        
        def get_result_by_filename(self, filename):
            return None


class ResultsController(BaseController):
    """测试结果控制器
    
    负责处理测试结果的查看、统计和详情展示。
    """
    
    def __init__(self, config=None):
        super().__init__(config)
        self.template_engine = TemplateEngine(config)
        self.template_context = TemplateContext(config)
        self.results_manager = ResultsManager()
        
        # 设置模板变量
        self.template_context.set_global('section', 'results')
        self.template_context.set_global('section_title', '测试结果')
    
    def index(self, request: Request) -> Response:
        """测试结果列表页面
        
        展示所有测试结果的列表和统计信息。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: 包含测试结果列表的HTML响应
        """
        try:
            # 获取分页参数
            pagination = self.get_pagination_params(request, default_page_size=20)
            
            # 获取筛选参数
            filter_type = request.get_param('type', 'all')  # all, summary, detailed
            search_query = request.get_param('search', '')
            
            # 获取数据
            stats = self.results_manager.get_statistics()
            all_results = self.results_manager.get_results()
            
            # 应用筛选
            filtered_results = self._filter_results(all_results, filter_type, search_query)
            
            # 应用分页
            total_count = len(filtered_results)
            start_idx = pagination['offset']
            end_idx = start_idx + pagination['page_size']
            paginated_results = filtered_results[start_idx:end_idx]
            
            # 计算分页信息
            total_pages = (total_count + pagination['page_size'] - 1) // pagination['page_size']
            
            # 准备模板上下文
            context = {
                'page_title': '测试结果查看器',
                'page_description': '自动扫描并展示所有测试结果',
                'stats': stats,
                'results': paginated_results,
                'filter_type': filter_type,
                'search_query': search_query,
                'pagination': {
                    'current_page': pagination['page'],
                    'total_pages': total_pages,
                    'page_size': pagination['page_size'],
                    'total_count': total_count,
                    'has_prev': pagination['page'] > 1,
                    'has_next': pagination['page'] < total_pages
                },
                'filter_options': [
                    {'value': 'all', 'label': '全部', 'active': filter_type == 'all'},
                    {'value': 'summary', 'label': '摘要', 'active': filter_type == 'summary'},
                    {'value': 'detailed', 'label': '详细', 'active': filter_type == 'detailed'}
                ],
                # 新增：面包屑导航，提供返回首页入口（对齐 scripts 页：首页 / 剧本管理 / 测试结果）
                'breadcrumbs': [
                    {'title': '首页', 'url': '/'},
                    {'title': '剧本管理', 'url': '/scripts'},
                    {'title': '测试结果', 'url': None}
                ]
            }
            
            # 渲染模板
            html_content = self.template_engine.render('pages/results/index.html', context)
            
            return Response(
                content=html_content,
                content_type='text/html; charset=utf-8',
                status_code=200
            )
            
        except Exception as e:
            return self._render_error_page('加载测试结果失败', str(e), 500)
    
    def detail(self, request: Request) -> Response:
        """测试结果详情页面
        
        展示单个测试结果的详细信息。
        
        Args:
            request: HTTP请求对象，应包含filename参数
            
        Returns:
            Response: 包含测试结果详情的HTML响应
        """
        try:
            # 获取文件名参数
            filename = request.get_param('filename')
            if not filename:
                raise BadRequestError('缺少filename参数')
            
            # 获取测试结果
            result = self.results_manager.get_result_by_filename(filename)
            if not result:
                raise NotFoundError(f'测试结果文件 {filename} 不存在')
            
            # 准备模板上下文
            context = {
                'page_title': f'测试结果详情 - {filename}',
                'result': result,
                'filename': filename,
                'formatted_data': self._format_result_data(result.data),
                'breadcrumbs': [
                    {'title': '首页', 'url': '/'},
                    {'title': '测试结果', 'url': '/results'},
                    {'title': filename, 'url': None}
                ]
            }
            
            # 渲染模板
            html_content = self.template_engine.render('pages/results/detail.html', context)
            
            return Response(
                content=html_content,
                content_type='text/html; charset=utf-8',
                status_code=200
            )
            
        except (BadRequestError, NotFoundError) as e:
            return self._render_error_page('请求错误', str(e), e.status_code if hasattr(e, 'status_code') else 400)
        except Exception as e:
            return self._render_error_page('加载测试结果详情失败', str(e), 500)
    
    def api_list(self, request: Request) -> Response:
        """测试结果API - 列表
        
        返回JSON格式的测试结果列表。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: JSON格式的测试结果列表
        """
        try:
            results = self.results_manager.get_results()
            data = [{
                'filename': r.filename,
                'timestamp': r.timestamp.isoformat(),
                'is_summary': r.is_summary,
                'is_detailed': r.is_detailed
            } for r in results]
            
            return self.success_response(data, '获取测试结果列表成功')
            
        except Exception as e:
            return self.error_response(
                message='获取测试结果列表失败',
                details=str(e),
                status_code=500
            )
    
    def api_detail(self, request: Request) -> Response:
        """测试结果API - 详情
        
        返回JSON格式的单个测试结果详情。
        
        Args:
            request: HTTP请求对象，应包含filename参数
            
        Returns:
            Response: JSON格式的测试结果详情
        """
        try:
            filename = request.get_param('filename')
            if not filename:
                raise BadRequestError('缺少filename参数')
            
            result = self.results_manager.get_result_by_filename(filename)
            if not result:
                raise NotFoundError(f'测试结果文件 {filename} 不存在')
            
            return self.success_response(result.data, '获取测试结果详情成功')
            
        except (BadRequestError, NotFoundError) as e:
            return self.error_response(
                message=str(e),
                status_code=e.status_code if hasattr(e, 'status_code') else 400
            )
        except Exception as e:
            return self.error_response(
                message='获取测试结果详情失败',
                details=str(e),
                status_code=500
            )
    
    def api_statistics(self, request: Request) -> Response:
        """测试结果API - 统计信息
        
        返回JSON格式的测试结果统计信息。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: JSON格式的统计信息
        """
        try:
            stats = self.results_manager.get_statistics()
            return self.success_response(stats, '获取统计信息成功')
            
        except Exception as e:
            return self.error_response(
                message='获取统计信息失败',
                details=str(e),
                status_code=500
            )
    
    def _filter_results(self, results: List[TestResult], filter_type: str, search_query: str) -> List[TestResult]:
        """筛选测试结果
        
        Args:
            results: 原始结果列表
            filter_type: 筛选类型 (all, summary, detailed)
            search_query: 搜索关键词
            
        Returns:
            List[TestResult]: 筛选后的结果列表
        """
        filtered = results
        
        # 按类型筛选
        if filter_type == 'summary':
            filtered = [r for r in filtered if r.is_summary]
        elif filter_type == 'detailed':
            filtered = [r for r in filtered if r.is_detailed]
        
        # 按搜索关键词筛选
        if search_query:
            search_query = search_query.lower()
            filtered = [r for r in filtered if search_query in r.filename.lower()]
        
        return filtered
    
    def _format_result_data(self, data: Dict[str, Any]) -> str:
        """格式化结果数据为可读的JSON字符串
        
        Args:
            data: 原始数据字典
            
        Returns:
            str: 格式化后的JSON字符串
        """
        try:
            return json.dumps(data, ensure_ascii=False, indent=2)
        except Exception:
            return str(data)
    
    def _render_error_page(self, title: str, message: str, status_code: int) -> Response:
        """渲染错误页面
        
        Args:
            title: 错误标题
            message: 错误消息
            status_code: HTTP状态码
            
        Returns:
            Response: 错误页面响应
        """
        context = {
            'page_title': title,
            'error_message': message,
            'status_code': status_code,
            'breadcrumbs': [
                {'title': '首页', 'url': '/'},
                {'title': '测试结果', 'url': '/results'},
                {'title': '错误', 'url': None}
            ]
        }
        
        error_html = self.template_engine.render('pages/error.html', context)
        
        return Response(
            content=error_html,
            content_type='text/html; charset=utf-8',
            status_code=status_code
        )