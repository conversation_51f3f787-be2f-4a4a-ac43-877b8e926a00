"""主页控制器

提供统一查看器的主页功能，整合测试结果和剧本管理的导航界面。
"""

from typing import Dict, Any
from controllers.base import BaseController
from core.middleware import Request, Response
from templates.engine import TemplateEngine
from templates.context import TemplateContext
from datetime import datetime, timedelta

# 导入现有的管理器
try:
    from producer.results_viewer import ResultsManager
    from producer.script_viewer import ScriptViewerManager
except ImportError:
    # 如果导入失败，使用模拟类
    class ResultsManager:
        def get_statistics(self):
            return {'total_tests': 0, 'success_rate': 0.0, 'summary_files': 0, 'detailed_files': 0}
    
    class ScriptViewerManager:
        def get_statistics(self):
            return {'total_projects': 0, 'completed_projects': 0, 'in_progress_projects': 0, 'draft_projects': 0}


class HomeController(BaseController):
    """主页控制器
    
    负责渲染统一查看器的主页，展示测试结果和剧本管理的统计信息。
    """
    
    def __init__(self, config=None):
        super().__init__(config)
        self.template_engine = TemplateEngine(config)
        self.template_context = TemplateContext(config)
        
        # 初始化管理器
        self.results_manager = ResultsManager()
        self.script_manager = ScriptViewerManager()
        
        # 记录应用启动时间，用于计算运行时长
        self.start_time = datetime.now()
        
        # 设置全局模板变量
        self.template_context.set_global('app_name', 'Producer统一查看器')
        self.template_context.set_global('app_version', '2.0.0')
    
    def _get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态
        
        返回用于首页展示的健康状态字典，包含：
        - status: 'healthy' 或 'unhealthy'
        - message: 友好的状态描述
        - details: 组件级状态详情
        """
        results_ok = True
        scripts_ok = True
        
        try:
            self.results_manager.get_statistics()
        except Exception:
            results_ok = False
        
        try:
            self.script_manager.get_statistics()
        except Exception:
            scripts_ok = False
        
        overall_ok = results_ok and scripts_ok
        status = 'healthy' if overall_ok else 'unhealthy'
        
        if overall_ok:
            message = '所有系统组件正常运行'
        elif results_ok and not scripts_ok:
            message = '剧本管理组件异常，其他组件正常'
        elif scripts_ok and not results_ok:
            message = '测试结果组件异常，其他组件正常'
        else:
            message = '多个组件异常，请检查系统'
        
        return {
            'status': status,
            'message': message,
            'details': {
                'results_manager': 'ok' if results_ok else 'error',
                'script_manager': 'ok' if scripts_ok else 'error'
            }
        }
    
    def _format_uptime(self, delta: timedelta) -> str:
        """格式化运行时长
        
        将时间差格式化为人类可读的字符串（例如："2天 3小时 15分钟"）。
        """
        seconds = int(delta.total_seconds())
        days, seconds = divmod(seconds, 86400)
        hours, seconds = divmod(seconds, 3600)
        minutes, _ = divmod(seconds, 60)
        parts = []
        if days:
            parts.append(f"{days}天")
        if hours:
            parts.append(f"{hours}小时")
        if minutes or not parts:
            parts.append(f"{minutes}分钟")
        return ' '.join(parts)
    
    def _get_homepage_stats(self, results_stats: Dict[str, Any], scripts_stats: Dict[str, Any]) -> Dict[str, Any]:
        """计算首页统计数据
        
        基于各模块统计信息聚合得到：
        - test_results: 测试结果数量
        - script_projects: 剧本项目数量
        - uptime: 系统运行时间
        - last_update: 数据最后更新时间字符串
        """
        now = datetime.now()
        uptime = self._format_uptime(now - self.start_time)
        
        return {
            'test_results': results_stats.get('total_tests', 0),
            'script_projects': scripts_stats.get('total_projects', 0),
            'uptime': uptime,
            'last_update': now.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def index(self, request: Request) -> Response:
        """主页
        
        展示统一查看器的导航界面，包含测试结果和剧本管理的统计信息。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: 包含主页HTML的响应
        """
        try:
            # 获取统计信息
            results_stats = self.results_manager.get_statistics()
            scripts_stats = self.script_manager.get_statistics()
            
            # 系统健康与首页聚合统计
            system_health = self._get_system_health()
            summary_stats = self._get_homepage_stats(results_stats, scripts_stats)
            
            # 准备模板上下文
            context = {
                'page_title': 'Producer统一查看器',
                'page_description': '统一的测试结果和剧本查看平台',
                'results_stats': results_stats,
                'scripts_stats': scripts_stats,
                'system_health': system_health,
                'stats': summary_stats,
                'nav_items': [
                    {
                        'title': '测试结果',
                        'description': '查看所有测试结果和统计信息',
                        'url': '/results',
                        'icon': '🧪',
                        'stats': [
                            {'label': '总测试次数', 'value': results_stats.get('total_tests', 0)},
                            {'label': '成功率', 'value': f"{results_stats.get('success_rate', 0):.1f}%"}
                        ],
                        'color_scheme': 'blue'
                    },
                    {
                        'title': '剧本管理',
                        'description': '查看和管理所有剧本项目',
                        'url': '/scripts',
                        'icon': '📝',
                        'stats': [
                            {'label': '总项目数', 'value': scripts_stats.get('total_projects', 0)},
                            {'label': '已完成', 'value': scripts_stats.get('completed_projects', 0)}
                        ],
                        'color_scheme': 'green'
                    }
                ]
            }
            
            # 渲染模板
            html_content = self.template_engine.render('pages/home/<USER>', context)
            
            return Response(
                content=html_content,
                content_type='text/html; charset=utf-8',
                status_code=200
            )
            
        except Exception as e:
            # 打印详细错误信息用于调试
            import traceback
            print(f"HomeController.index 错误: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            print(f"错误堆栈: {traceback.format_exc()}")
            
            # 返回简单的错误响应
            error_html = f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>服务器错误</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>服务器错误</h1>
                <p>加载主页时发生错误: {str(e)}</p>
                <p>错误类型: {type(e).__name__}</p>
                <pre>{traceback.format_exc()}</pre>
            </body>
            </html>
            '''
            
            return Response(
                content=error_html,
                content_type='text/html; charset=utf-8',
                status_code=500
            )
    
    def health_check(self, request: Request) -> Response:
        """健康检查接口
        
        用于检查服务状态和各个组件的可用性。
        
        Args:
            request: HTTP请求对象
            
        Returns:
            Response: JSON格式的健康状态响应
        """
        try:
            # 检查各个组件状态
            results_status = 'ok'
            scripts_status = 'ok'
            
            try:
                self.results_manager.get_statistics()
            except Exception:
                results_status = 'error'
            
            try:
                self.script_manager.get_statistics()
            except Exception:
                scripts_status = 'error'
            
            health_data = {
                'status': 'ok' if results_status == 'ok' and scripts_status == 'ok' else 'degraded',
                'timestamp': request.timestamp.isoformat() if hasattr(request, 'timestamp') else None,
                'components': {
                    'results_manager': results_status,
                    'script_manager': scripts_status,
                    'template_engine': 'ok'
                },
                'version': '2.0.0'
            }
            
            return self.success_response(health_data, '系统状态正常')
            
        except Exception as e:
            return self.error_response(
                message='健康检查失败',
                details=str(e),
                status_code=500
            )