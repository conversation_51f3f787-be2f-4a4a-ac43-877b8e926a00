"""路由配置

定义所有Web应用的路由规则和控制器映射。
"""

from core.router import Router
from controllers.home import HomeController
from controllers.results import ResultsController
from controllers.scripts import ScriptsController
from controllers.web import WebController


def setup_routes(router: Router, config=None):
    """设置应用路由
    
    Args:
        router: 路由器实例
        config: 应用配置
    """
    
    # 创建控制器实例
    home_controller = HomeController(config)
    results_controller = ResultsController(config)
    scripts_controller = ScriptsController(config)
    web_controller = WebController(config)
    
    # 主页路由
    router.add_route('/', home_controller.index, ['GET'], 'home')
    router.add_route('/health', home_controller.health_check, ['GET'], 'health')
    
    # 测试结果路由组
    results_group = router.group('/results')
    results_group.add_route('', results_controller.index, ['GET'], 'results.index')
    results_group.add_route('/', results_controller.index, ['GET'], 'results.index_slash')
    results_group.add_route('/detail', results_controller.detail, ['GET'], 'results.detail')
    
    # 测试结果API路由组
    api_results_group = router.group('/api/results')
    api_results_group.add_route('', results_controller.api_list, ['GET'], 'api.results.list')
    api_results_group.add_route('/', results_controller.api_list, ['GET'], 'api.results.list_slash')
    api_results_group.add_route('/<test_id>', results_controller.api_detail, ['GET'], 'api.results.detail')
    api_results_group.add_route('/statistics', results_controller.api_statistics, ['GET'], 'api.results.statistics')
    
    # 剧本项目路由组
    scripts_group = router.group('/scripts')
    scripts_group.add_route('', scripts_controller.index, ['GET'], 'scripts.index')
    scripts_group.add_route('/', scripts_controller.index, ['GET'], 'scripts.index_slash')
    scripts_group.add_route('/detail', scripts_controller.detail, ['GET'], 'scripts.detail')
    
    # 剧本项目API路由组
    api_scripts_group = router.group('/api/scripts')
    api_scripts_group.add_route('', scripts_controller.api_list, ['GET'], 'api.scripts.list')
    api_scripts_group.add_route('/', scripts_controller.api_list, ['GET'], 'api.scripts.list_slash')
    api_scripts_group.add_route('/<project_id>', scripts_controller.api_detail, ['GET'], 'api.scripts.detail')
    api_scripts_group.add_route('/statistics', scripts_controller.api_statistics, ['GET'], 'api.scripts.statistics')
    
    # 系统API路由
    api_group = router.group('/api')
    api_group.add_route('/health', home_controller.health_check, ['GET'], 'api.health')
    
    # 兼容性路由 - 保持与原unified_viewer的兼容性
    compat_group = router.group('/unified_viewer')
    compat_group.add_route('', web_controller.unified_viewer_compat, ['GET'], 'compat.unified_viewer')
    compat_group.add_route('/', web_controller.unified_viewer_compat, ['GET'], 'compat.unified_viewer_slash')
    
    # 静态文件路由（如果需要）
    # router.add_route('/static/<path:filename>', static_file_handler, ['GET'], 'static')
    
    return router


def get_route_info(router: Router):
    """获取路由信息
    
    Args:
        router: 路由器实例
        
    Returns:
        dict: 路由信息字典
    """
    routes_info = []
    
    for route in router.routes:
        routes_info.append({
            'path': route.path,
            'methods': route.methods,
            'name': route.name,
            'handler': f"{route.handler.__module__}.{route.handler.__qualname__}"
        })
    
    return {
        'total_routes': len(routes_info),
        'routes': routes_info
    }


def print_routes(router: Router):
    """打印所有路由信息
    
    Args:
        router: 路由器实例
    """
    print("\n=== 路由信息 ===")
    print(f"总路由数: {len(router.routes)}")
    print("\n路由列表:")
    
    for route in router.routes:
        methods_str = ', '.join(route.methods)
        handler_name = f"{route.handler.__module__}.{route.handler.__qualname__}"
        name_str = f" ({route.name})" if route.name else ""
        
        print(f"  {route.path:<30} [{methods_str:<15}] -> {handler_name}{name_str}")
    
    print("\n=== 路由组信息 ===")
    print(f"总路由组数: {len(router.groups)}")
    
    for i, group in enumerate(router.groups):
        print(f"\n路由组 {i+1}:")
        print(f"  前缀: {group.prefix}")
        print(f"  中间件数: {len(group.middleware)}")
        print(f"  路由数: {len(group.routes)}")
        
        for route in group.routes:
            methods_str = ', '.join(route.methods)
            handler_name = f"{route.handler.__module__}.{route.handler.__qualname__}"
            name_str = f" ({route.name})" if route.name else ""
            
            print(f"    {route.path:<28} [{methods_str:<15}] -> {handler_name}{name_str}")
    
    print("\n==================\n")