"""模板过滤器

为Jinja2模板引擎提供自定义过滤器函数。
"""

import datetime
import os
from typing import Any, Optional
from urllib.parse import quote, unquote


def register_custom_filters(env):
    """注册自定义过滤器到Jinja2环境
    
    Args:
        env: Jinja2环境实例
    """
    # 时间格式化过滤器
    env.filters['datetime'] = format_datetime
    env.filters['date'] = format_date
    env.filters['time'] = format_time
    env.filters['timeago'] = time_ago
    
    # 文件相关过滤器
    env.filters['filesize'] = format_filesize
    env.filters['filename'] = get_filename
    env.filters['file_ext'] = get_file_extension
    env.filters['basename'] = get_basename
    
    # 文本处理过滤器
    env.filters['truncate_words'] = truncate_words
    env.filters['slugify'] = slugify
    env.filters['highlight'] = highlight_text
    env.filters['nl2br'] = newlines_to_br
    
    # URL相关过滤器
    env.filters['urlencode'] = url_encode
    env.filters['urldecode'] = url_decode
    
    # 数据格式化过滤器
    env.filters['json_pretty'] = json_pretty
    env.filters['pluralize'] = pluralize
    env.filters['yesno'] = yes_no
    
    # 安全相关过滤器
    env.filters['mask_sensitive'] = mask_sensitive_data


def format_datetime(value: Any, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """格式化日期时间
    
    Args:
        value: 日期时间值
        format_str: 格式字符串
        
    Returns:
        str: 格式化后的日期时间字符串
    """
    if not value:
        return ''
    
    if isinstance(value, str):
        try:
            value = datetime.datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            return str(value)
    
    if isinstance(value, datetime.datetime):
        return value.strftime(format_str)
    elif isinstance(value, datetime.date):
        return value.strftime(format_str)
    
    return str(value)


def format_date(value: Any, format_str: str = '%Y-%m-%d') -> str:
    """格式化日期
    
    Args:
        value: 日期值
        format_str: 格式字符串
        
    Returns:
        str: 格式化后的日期字符串
    """
    return format_datetime(value, format_str)


def format_time(value: Any, format_str: str = '%H:%M:%S') -> str:
    """格式化时间
    
    Args:
        value: 时间值
        format_str: 格式字符串
        
    Returns:
        str: 格式化后的时间字符串
    """
    return format_datetime(value, format_str)


def time_ago(value: Any) -> str:
    """显示相对时间（多久之前）
    
    Args:
        value: 日期时间值
        
    Returns:
        str: 相对时间描述
    """
    if not value:
        return ''
    
    if isinstance(value, str):
        try:
            value = datetime.datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            return str(value)
    
    if not isinstance(value, datetime.datetime):
        return str(value)
    
    now = datetime.datetime.now()
    if value.tzinfo:
        now = now.replace(tzinfo=value.tzinfo)
    
    diff = now - value
    
    if diff.days > 0:
        return f'{diff.days}天前'
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f'{hours}小时前'
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f'{minutes}分钟前'
    else:
        return '刚刚'


def format_filesize(size: int) -> str:
    """格式化文件大小
    
    Args:
        size: 文件大小（字节）
        
    Returns:
        str: 格式化后的文件大小
    """
    if not isinstance(size, (int, float)):
        return str(size)
    
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size < 1024.0:
            return f'{size:.1f} {unit}'
        size /= 1024.0
    return f'{size:.1f} PB'


def get_filename(path: str) -> str:
    """获取文件名
    
    Args:
        path: 文件路径
        
    Returns:
        str: 文件名
    """
    return os.path.basename(path) if path else ''


def get_file_extension(filename: str) -> str:
    """获取文件扩展名
    
    Args:
        filename: 文件名
        
    Returns:
        str: 文件扩展名（不含点号）
    """
    if not filename:
        return ''
    return os.path.splitext(filename)[1].lstrip('.')


def get_basename(path: str) -> str:
    """获取不含扩展名的文件名
    
    Args:
        path: 文件路径
        
    Returns:
        str: 不含扩展名的文件名
    """
    if not path:
        return ''
    filename = os.path.basename(path)
    return os.path.splitext(filename)[0]


def truncate_words(text: str, length: int = 50, suffix: str = '...') -> str:
    """按单词截断文本
    
    Args:
        text: 原始文本
        length: 最大单词数
        suffix: 截断后缀
        
    Returns:
        str: 截断后的文本
    """
    if not text:
        return ''
    
    words = text.split()
    if len(words) <= length:
        return text
    
    return ' '.join(words[:length]) + suffix


def slugify(text: str) -> str:
    """将文本转换为URL友好的slug
    
    Args:
        text: 原始文本
        
    Returns:
        str: URL友好的slug
    """
    if not text:
        return ''
    
    import re
    # 转换为小写并替换空格和特殊字符
    slug = re.sub(r'[^\w\s-]', '', text.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')


def highlight_text(text: str, query: str, tag: str = 'mark') -> str:
    """高亮显示文本中的关键词
    
    Args:
        text: 原始文本
        query: 搜索关键词
        tag: HTML标签名
        
    Returns:
        str: 高亮后的HTML文本
    """
    if not text or not query:
        return text
    
    import re
    pattern = re.compile(re.escape(query), re.IGNORECASE)
    return pattern.sub(f'<{tag}>\\g<0></{tag}>', text)


def newlines_to_br(text: str) -> str:
    """将换行符转换为HTML <br> 标签
    
    Args:
        text: 原始文本
        
    Returns:
        str: 转换后的HTML文本
    """
    if not text:
        return ''
    return text.replace('\n', '<br>')


def url_encode(text: str) -> str:
    """URL编码
    
    Args:
        text: 原始文本
        
    Returns:
        str: URL编码后的文本
    """
    return quote(str(text)) if text else ''


def url_decode(text: str) -> str:
    """URL解码
    
    Args:
        text: URL编码的文本
        
    Returns:
        str: 解码后的文本
    """
    return unquote(str(text)) if text else ''


def json_pretty(data: Any, indent: int = 2) -> str:
    """格式化JSON数据
    
    Args:
        data: 要格式化的数据
        indent: 缩进空格数
        
    Returns:
        str: 格式化后的JSON字符串
    """
    import json
    try:
        return json.dumps(data, ensure_ascii=False, indent=indent)
    except (TypeError, ValueError):
        return str(data)


def pluralize(count: int, singular: str, plural: Optional[str] = None) -> str:
    """根据数量返回单数或复数形式
    
    Args:
        count: 数量
        singular: 单数形式
        plural: 复数形式（可选）
        
    Returns:
        str: 单数或复数形式的文本
    """
    if count == 1:
        return singular
    
    if plural is None:
        # 简单的英文复数规则
        if singular.endswith(('s', 'sh', 'ch', 'x', 'z')):
            plural = singular + 'es'
        elif singular.endswith('y') and len(singular) > 1 and singular[-2] not in 'aeiou':
            plural = singular[:-1] + 'ies'
        else:
            plural = singular + 's'
    
    return plural


def yes_no(value: Any, yes_text: str = '是', no_text: str = '否') -> str:
    """将布尔值转换为是/否文本
    
    Args:
        value: 布尔值或可转换为布尔值的值
        yes_text: 真值对应的文本
        no_text: 假值对应的文本
        
    Returns:
        str: 是/否文本
    """
    return yes_text if value else no_text


def mask_sensitive_data(text: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """遮蔽敏感数据
    
    Args:
        text: 原始文本
        mask_char: 遮蔽字符
        visible_chars: 可见字符数（前后各显示的字符数）
        
    Returns:
        str: 遮蔽后的文本
    """
    if not text or len(text) <= visible_chars * 2:
        return text
    
    visible_start = text[:visible_chars]
    visible_end = text[-visible_chars:]
    mask_length = len(text) - visible_chars * 2
    
    return f'{visible_start}{mask_char * mask_length}{visible_end}'