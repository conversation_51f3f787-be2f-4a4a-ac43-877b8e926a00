"""模板组件系统

提供可复用的UI组件，支持组件化开发。
"""

import os
from typing import Dict, Any, Optional, List
from .engine import TemplateEngine
from .context import TemplateContext


class Component:
    """基础组件类"""
    
    def __init__(self, name: str, template_path: str = None):
        self.name = name
        self.template_path = template_path or f"components/{name}.html"
        self.props = {}
        self.slots = {}
    
    def render(self, engine: TemplateEngine, context: Dict[str, Any] = None, **props) -> str:
        """渲染组件"""
        # 合并属性
        render_context = {}
        if context:
            render_context.update(context)
        render_context.update(self.props)
        render_context.update(props)
        
        # 添加组件特定的上下文
        render_context['component'] = {
            'name': self.name,
            'props': props,
            'slots': self.slots
        }
        
        return engine.render(self.template_path, render_context)
    
    def set_prop(self, key: str, value: Any):
        """设置属性"""
        self.props[key] = value
        return self
    
    def set_slot(self, name: str, content: str):
        """设置插槽内容"""
        self.slots[name] = content
        return self


class ComponentRegistry:
    """组件注册表"""
    
    def __init__(self):
        self._components = {}
        self._register_builtin_components()
    
    def register(self, name: str, component_class: type = None, template_path: str = None):
        """注册组件"""
        if component_class:
            self._components[name] = component_class
        else:
            self._components[name] = lambda: Component(name, template_path)
    
    def get(self, name: str) -> Component:
        """获取组件实例"""
        if name not in self._components:
            raise ValueError(f"Component '{name}' not found")
        
        component_factory = self._components[name]
        if callable(component_factory):
            return component_factory()
        return component_factory
    
    def _register_builtin_components(self):
        """注册内置组件"""
        # 基础组件
        self.register('button', template_path='components/button.html')
        self.register('card', template_path='components/card.html')
        self.register('modal', template_path='components/modal.html')
        self.register('alert', template_path='components/alert.html')
        self.register('breadcrumb', template_path='components/breadcrumb.html')
        self.register('pagination', template_path='components/pagination.html')
        
        # 表单组件
        self.register('form', template_path='components/form.html')
        self.register('input', template_path='components/input.html')
        self.register('select', template_path='components/select.html')
        self.register('textarea', template_path='components/textarea.html')
        self.register('checkbox', template_path='components/checkbox.html')
        self.register('radio', template_path='components/radio.html')
        
        # 布局组件
        self.register('layout', template_path='components/layout.html')
        self.register('header', template_path='components/header.html')
        self.register('footer', template_path='components/footer.html')
        self.register('sidebar', template_path='components/sidebar.html')
        self.register('container', template_path='components/container.html')
        
        # 数据展示组件
        self.register('table', template_path='components/table.html')
        self.register('list', template_path='components/list.html')
        self.register('tree', template_path='components/tree.html')
        self.register('tabs', template_path='components/tabs.html')
        
        # 文件相关组件
        self.register('file_icon', FileIconComponent)
        self.register('file_list', FileListComponent)
        self.register('file_preview', FilePreviewComponent)
        self.register('directory_tree', DirectoryTreeComponent)


class FileIconComponent(Component):
    """文件图标组件"""
    
    def __init__(self):
        super().__init__('file_icon', 'components/file_icon.html')
        self.icon_map = {
            # 图片
            'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'gif': 'image',
            'bmp': 'image', 'webp': 'image', 'svg': 'image',
            
            # 视频
            'mp4': 'video', 'avi': 'video', 'mov': 'video', 'wmv': 'video',
            'flv': 'video', 'webm': 'video', 'mkv': 'video',
            
            # 音频
            'mp3': 'audio', 'wav': 'audio', 'flac': 'audio', 'aac': 'audio',
            'ogg': 'audio', 'm4a': 'audio',
            
            # 文档
            'pdf': 'pdf', 'doc': 'document', 'docx': 'document',
            'xls': 'spreadsheet', 'xlsx': 'spreadsheet',
            'ppt': 'presentation', 'pptx': 'presentation',
            'txt': 'text', 'md': 'text', 'rtf': 'text',
            
            # 代码
            'py': 'code', 'js': 'code', 'html': 'code', 'css': 'code',
            'java': 'code', 'cpp': 'code', 'c': 'code', 'php': 'code',
            'rb': 'code', 'go': 'code', 'rs': 'code', 'swift': 'code',
            
            # 压缩文件
            'zip': 'archive', 'rar': 'archive', '7z': 'archive',
            'tar': 'archive', 'gz': 'archive', 'bz2': 'archive',
        }
    
    def render(self, engine: TemplateEngine, context: Dict[str, Any] = None, **props) -> str:
        filename = props.get('filename', '')
        file_type = props.get('type', 'file')
        
        if file_type == 'directory':
            icon_type = 'folder'
        else:
            ext = filename.split('.')[-1].lower() if '.' in filename else ''
            icon_type = self.icon_map.get(ext, 'file')
        
        props['icon_type'] = icon_type
        return super().render(engine, context, **props)


class FileListComponent(Component):
    """文件列表组件"""
    
    def __init__(self):
        super().__init__('file_list', 'components/file_list.html')
    
    def render(self, engine: TemplateEngine, context: Dict[str, Any] = None, **props) -> str:
        files = props.get('files', [])
        show_size = props.get('show_size', True)
        show_date = props.get('show_date', True)
        show_type = props.get('show_type', True)
        
        # 处理文件数据
        processed_files = []
        for file_info in files:
            if isinstance(file_info, dict):
                processed_files.append(file_info)
            else:
                # 如果是字符串，转换为字典
                processed_files.append({
                    'name': str(file_info),
                    'type': 'file',
                    'size': 0,
                    'modified': None
                })
        
        props.update({
            'files': processed_files,
            'show_size': show_size,
            'show_date': show_date,
            'show_type': show_type
        })
        
        return super().render(engine, context, **props)


class FilePreviewComponent(Component):
    """文件预览组件"""
    
    def __init__(self):
        super().__init__('file_preview', 'components/file_preview.html')
    
    def render(self, engine: TemplateEngine, context: Dict[str, Any] = None, **props) -> str:
        filename = props.get('filename', '')
        file_path = props.get('file_path', '')
        file_type = props.get('file_type', 'unknown')
        
        # 根据文件类型确定预览方式
        preview_type = self._get_preview_type(filename, file_type)
        props['preview_type'] = preview_type
        
        return super().render(engine, context, **props)
    
    def _get_preview_type(self, filename: str, file_type: str) -> str:
        """确定预览类型"""
        if not filename:
            return 'none'
        
        ext = filename.split('.')[-1].lower() if '.' in filename else ''
        
        # 图片预览
        if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']:
            return 'image'
        
        # 视频预览
        if ext in ['mp4', 'webm', 'ogg']:
            return 'video'
        
        # 音频预览
        if ext in ['mp3', 'wav', 'ogg', 'm4a']:
            return 'audio'
        
        # 文本预览
        if ext in ['txt', 'md', 'py', 'js', 'html', 'css', 'json', 'xml', 'yml', 'yaml']:
            return 'text'
        
        # PDF预览
        if ext == 'pdf':
            return 'pdf'
        
        return 'download'


class DirectoryTreeComponent(Component):
    """目录树组件"""
    
    def __init__(self):
        super().__init__('directory_tree', 'components/directory_tree.html')
    
    def render(self, engine: TemplateEngine, context: Dict[str, Any] = None, **props) -> str:
        tree_data = props.get('tree_data', [])
        current_path = props.get('current_path', '')
        expandable = props.get('expandable', True)
        
        # 处理树形数据
        processed_tree = self._process_tree_data(tree_data, current_path)
        
        props.update({
            'tree_data': processed_tree,
            'current_path': current_path,
            'expandable': expandable
        })
        
        return super().render(engine, context, **props)
    
    def _process_tree_data(self, tree_data: List[Dict], current_path: str) -> List[Dict]:
        """处理树形数据"""
        processed = []
        
        for item in tree_data:
            processed_item = item.copy()
            
            # 标记当前路径
            item_path = item.get('path', '')
            processed_item['is_current'] = item_path == current_path
            processed_item['is_parent'] = current_path.startswith(item_path + '/')
            
            # 递归处理子项
            if 'children' in item and item['children']:
                processed_item['children'] = self._process_tree_data(
                    item['children'], current_path
                )
            
            processed.append(processed_item)
        
        return processed


# 全局组件注册表实例
component_registry = ComponentRegistry()


def render_component(name: str, engine: TemplateEngine, context: Dict[str, Any] = None, **props) -> str:
    """渲染组件的便捷函数"""
    component = component_registry.get(name)
    return component.render(engine, context, **props)


def register_component(name: str, component_class: type = None, template_path: str = None):
    """注册组件的便捷函数"""
    component_registry.register(name, component_class, template_path)