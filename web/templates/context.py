"""模板上下文

管理模板渲染时的上下文变量和全局函数。
"""

import datetime
from typing import Dict, Any, Optional
from urllib.parse import urlencode


class TemplateContext:
    """模板上下文管理器"""
    
    def __init__(self, config=None):
        self.config = config
        self._context = {}
        self._setup_globals()
    
    def _setup_globals(self):
        """设置全局变量和函数"""
        self._context.update({
            # 应用信息
            'app_name': getattr(self.config, 'APP_NAME', 'Producer Web'),
            'app_version': getattr(self.config, 'APP_VERSION', '1.0.0'),
            'debug': getattr(self.config, 'DEBUG', False),
            
            # 时间相关
            'now': datetime.datetime.now(),
            'today': datetime.date.today(),
            
            # 工具函数
            'url_for': self.url_for,
            'static_url': self.static_url,
            'format_datetime': self.format_datetime,
            'format_date': self.format_date,
            'format_time': self.format_time,
            'format_filesize': self.format_filesize,
            'truncate': self.truncate,
            'build_query_string': self.build_query_string,
            'get_file_extension': self.get_file_extension,
            'is_image_file': self.is_image_file,
            'is_video_file': self.is_video_file,
            'pluralize': self.pluralize,
        })
    
    def update(self, context: Dict[str, Any]):
        """更新上下文"""
        self._context.update(context)
    
    def set(self, key: str, value: Any):
        """设置单个变量"""
        self._context[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取变量"""
        return self._context.get(key, default)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self._context.copy()
    
    def set_global(self, key: str, value: Any):
        """设置全局变量（别名方法）"""
        self.set(key, value)
    
    def get_context(self) -> Dict[str, Any]:
        """获取完整上下文（别名方法）"""
        return self.to_dict()
    
    # 工具函数
    
    def url_for(self, endpoint: str, **params) -> str:
        """生成URL（简化版本）"""
        # 这里可以集成路由系统的url_for功能
        if params:
            query_string = urlencode(params)
            return f"{endpoint}?{query_string}"
        return endpoint
    
    def static_url(self, filename: str) -> str:
        """生成静态文件URL"""
        static_prefix = getattr(self.config, 'STATIC_URL_PREFIX', '/static')
        return f"{static_prefix}/{filename.lstrip('/')}"
    
    def format_datetime(self, dt: datetime.datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化日期时间"""
        if dt is None:
            return ''
        if isinstance(dt, str):
            try:
                dt = datetime.datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except ValueError:
                return dt
        return dt.strftime(format_str)
    
    def format_date(self, date: datetime.date, format_str: str = '%Y-%m-%d') -> str:
        """格式化日期"""
        if date is None:
            return ''
        if isinstance(date, str):
            try:
                date = datetime.datetime.fromisoformat(date).date()
            except ValueError:
                return date
        return date.strftime(format_str)
    
    def format_time(self, time: datetime.time, format_str: str = '%H:%M:%S') -> str:
        """格式化时间"""
        if time is None:
            return ''
        if isinstance(time, str):
            try:
                time = datetime.datetime.fromisoformat(f"1970-01-01T{time}").time()
            except ValueError:
                return time
        return time.strftime(format_str)
    
    def format_filesize(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return '0 B'
        
        size_names = ['B', 'KB', 'MB', 'GB', 'TB']
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def truncate(self, text: str, length: int = 100, suffix: str = '...') -> str:
        """截断文本"""
        if not text or len(text) <= length:
            return text
        return text[:length - len(suffix)] + suffix
    
    def build_query_string(self, params: Dict[str, Any]) -> str:
        """构建查询字符串"""
        if not params:
            return ''
        
        # 过滤空值
        filtered_params = {k: v for k, v in params.items() if v is not None and v != ''}
        
        if not filtered_params:
            return ''
        
        return urlencode(filtered_params)
    
    def get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        if not filename or '.' not in filename:
            return ''
        return filename.rsplit('.', 1)[-1].lower()
    
    def is_image_file(self, filename: str) -> bool:
        """判断是否为图片文件"""
        image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'}
        return self.get_file_extension(filename) in image_extensions
    
    def is_video_file(self, filename: str) -> bool:
        """判断是否为视频文件"""
        video_extensions = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'}
        return self.get_file_extension(filename) in video_extensions
    
    def pluralize(self, count: int, singular: str, plural: str = None) -> str:
        """复数形式"""
        if count == 1:
            return singular
        return plural or f"{singular}s"
    
    def relative_time(self, dt: datetime.datetime) -> str:
        """相对时间显示"""
        if not dt:
            return ''
        
        if isinstance(dt, str):
            try:
                dt = datetime.datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except ValueError:
                return dt
        
        now = datetime.datetime.now(dt.tzinfo) if dt.tzinfo else datetime.datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            if diff.days == 1:
                return '1天前'
            elif diff.days < 30:
                return f'{diff.days}天前'
            elif diff.days < 365:
                months = diff.days // 30
                return f'{months}个月前'
            else:
                years = diff.days // 365
                return f'{years}年前'
        
        seconds = diff.seconds
        if seconds < 60:
            return '刚刚'
        elif seconds < 3600:
            minutes = seconds // 60
            return f'{minutes}分钟前'
        else:
            hours = seconds // 3600
            return f'{hours}小时前'
    
    def highlight_search(self, text: str, query: str, css_class: str = 'highlight') -> str:
        """高亮搜索关键词"""
        if not text or not query:
            return text
        
        import re
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        return pattern.sub(f'<span class="{css_class}">\\g<0></span>', text)
    
    def json_dumps(self, obj: Any, indent: int = None) -> str:
        """JSON序列化"""
        import json
        return json.dumps(obj, ensure_ascii=False, indent=indent, default=str)
    
    def markdown_to_html(self, text: str) -> str:
        """Markdown转HTML（简化版本）"""
        if not text:
            return ''
        
        # 简单的Markdown处理
        import re
        
        # 标题
        text = re.sub(r'^### (.+)$', r'<h3>\1</h3>', text, flags=re.MULTILINE)
        text = re.sub(r'^## (.+)$', r'<h2>\1</h2>', text, flags=re.MULTILINE)
        text = re.sub(r'^# (.+)$', r'<h1>\1</h1>', text, flags=re.MULTILINE)
        
        # 粗体和斜体
        text = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', text)
        text = re.sub(r'\*(.+?)\*', r'<em>\1</em>', text)
        
        # 代码
        text = re.sub(r'`(.+?)`', r'<code>\1</code>', text)
        
        # 链接
        text = re.sub(r'\[(.+?)\]\((.+?)\)', r'<a href="\2">\1</a>', text)
        
        # 换行
        text = text.replace('\n', '<br>')
        
        return text