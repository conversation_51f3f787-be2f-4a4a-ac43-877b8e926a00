<!-- 按钮组件 -->
<button 
    type="{{ component.props.type or 'button' }}"
    class="btn {{ component.props.variant or 'btn-primary' }} {{ component.props.size or '' }} {{ component.props.class or '' }}"
    {% if component.props.id %}id="{{ component.props.id }}"{% endif %}
    {% if component.props.disabled %}disabled{% endif %}
    {% if component.props.onclick %}onclick="{{ component.props.onclick }}"{% endif %}
    {% if component.props.data_toggle %}data-toggle="{{ component.props.data_toggle }}"{% endif %}
    {% if component.props.data_target %}data-target="{{ component.props.data_target }}"{% endif %}
    {% if component.props.aria_label %}aria-label="{{ component.props.aria_label }}"{% endif %}
    {% if component.props.title %}title="{{ component.props.title }}"{% endif %}
>
    {% if component.props.icon %}
    <i class="{{ component.props.icon }}"></i>
    {% endif %}
    
    {% if component.props.text %}
    {{ component.props.text }}
    {% elif component.slots.default %}
    {{ component.slots.default | safe }}
    {% else %}
    按钮
    {% endif %}
    
    {% if component.props.loading %}
    <span class="loading ml-2"></span>
    {% endif %}
</button>