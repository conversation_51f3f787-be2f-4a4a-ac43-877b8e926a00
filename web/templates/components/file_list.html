<!-- 文件列表组件 -->
<div class="file-list {{ component.props.class or '' }}">
    {% if files %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th width="40"></th>
                    <th>名称</th>
                    {% if show_type %}
                    <th width="80">类型</th>
                    {% endif %}
                    {% if show_size %}
                    <th width="100">大小</th>
                    {% endif %}
                    {% if show_date %}
                    <th width="150">修改时间</th>
                    {% endif %}
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for file in files %}
                <tr class="file-item" data-path="{{ file.path or '' }}" data-type="{{ file.type or 'file' }}">
                    <td>
                        {{ render_component('file_icon', engine, {
                            'filename': file.name,
                            'icon_type': file.type or 'file'
                        }) | safe }}
                    </td>
                    <td>
                        <a href="{{ url_for('/files', path=file.path) if file.type == 'directory' else url_for('/preview', path=file.path) }}" 
                           class="file-link text-decoration-none">
                            {{ file.name }}
                        </a>
                        {% if file.description %}
                        <small class="text-muted d-block">{{ file.description }}</small>
                        {% endif %}
                    </td>
                    {% if show_type %}
                    <td>
                        <span class="badge badge-{{ 'warning' if file.type == 'directory' else 'secondary' }}">
                            {{ '文件夹' if file.type == 'directory' else (file.extension or '文件') }}
                        </span>
                    </td>
                    {% endif %}
                    {% if show_size %}
                    <td>
                        {% if file.type != 'directory' and file.size is defined %}
                        {{ format_filesize(file.size) }}
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    {% endif %}
                    {% if show_date %}
                    <td>
                        {% if file.modified %}
                        <span title="{{ format_datetime(file.modified) }}">
                            {{ relative_time(file.modified) }}
                        </span>
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    {% endif %}
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            {% if file.type == 'directory' %}
                            <a href="{{ url_for('/files', path=file.path) }}" 
                               class="btn btn-outline-primary btn-sm" 
                               title="打开文件夹">
                                <i class="fas fa-folder-open"></i>
                            </a>
                            {% else %}
                            <a href="{{ url_for('/preview', path=file.path) }}" 
                               class="btn btn-outline-primary btn-sm" 
                               title="预览">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% endif %}
                            
                            <a href="{{ url_for('/download', path=file.path) }}" 
                               class="btn btn-outline-success btn-sm" 
                               title="下载">
                                <i class="fas fa-download"></i>
                            </a>
                            
                            <button type="button" 
                                    class="btn btn-outline-info btn-sm" 
                                    title="信息"
                                    onclick="showFileInfo('{{ file.path }}')">
                                <i class="fas fa-info"></i>
                            </button>
                            
                            {% if component.props.show_delete %}
                            <button type="button" 
                                    class="btn btn-outline-danger btn-sm" 
                                    title="删除"
                                    onclick="deleteFile('{{ file.path }}', '{{ file.name }}')"
                                    data-confirm="确定要删除 '{{ file.name }}' 吗？">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">{{ component.props.empty_message or '此文件夹为空' }}</h5>
        {% if component.props.show_upload %}
        <p class="text-muted">您可以拖拽文件到这里上传</p>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- 文件操作脚本 -->
<script>
function showFileInfo(filePath) {
    // 显示文件信息模态框
    $.get('{{ url_for("/api/files/info") }}', {path: filePath})
        .done(function(data) {
            // 构建信息内容
            var content = '<dl class="row">';
            content += '<dt class="col-sm-3">文件名:</dt><dd class="col-sm-9">' + data.name + '</dd>';
            content += '<dt class="col-sm-3">路径:</dt><dd class="col-sm-9">' + data.path + '</dd>';
            if (data.size !== undefined) {
                content += '<dt class="col-sm-3">大小:</dt><dd class="col-sm-9">' + data.size_formatted + '</dd>';
            }
            if (data.modified) {
                content += '<dt class="col-sm-3">修改时间:</dt><dd class="col-sm-9">' + data.modified_formatted + '</dd>';
            }
            if (data.type) {
                content += '<dt class="col-sm-3">类型:</dt><dd class="col-sm-9">' + data.type + '</dd>';
            }
            content += '</dl>';
            
            // 显示模态框
            showModal('文件信息', content);
        })
        .fail(function() {
            showMessage('获取文件信息失败', 'error');
        });
}

function deleteFile(filePath, fileName) {
    if (!confirm('确定要删除 "' + fileName + '" 吗？此操作不可撤销。')) {
        return;
    }
    
    $.ajax({
        url: '{{ url_for("/api/files/delete") }}',
        method: 'DELETE',
        data: {path: filePath}
    })
    .done(function(data) {
        showMessage('文件删除成功', 'success');
        // 刷新页面或移除行
        $('[data-path="' + filePath + '"]').fadeOut(function() {
            $(this).remove();
        });
    })
    .fail(function(xhr) {
        var message = xhr.responseJSON ? xhr.responseJSON.error : '删除失败';
        showMessage(message, 'error');
    });
}

function showModal(title, content) {
    var modal = $('<div class="modal fade" tabindex="-1" role="dialog">' +
        '<div class="modal-dialog" role="document">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h5 class="modal-title">' + title + '</h5>' +
        '<button type="button" class="close" data-dismiss="modal" aria-label="关闭">' +
        '<span aria-hidden="true">&times;</span>' +
        '</button>' +
        '</div>' +
        '<div class="modal-body">' + content + '</div>' +
        '<div class="modal-footer">' +
        '<button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>');
    
    $('body').append(modal);
    modal.modal('show');
    modal.on('hidden.bs.modal', function() {
        modal.remove();
    });
}
</script>