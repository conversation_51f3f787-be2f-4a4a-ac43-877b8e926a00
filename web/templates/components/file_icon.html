<!-- 文件图标组件 -->
{% set icon_classes = {
    'folder': 'fas fa-folder text-warning',
    'image': 'fas fa-image text-info',
    'video': 'fas fa-video text-danger',
    'audio': 'fas fa-music text-success',
    'pdf': 'fas fa-file-pdf text-danger',
    'document': 'fas fa-file-word text-primary',
    'spreadsheet': 'fas fa-file-excel text-success',
    'presentation': 'fas fa-file-powerpoint text-warning',
    'text': 'fas fa-file-alt text-secondary',
    'code': 'fas fa-file-code text-info',
    'archive': 'fas fa-file-archive text-warning',
    'file': 'fas fa-file text-muted'
} %}

{% set icon_class = icon_classes.get(icon_type, icon_classes['file']) %}

<i class="{{ icon_class }} {{ component.props.class or '' }}"
   {% if component.props.title %}title="{{ component.props.title }}"{% endif %}
   {% if component.props.style %}style="{{ component.props.style }}"{% endif %}>
</i>