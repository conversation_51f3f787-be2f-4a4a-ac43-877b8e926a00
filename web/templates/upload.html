{% extends "base.html" %}

{% block title %}文件上传 - {{ app_name }}{% endblock %}

{% block extra_css %}
<style>
    .upload-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        padding: 3rem 2rem;
        text-align: center;
        background: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .upload-area:hover {
        border-color: #007bff;
        background: #e7f3ff;
    }
    
    .upload-area.dragover {
        border-color: #28a745;
        background: #d4edda;
        transform: scale(1.02);
    }
    
    .upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }
    
    .upload-area.dragover .upload-icon {
        color: #28a745;
    }
    
    .upload-text {
        font-size: 1.1rem;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .upload-hint {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }
    
    .upload-options {
        margin-top: 2rem;
        padding: 1.5rem;
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
    }
    
    .file-list {
        margin-top: 2rem;
    }
    
    .file-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
    }
    
    .file-item:hover {
        background: #f8f9fa;
    }
    
    .file-icon {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.25rem;
        margin-right: 1rem;
        font-size: 1.2rem;
    }
    
    .file-icon.image {
        background-color: #d4edda;
        color: #155724;
    }
    
    .file-icon.document {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .file-icon.archive {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .file-icon.other {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .file-info {
        flex: 1;
        min-width: 0;
    }
    
    .file-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
        word-break: break-all;
    }
    
    .file-meta {
        font-size: 0.875rem;
        color: #6c757d;
        display: flex;
        gap: 1rem;
    }
    
    .file-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-left: 1rem;
    }
    
    .progress-container {
        width: 120px;
        margin-left: 1rem;
    }
    
    .progress {
        height: 0.5rem;
    }
    
    .file-actions {
        display: flex;
        gap: 0.5rem;
        margin-left: 1rem;
    }
    
    .status-icon {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
    }
    
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-uploading {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .status-success {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-error {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .upload-summary {
        background: #e7f3ff;
        border: 1px solid #b3d7ff;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
    }
    
    .upload-summary.show {
        display: block;
    }
    
    .summary-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 600;
        color: #007bff;
    }
    
    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .upload-container {
            margin: 1rem auto;
        }
        
        .upload-area {
            padding: 2rem 1rem;
        }
        
        .file-meta {
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .file-actions {
            flex-direction: column;
        }
        
        .summary-stats {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="upload-container">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-cloud-upload-alt"></i> 文件上传</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="/?path={{ current_path }}" class="text-decoration-none">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                </li>
            </ol>
        </nav>
    </div>
    
    <!-- 上传区域 -->
    <div class="upload-area" id="uploadArea">
        <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="upload-text">拖拽文件到此处或点击选择文件</div>
        <div class="upload-hint">支持多文件上传，单个文件最大 100MB</div>
        <input type="file" class="file-input" id="fileInput" multiple title="选择要上传的文件">
    </div>
    
    <!-- 上传选项 -->
    <div class="upload-options">
        <h5><i class="fas fa-cog"></i> 上传选项</h5>
        <div class="row">
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="overwriteFiles" checked>
                    <label class="form-check-label" for="overwriteFiles">
                        覆盖同名文件
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="createFolders" checked>
                    <label class="form-check-label" for="createFolders">
                        自动创建目录
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="uploadPath" class="form-label">上传路径</label>
                    <input type="text" class="form-control" id="uploadPath" value="{{ current_path }}" placeholder="/">
                </div>
            </div>
        </div>
        
        <div class="d-flex gap-2">
            <button class="btn btn-primary" id="startUpload" disabled>
                <i class="fas fa-upload"></i> 开始上传
            </button>
            <button class="btn btn-secondary" id="clearFiles" disabled>
                <i class="fas fa-trash"></i> 清空列表
            </button>
            <button class="btn btn-outline-secondary" id="pauseUpload" disabled>
                <i class="fas fa-pause"></i> 暂停
            </button>
        </div>
    </div>
    
    <!-- 文件列表 -->
    <div class="file-list" id="fileList" style="display: none;">
        <h5><i class="fas fa-list"></i> 待上传文件</h5>
        <div id="fileItems"></div>
    </div>
    
    <!-- 上传摘要 -->
    <div class="upload-summary" id="uploadSummary">
        <h6><i class="fas fa-chart-bar"></i> 上传统计</h6>
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-number" id="totalFiles">0</div>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="successFiles">0</div>
                <div class="stat-label">成功</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="failedFiles">0</div>
                <div class="stat-label">失败</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalSize">0</div>
                <div class="stat-label">总大小</div>
            </div>
        </div>
        <div class="progress">
            <div class="progress-bar" id="overallProgress" role="progressbar" style="width: 0%"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class FileUploader {
    constructor() {
        this.files = [];
        this.uploadQueue = [];
        this.isUploading = false;
        this.isPaused = false;
        this.currentUpload = null;
        
        this.initializeElements();
        this.bindEvents();
    }
    
    initializeElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.fileList = document.getElementById('fileList');
        this.fileItems = document.getElementById('fileItems');
        this.startUploadBtn = document.getElementById('startUpload');
        this.clearFilesBtn = document.getElementById('clearFiles');
        this.pauseUploadBtn = document.getElementById('pauseUpload');
        this.uploadSummary = document.getElementById('uploadSummary');
    }
    
    bindEvents() {
        // 文件选择
        this.fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });
        
        // 拖拽事件
        this.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.uploadArea.classList.add('dragover');
        });
        
        this.uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.uploadArea.classList.remove('dragover');
        });
        
        this.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.uploadArea.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });
        
        // 按钮事件
        this.startUploadBtn.addEventListener('click', () => {
            this.startUpload();
        });
        
        this.clearFilesBtn.addEventListener('click', () => {
            this.clearFiles();
        });
        
        this.pauseUploadBtn.addEventListener('click', () => {
            this.togglePause();
        });
    }
    
    handleFiles(fileList) {
        const newFiles = Array.from(fileList).map(file => ({
            id: Date.now() + Math.random(),
            file: file,
            name: file.name,
            size: file.size,
            type: this.getFileType(file.name),
            status: 'pending',
            progress: 0,
            error: null
        }));
        
        this.files.push(...newFiles);
        this.renderFileList();
        this.updateButtons();
        this.updateSummary();
    }
    
    getFileType(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) {
            return 'image';
        } else if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(ext)) {
            return 'document';
        } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
            return 'archive';
        } else {
            return 'other';
        }
    }
    
    renderFileList() {
        if (this.files.length === 0) {
            this.fileList.style.display = 'none';
            return;
        }
        
        this.fileList.style.display = 'block';
        
        this.fileItems.innerHTML = this.files.map(file => `
            <div class="file-item" data-file-id="${file.id}">
                <div class="file-icon ${file.type}">
                    ${this.getFileIcon(file.type)}
                </div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-meta">
                        <span>${this.formatFileSize(file.size)}</span>
                        <span>${file.type}</span>
                    </div>
                </div>
                
                ${file.status === 'uploading' ? `
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" style="width: ${file.progress}%"></div>
                        </div>
                        <small class="text-muted">${file.progress}%</small>
                    </div>
                ` : ''}
                
                <div class="file-status">
                    <div class="status-icon status-${file.status}">
                        ${this.getStatusIcon(file.status)}
                    </div>
                </div>
                
                <div class="file-actions">
                    ${file.status === 'pending' ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="uploader.removeFile('${file.id}')" title="移除">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                    ${file.status === 'error' ? `
                        <button class="btn btn-sm btn-outline-primary" onclick="uploader.retryFile('${file.id}')" title="重试">
                            <i class="fas fa-redo"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }
    
    getFileIcon(type) {
        const icons = {
            image: '<i class="fas fa-file-image"></i>',
            document: '<i class="fas fa-file-alt"></i>',
            archive: '<i class="fas fa-file-archive"></i>',
            other: '<i class="fas fa-file"></i>'
        };
        return icons[type] || icons.other;
    }
    
    getStatusIcon(status) {
        const icons = {
            pending: '<i class="fas fa-clock"></i>',
            uploading: '<i class="fas fa-spinner fa-spin"></i>',
            success: '<i class="fas fa-check"></i>',
            error: '<i class="fas fa-exclamation"></i>'
        };
        return icons[status] || '';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    updateButtons() {
        const hasPendingFiles = this.files.some(f => f.status === 'pending');
        const hasFiles = this.files.length > 0;
        
        this.startUploadBtn.disabled = !hasPendingFiles || this.isUploading;
        this.clearFilesBtn.disabled = !hasFiles || this.isUploading;
        this.pauseUploadBtn.disabled = !this.isUploading;
        
        if (this.isPaused) {
            this.pauseUploadBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
        } else {
            this.pauseUploadBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
        }
    }
    
    updateSummary() {
        const total = this.files.length;
        const success = this.files.filter(f => f.status === 'success').length;
        const failed = this.files.filter(f => f.status === 'error').length;
        const totalSize = this.files.reduce((sum, f) => sum + f.size, 0);
        
        document.getElementById('totalFiles').textContent = total;
        document.getElementById('successFiles').textContent = success;
        document.getElementById('failedFiles').textContent = failed;
        document.getElementById('totalSize').textContent = this.formatFileSize(totalSize);
        
        if (total > 0) {
            this.uploadSummary.classList.add('show');
            
            const overallProgress = total > 0 ? ((success + failed) / total) * 100 : 0;
            document.getElementById('overallProgress').style.width = overallProgress + '%';
        }
    }
    
    async startUpload() {
        if (this.isUploading) return;
        
        this.isUploading = true;
        this.isPaused = false;
        this.uploadQueue = this.files.filter(f => f.status === 'pending');
        
        this.updateButtons();
        
        while (this.uploadQueue.length > 0 && !this.isPaused) {
            const file = this.uploadQueue.shift();
            await this.uploadFile(file);
        }
        
        this.isUploading = false;
        this.updateButtons();
    }
    
    async uploadFile(fileData) {
        fileData.status = 'uploading';
        fileData.progress = 0;
        this.renderFileList();
        
        const formData = new FormData();
        formData.append('file', fileData.file);
        formData.append('path', document.getElementById('uploadPath').value);
        formData.append('overwrite', document.getElementById('overwriteFiles').checked);
        formData.append('create_dirs', document.getElementById('createFolders').checked);
        
        try {
            const response = await fetch('/api/files/upload', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                fileData.status = 'success';
                fileData.progress = 100;
            } else {
                const error = await response.json();
                fileData.status = 'error';
                fileData.error = error.message || '上传失败';
            }
        } catch (error) {
            fileData.status = 'error';
            fileData.error = error.message || '网络错误';
        }
        
        this.renderFileList();
        this.updateSummary();
    }
    
    removeFile(fileId) {
        this.files = this.files.filter(f => f.id !== fileId);
        this.renderFileList();
        this.updateButtons();
        this.updateSummary();
    }
    
    retryFile(fileId) {
        const file = this.files.find(f => f.id === fileId);
        if (file) {
            file.status = 'pending';
            file.error = null;
            file.progress = 0;
            this.renderFileList();
            this.updateButtons();
        }
    }
    
    clearFiles() {
        if (this.isUploading) return;
        
        this.files = [];
        this.renderFileList();
        this.updateButtons();
        this.updateSummary();
        this.uploadSummary.classList.remove('show');
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        this.updateButtons();
    }
}

// 初始化上传器
const uploader = new FileUploader();

// 阻止默认的拖拽行为
document.addEventListener('dragover', (e) => e.preventDefault());
document.addEventListener('drop', (e) => e.preventDefault());
</script>
{% endblock %}