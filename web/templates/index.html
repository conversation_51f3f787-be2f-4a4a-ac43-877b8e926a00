{% extends "base.html" %}

{% block title %}{{ page_title }} - {{ app_name }}{% endblock %}

{% block extra_css %}
<style>
    .file-manager {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
    }
    
    .toolbar {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "/";
        color: #6c757d;
    }
    
    .toolbar-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .file-list {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
    }
    
    .file-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border: 1px solid transparent;
        border-radius: 0.375rem;
        margin-bottom: 0.25rem;
        transition: all 0.2s ease;
        cursor: pointer;
    }
    
    .file-item:hover {
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }
    
    .file-item.selected {
        background-color: #e3f2fd;
        border-color: #2196f3;
    }
    
    .file-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.25rem;
        font-size: 1.2rem;
    }
    
    .file-icon.directory {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .file-icon.text {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .file-icon.image {
        background-color: #d4edda;
        color: #155724;
    }
    
    .file-icon.video {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .file-icon.audio {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .file-icon.binary {
        background-color: #e6e6e6;
        color: #6c757d;
    }
    
    .file-info {
        flex: 1;
        min-width: 0;
    }
    
    .file-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
        word-break: break-all;
    }
    
    .file-meta {
        font-size: 0.875rem;
        color: #6c757d;
        display: flex;
        gap: 1rem;
    }
    
    .file-actions {
        display: flex;
        gap: 0.25rem;
        opacity: 0;
        transition: opacity 0.2s ease;
    }
    
    .file-item:hover .file-actions {
        opacity: 1;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .search-box {
        position: relative;
        width: 300px;
    }
    
    .search-box input {
        padding-left: 2.5rem;
    }
    
    .search-box .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }
    
    .status-bar {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        color: #6c757d;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    @media (max-width: 768px) {
        .toolbar {
            flex-direction: column;
            align-items: stretch;
        }
        
        .toolbar-actions {
            justify-content: center;
        }
        
        .search-box {
            width: 100%;
        }
        
        .file-meta {
            flex-direction: column;
            gap: 0.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="file-manager">
    <!-- 工具栏 -->
    <div class="toolbar">
        <div class="d-flex align-items-center flex-wrap gap-3">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    {% for crumb in breadcrumbs %}
                        {% if loop.last %}
                            <li class="breadcrumb-item active" aria-current="page">{{ crumb.name }}</li>
                        {% else %}
                            <li class="breadcrumb-item">
                                <a href="/?path={{ crumb.path }}" class="text-decoration-none">{{ crumb.name }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ol>
            </nav>
            
            <!-- 路径信息 -->
            <small class="text-muted">{{ total_files }} 项</small>
        </div>
        
        <div class="toolbar-actions">
            <!-- 搜索框 -->
            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索文件..." id="searchInput">
                <i class="fas fa-search search-icon"></i>
            </div>
            
            <!-- 操作按钮 -->
            <button class="btn btn-outline-primary btn-sm" onclick="refreshPage()" aria-label="刷新">
                <i class="fas fa-sync-alt"></i>
            </button>
            
            <button class="btn btn-outline-secondary btn-sm" onclick="toggleView()" aria-label="切换视图">
                <i class="fas fa-th-list"></i>
            </button>
            
            <div class="dropdown">
                <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-plus"></i> 新建
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="createFolder()"><i class="fas fa-folder"></i> 新建文件夹</a></li>
                    <li><a class="dropdown-item" href="/upload?path={{ current_path }}"><i class="fas fa-upload"></i> 上传文件</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- 文件列表 -->
    <div class="file-list">
        {% if files %}
            {% for file in files %}
                <div class="file-item" data-path="{{ file.relative_path }}" data-type="{{ file.type }}" onclick="selectFile(this)" ondblclick="openFile(this)">
                    <div class="file-icon {{ file.type }}">
                        {% if file.type == 'directory' %}
                            <i class="fas fa-folder"></i>
                        {% elif file.type == 'text' %}
                            <i class="fas fa-file-alt"></i>
                        {% elif file.type == 'image' %}
                            <i class="fas fa-file-image"></i>
                        {% elif file.type == 'video' %}
                            <i class="fas fa-file-video"></i>
                        {% elif file.type == 'audio' %}
                            <i class="fas fa-file-audio"></i>
                        {% else %}
                            <i class="fas fa-file"></i>
                        {% endif %}
                    </div>
                    
                    <div class="file-info">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="file-meta">
                            <span>{{ format_file_size(file.size) }}</span>
                            <span>{{ format_datetime(file.modified) }}</span>
                            {% if file.extension %}
                                <span>{{ file.extension.upper() }}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="file-actions">
                        {% if file.type == 'directory' %}
                            <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); openFolder('{{ file.relative_path }}')" aria-label="打开文件夹" title="打开文件夹">
                                <i class="fas fa-folder-open"></i>
                            </button>
                        {% else %}
                            <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); viewFile('{{ file.relative_path }}')" aria-label="查看文件" title="查看文件">
                                <i class="fas fa-eye"></i>
                            </button>
                        {% endif %}
                        
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" onclick="event.stopPropagation()" aria-label="更多操作">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="renameFile('{{ file.relative_path }}')"><i class="fas fa-edit"></i> 重命名</a></li>
                                <li><a class="dropdown-item" href="#" onclick="copyFile('{{ file.relative_path }}')"><i class="fas fa-copy"></i> 复制</a></li>
                                <li><a class="dropdown-item" href="#" onclick="moveFile('{{ file.relative_path }}')"><i class="fas fa-cut"></i> 移动</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteFile('{{ file.relative_path }}')"><i class="fas fa-trash"></i> 删除</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-folder-open"></i>
                </div>
                <h5>此文件夹为空</h5>
                <p class="text-muted">您可以上传文件或创建新文件夹</p>
                <div class="mt-3">
                    <button class="btn btn-primary me-2" onclick="createFolder()">
                        <i class="fas fa-folder-plus"></i> 新建文件夹
                    </button>
                    <a href="/upload?path={{ current_path }}" class="btn btn-outline-primary">
                        <i class="fas fa-upload"></i> 上传文件
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
    
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>{{ total_files }} 项</span>
        <span>路径: {{ current_path or '/' }}</span>
    </div>
</div>

<!-- 模态框 -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建文件夹</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="folderName" class="form-label">文件夹名称</label>
                    <input type="text" class="form-control" id="folderName" placeholder="请输入文件夹名称">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmCreateFolder()">创建</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="renameModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重命名</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newName" class="form-label">新名称</label>
                    <input type="text" class="form-control" id="newName" placeholder="请输入新名称">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmRename()">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedFiles = [];
let currentPath = '{{ current_path }}';
let renameTarget = '';

// 搜索功能
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const query = this.value.trim();
        if (query) {
            window.location.href = `/search?q=${encodeURIComponent(query)}&path=${encodeURIComponent(currentPath)}`;
        }
    }
});

// 选择文件
function selectFile(element) {
    // 清除其他选择
    document.querySelectorAll('.file-item.selected').forEach(item => {
        item.classList.remove('selected');
    });
    
    // 选择当前文件
    element.classList.add('selected');
    selectedFiles = [element.dataset.path];
}

// 打开文件/文件夹
function openFile(element) {
    const path = element.dataset.path;
    const type = element.dataset.type;
    
    if (type === 'directory') {
        openFolder(path);
    } else {
        viewFile(path);
    }
}

// 打开文件夹
function openFolder(path) {
    window.location.href = `/?path=${encodeURIComponent(path)}`;
}

// 查看文件
function viewFile(path) {
    window.location.href = `/file?path=${encodeURIComponent(path)}`;
}

// 刷新页面
function refreshPage() {
    window.location.reload();
}

// 切换视图
function toggleView() {
    // 这里可以实现网格视图和列表视图的切换
    alert('视图切换功能待实现');
}

// 创建文件夹
function createFolder() {
    const modal = new bootstrap.Modal(document.getElementById('createFolderModal'));
    modal.show();
    
    // 清空输入框
    document.getElementById('folderName').value = '';
    
    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('folderName').focus();
    }, 500);
}

// 确认创建文件夹
function confirmCreateFolder() {
    const folderName = document.getElementById('folderName').value.trim();
    
    if (!folderName) {
        alert('请输入文件夹名称');
        return;
    }
    
    // 验证文件夹名称
    if (!/^[^<>:"/\\|?*]+$/.test(folderName)) {
        alert('文件夹名称包含非法字符');
        return;
    }
    
    // 发送创建请求
    fetch('/api/files/create-directory', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            path: currentPath ? `${currentPath}/${folderName}` : folderName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('createFolderModal')).hide();
            // 刷新页面
            window.location.reload();
        } else {
            alert(data.message || '创建文件夹失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('创建文件夹时发生错误');
    });
}

// 重命名文件
function renameFile(path) {
    renameTarget = path;
    const fileName = path.split('/').pop();
    
    document.getElementById('newName').value = fileName;
    
    const modal = new bootstrap.Modal(document.getElementById('renameModal'));
    modal.show();
    
    // 选中文件名（不包括扩展名）
    setTimeout(() => {
        const input = document.getElementById('newName');
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            input.setSelectionRange(0, lastDotIndex);
        } else {
            input.select();
        }
        input.focus();
    }, 500);
}

// 确认重命名
function confirmRename() {
    const newName = document.getElementById('newName').value.trim();
    
    if (!newName) {
        alert('请输入新名称');
        return;
    }
    
    // 验证文件名
    if (!/^[^<>:"/\\|?*]+$/.test(newName)) {
        alert('文件名包含非法字符');
        return;
    }
    
    // 发送重命名请求
    fetch('/api/files/rename', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            old_path: renameTarget,
            new_name: newName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('renameModal')).hide();
            // 刷新页面
            window.location.reload();
        } else {
            alert(data.message || '重命名失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('重命名时发生错误');
    });
}

// 复制文件
function copyFile(path) {
    const newPath = prompt('请输入目标路径:', path + '_copy');
    if (newPath && newPath !== path) {
        fetch('/api/files/copy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                source_path: path,
                target_path: newPath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '复制失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('复制时发生错误');
        });
    }
}

// 移动文件
function moveFile(path) {
    const newPath = prompt('请输入目标路径:', path);
    if (newPath && newPath !== path) {
        fetch('/api/files/move', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                source_path: path,
                target_path: newPath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '移动失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('移动时发生错误');
        });
    }
}

// 删除文件
function deleteFile(path) {
    if (confirm(`确定要删除 "${path.split('/').pop()}" 吗？此操作不可撤销。`)) {
        fetch(`/api/files/delete?path=${encodeURIComponent(path)}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除时发生错误');
        });
    }
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // F5 刷新
    if (e.key === 'F5') {
        e.preventDefault();
        refreshPage();
    }
    
    // Ctrl+N 新建文件夹
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        createFolder();
    }
    
    // Delete 删除选中文件
    if (e.key === 'Delete' && selectedFiles.length > 0) {
        e.preventDefault();
        deleteFile(selectedFiles[0]);
    }
    
    // F2 重命名选中文件
    if (e.key === 'F2' && selectedFiles.length > 0) {
        e.preventDefault();
        renameFile(selectedFiles[0]);
    }
});
</script>
{% endblock %}