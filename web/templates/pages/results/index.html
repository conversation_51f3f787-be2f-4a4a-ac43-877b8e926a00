<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Producer Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .breadcrumbs {
            margin-bottom: 1rem;
        }
        
        .breadcrumbs a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs span {
            color: #999;
            margin: 0 0.5rem;
        }
        
        .stats-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }
        
        .stat-item .value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-item .label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .controls {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .controls-row {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }
        
        .search-box {
            flex: 1;
            min-width: 250px;
        }
        
        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .filter-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .filter-group label {
            font-weight: 500;
            color: #555;
        }
        
        .filter-group select {
            padding: 0.5rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            background: white;
            font-size: 0.9rem;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .result-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #667eea;
        }
        
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }
        
        .result-card .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            padding: 0;
            background: none;
            box-shadow: none;
        }
        
        .result-card .title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .result-card .status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status.passed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .result-card .meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .result-card .description {
            color: #555;
            line-height: 1.5;
            margin-bottom: 1rem;
        }
        
        .result-card .actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            text-decoration: none;
            color: #667eea;
            background: white;
        }
        
        .pagination a:hover {
            background: #f8f9fa;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .pagination .disabled {
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .empty-state h3 {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .empty-state p {
            color: #999;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .result-card .meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <nav class="breadcrumbs">
                {% for crumb in breadcrumbs %}
                    {% if crumb.url %}
                        <a href="{{ crumb.url }}">{{ crumb.title }}</a>
                    {% else %}
                        {{ crumb.title }}
                    {% endif %}
                    {% if not loop.last %}<span>/</span>{% endif %}
                {% endfor %}
            </nav>
            <h1>{{ page_title }}</h1>
            <p>{{ page_description }}</p>
        </div>
        
        <div class="stats-bar">
            <div class="stat-item">
                <div class="value">{{ stats.total_results }}</div>
                <div class="label">总测试结果</div>
            </div>
            <div class="stat-item">
                <div class="value">{{ stats.passed_results }}</div>
                <div class="label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="value">{{ stats.failed_results }}</div>
                <div class="label">失败测试</div>
            </div>
            <div class="stat-item">
                <div class="value">{{ stats.pending_results }}</div>
                <div class="label">待处理</div>
            </div>
        </div>
        
        <div class="controls">
            <form method="GET" class="controls-row">
                <div class="search-box">
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="搜索测试结果...">
                </div>
                
                <div class="filter-group">
                    <label>状态:</label>
                    <select name="status" title="选择测试结果状态">
                        {% for option in status_options %}
                            <option value="{{ option.value }}" 
                                    {{ 'selected' if option.active else '' }}>
                                {{ option.label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>排序:</label>
                    <select name="sort" title="选择排序方式">
                        {% for option in sort_options %}
                            <option value="{{ option.value }}" 
                                    {{ 'selected' if option.active else '' }}>
                                {{ option.label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary">筛选</button>
            </form>
        </div>
        
        {% if results %}
            <div class="results-grid">
                {% for result in results %}
                    <div class="result-card">
                        <div class="header">
                            <div>
                                <div class="title">{{ result.name or result.test_id }}</div>
                            </div>
                            <span class="status {{ result.status }}">{{ result.status }}</span>
                        </div>
                        
                        <div class="meta">
                            <div><strong>测试ID:</strong> {{ result.test_id }}</div>
                            <div><strong>执行时间:</strong> {{ result.execution_time or 'N/A' }}</div>
                            <div><strong>创建时间:</strong> {{ result.created_at or 'N/A' }}</div>
                            <div><strong>更新时间:</strong> {{ result.updated_at or 'N/A' }}</div>
                        </div>
                        
                        {% if result.description %}
                            <div class="description">{{ result.description }}</div>
                        {% endif %}
                        
                        <div class="actions">
                            <a href="/results/detail?test_id={{ result.test_id }}" 
                               class="btn btn-primary">查看详情</a>
                            <a href="/api/results/{{ result.test_id }}" 
                               class="btn btn-secondary">API数据</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            {% if pagination.total_pages > 1 %}
                <div class="pagination">
                    {% if pagination.has_prev %}
                        <a href="?page={{ pagination.current_page - 1 }}&search={{ search_query }}&status={{ filter_status }}&sort={{ sort_by }}">
                            上一页
                        </a>
                    {% else %}
                        <span class="disabled">上一页</span>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.current_page %}
                            <span class="current">{{ page_num }}</span>
                        {% else %}
                            <a href="?page={{ page_num }}&search={{ search_query }}&status={{ filter_status }}&sort={{ sort_by }}">
                                {{ page_num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <a href="?page={{ pagination.current_page + 1 }}&search={{ search_query }}&status={{ filter_status }}&sort={{ sort_by }}">
                            下一页
                        </a>
                    {% else %}
                        <span class="disabled">下一页</span>
                    {% endif %}
                </div>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <h3>暂无测试结果</h3>
                <p>当前筛选条件下没有找到测试结果，请尝试调整筛选条件。</p>
            </div>
        {% endif %}
    </div>
</body>
</html>