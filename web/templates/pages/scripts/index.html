<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Producer Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .breadcrumbs {
            margin-bottom: 1rem;
        }
        
        .breadcrumbs a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs span {
            color: #999;
            margin: 0 0.5rem;
        }
        
        .stats-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }
        
        .stat-item .value {
            font-size: 2rem;
            font-weight: 700;
            color: #764ba2;
            margin-bottom: 0.5rem;
        }
        
        .stat-item .label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .controls {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .controls-row {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }
        
        .search-box {
            flex: 1;
            min-width: 250px;
        }
        
        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #764ba2;
        }
        
        .filter-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .filter-group label {
            font-weight: 500;
            color: #555;
        }
        
        .filter-group select {
            padding: 0.5rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            background: white;
            font-size: 0.9rem;
        }
        
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .project-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #764ba2;
            position: relative;
            overflow: hidden;
        }
        
        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }
        
        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #764ba2, #667eea);
            opacity: 0.1;
            border-radius: 0 12px 0 60px;
        }
        
        .project-card .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            padding: 0;
            background: none;
            box-shadow: none;
        }
        
        .project-card .title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }
        
        .project-card .project-id {
            font-size: 0.85rem;
            color: #999;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        .project-card .status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            white-space: nowrap;
        }
        
        .status.draft {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.in_progress {
            background: #cce5ff;
            color: #004085;
        }
        
        .status.completed {
            background: #d4edda;
            color: #155724;
        }
        
        .project-card .meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
        }
        
        .meta-item .label {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 0.2rem;
        }
        
        .meta-item .value {
            color: #333;
            font-weight: 500;
        }
        
        .project-card .theme-era {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            border-left: 3px solid #764ba2;
        }
        
        .theme-era .theme {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }
        
        .theme-era .era {
            color: #666;
            font-size: 0.9rem;
        }
        
        .project-card .actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: background-color 0.3s ease;
            flex: 1;
        }
        
        .btn-primary {
            background: #764ba2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #6a4190;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            text-decoration: none;
            color: #764ba2;
            background: white;
        }
        
        .pagination a:hover {
            background: #f8f9fa;
        }
        
        .pagination .current {
            background: #764ba2;
            color: white;
            border-color: #764ba2;
        }
        
        .pagination .disabled {
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .empty-state h3 {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .empty-state p {
            color: #999;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .project-card .meta {
                grid-template-columns: 1fr;
            }
            
            .project-card .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <nav class="breadcrumbs">
                <a href="/">首页</a>
                <span>/</span>
                <span>剧本管理</span>
            </nav>
            <h1>{{ page_title }}</h1>
            <p>{{ page_description }}</p>
        </div>
        
        <div class="stats-bar">
            <div class="stat-item">
                <div class="value">{{ stats.total_projects }}</div>
                <div class="label">总项目数</div>
            </div>
            <div class="stat-item">
                <div class="value">{{ stats.completed_projects }}</div>
                <div class="label">已完成</div>
            </div>
            <div class="stat-item">
                <div class="value">{{ stats.in_progress_projects }}</div>
                <div class="label">进行中</div>
            </div>
            <div class="stat-item">
                <div class="value">{{ stats.draft_projects }}</div>
                <div class="label">草稿</div>
            </div>
        </div>
        
        <div class="controls">
            <form method="GET" class="controls-row">
                <div class="search-box">
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="搜索剧本项目...">
                </div>
                
                <div class="filter-group">
                    <label>状态:</label>
                    <select name="status" title="选择项目状态">
                        {% for option in status_options %}
                            <option value="{{ option.value }}" 
                                    {{ 'selected' if option.active else '' }}>
                                {{ option.label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>排序:</label>
                    <select name="sort" title="选择排序方式">
                        {% for option in sort_options %}
                            <option value="{{ option.value }}" 
                                    {{ 'selected' if option.active else '' }}>
                                {{ option.label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary">筛选</button>
            </form>
        </div>
        
        {% if projects %}
            <div class="projects-grid">
                {% for project in projects %}
                    <div class="project-card">
                        <div class="header">
                            <div>
                                <div class="title">{{ project.title }}</div>
                                <div class="project-id">ID: {{ project.project_id }}</div>
                            </div>
                            <span class="status {{ project.status }}">{{ project.status }}</span>
                        </div>
                        
                        <div class="meta">
                            <div class="meta-item">
                                <div class="label">创建时间</div>
                                <div class="value">{{ project.created_at or 'N/A' }}</div>
                            </div>
                            <div class="meta-item">
                                <div class="label">更新时间</div>
                                <div class="value">{{ project.updated_at or 'N/A' }}</div>
                            </div>
                        </div>
                        
                        {% if project.theme or project.era %}
                            <div class="theme-era">
                                {% if project.theme %}
                                    <div class="theme">主题: {{ project.theme }}</div>
                                {% endif %}
                                {% if project.era %}
                                    <div class="era">时代: {{ project.era }}</div>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        <div class="actions">
                            <a href="/scripts/detail?project_id={{ project.project_id }}" 
                               class="btn btn-primary">查看详情</a>
                            <a href="/api/scripts/{{ project.project_id }}" 
                               class="btn btn-secondary">API数据</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            {% if pagination.total_pages > 1 %}
                <div class="pagination">
                    {% if pagination.has_prev %}
                        <a href="?page={{ pagination.current_page - 1 }}&search={{ search_query }}&status={{ filter_status }}&sort={{ sort_by }}">
                            上一页
                        </a>
                    {% else %}
                        <span class="disabled">上一页</span>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.current_page %}
                            <span class="current">{{ page_num }}</span>
                        {% else %}
                            <a href="?page={{ page_num }}&search={{ search_query }}&status={{ filter_status }}&sort={{ sort_by }}">
                                {{ page_num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <a href="?page={{ pagination.current_page + 1 }}&search={{ search_query }}&status={{ filter_status }}&sort={{ sort_by }}">
                            下一页
                        </a>
                    {% else %}
                        <span class="disabled">下一页</span>
                    {% endif %}
                </div>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <h3>暂无剧本项目</h3>
                <p>当前筛选条件下没有找到剧本项目，请尝试调整筛选条件。</p>
            </div>
        {% endif %}
    </div>
</body>
</html>