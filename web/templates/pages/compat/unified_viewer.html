<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .redirect-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            border-left: 4px solid #3498db;
        }
        
        .redirect-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .redirect-url {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 1.1em;
            color: #495057;
            margin: 10px 0;
        }
        
        .endpoints-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .endpoint-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .endpoint-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            border-color: #3498db;
        }
        
        .endpoint-card h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .endpoint-card p {
            color: #7f8c8d;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .migration-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .migration-info h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .migration-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            text-align: left;
        }
        
        .migration-detail {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #f39c12;
        }
        
        .migration-detail strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }
        
        .migration-detail span {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .auto-redirect {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .auto-redirect h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .countdown {
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
            margin: 10px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .endpoints-grid {
                grid-template-columns: 1fr;
            }
            
            .migration-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Unified Viewer</h1>
            <p>系统迁移兼容性接口</p>
        </div>
        
        {% if redirect_url %}
        <div class="auto-redirect">
            <h3>🚀 自动重定向</h3>
            <p>检测到兼容性请求，正在重定向到新的接口...</p>
            <div class="redirect-url">{{ redirect_url }}</div>
            <div class="countdown" id="countdown">3</div>
            <p>秒后自动跳转，或点击下方按钮立即访问</p>
            <a href="{{ redirect_url }}" class="btn">立即访问</a>
        </div>
        {% endif %}
        
        <div class="redirect-info">
            <h3>📍 当前请求信息</h3>
            <p><strong>操作类型:</strong> {{ action }}</p>
            <p><strong>响应格式:</strong> {{ view_type }}</p>
            {% if redirect_url %}
            <p><strong>目标地址:</strong></p>
            <div class="redirect-url">{{ redirect_url }}</div>
            {% endif %}
        </div>
        
        <h3>🌟 可用接口</h3>
        <div class="endpoints-grid">
            {% for endpoint in available_endpoints %}
            <a href="{{ endpoint.url }}" class="endpoint-card">
                <h4>{{ endpoint.url }}</h4>
                <p>{{ endpoint.description }}</p>
            </a>
            {% endfor %}
        </div>
        
        <div class="migration-info">
            <h3>📋 迁移信息</h3>
            <div class="migration-details">
                <div class="migration-detail">
                    <strong>原接口</strong>
                    <span>{{ migration_info.old_endpoint }}</span>
                </div>
                <div class="migration-detail">
                    <strong>新框架</strong>
                    <span>{{ migration_info.new_framework }}</span>
                </div>
                <div class="migration-detail">
                    <strong>迁移时间</strong>
                    <span>{{ migration_info.migration_date }}</span>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="/" class="btn">返回首页</a>
            <a href="/api/health" class="btn btn-secondary">系统状态</a>
        </div>
    </div>
    
    {% if redirect_url %}
    <script>
        // 自动重定向倒计时
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '{{ redirect_url }}';
            }
        }, 1000);
        
        // 点击任意地方取消自动重定向
        document.addEventListener('click', () => {
            clearInterval(timer);
            countdownElement.textContent = '已取消';
        });
    </script>
    {% endif %}
</body>
</html>