<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Producer Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0,0,0,0.15);
        }
        
        .stat-card h3 {
            color: #667eea;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-card .value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .description {
            color: #666;
            font-size: 0.9rem;
        }
        
        .navigation {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .nav-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0,0,0,0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .nav-card h3 {
            color: #667eea;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .nav-card .icon {
            width: 24px;
            height: 24px;
            margin-right: 0.5rem;
            background: #667eea;
            border-radius: 4px;
            display: inline-block;
        }
        
        .nav-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .nav-card .features {
            margin-top: 1rem;
            list-style: none;
        }
        
        .nav-card .features li {
            color: #888;
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
            padding-left: 1rem;
            position: relative;
        }
        
        .nav-card .features li:before {
            content: '•';
            color: #667eea;
            position: absolute;
            left: 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 3rem;
            color: white;
            opacity: 0.8;
        }
        
        .health-status {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .health-status.healthy {
            background: #d4edda;
            color: #155724;
        }
        
        .health-status.unhealthy {
            background: #f8d7da;
            color: #721c24;
        }
        
        .health-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .health-indicator.healthy {
            background: #28a745;
        }
        
        .health-indicator.unhealthy {
            background: #dc3545;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid,
            .navigation {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>{{ page_title }}</h1>
            <p>{{ page_description }}</p>
            <div class="health-status {{ 'healthy' if system_health.status == 'healthy' else 'unhealthy' }}">
                <span class="health-indicator {{ 'healthy' if system_health.status == 'healthy' else 'unhealthy' }}"></span>
                系统状态: {{ system_health.message }}
            </div>
        </header>
        
        <section class="stats-grid">
            <div class="stat-card">
                <h3>测试结果</h3>
                <div class="value">{{ stats.test_results }}</div>
                <div class="description">已完成的测试结果数量</div>
            </div>
            
            <div class="stat-card">
                <h3>剧本项目</h3>
                <div class="value">{{ stats.script_projects }}</div>
                <div class="description">管理中的剧本项目</div>
            </div>
            
            <div class="stat-card">
                <h3>系统运行时间</h3>
                <div class="value">{{ stats.uptime }}</div>
                <div class="description">系统持续运行时间</div>
            </div>
            
            <div class="stat-card">
                <h3>最后更新</h3>
                <div class="value">{{ stats.last_update }}</div>
                <div class="description">数据最后更新时间</div>
            </div>
        </section>
        
        <section class="navigation">
            <a href="/results" class="nav-card">
                <h3>
                    <span class="icon"></span>
                    测试结果管理
                </h3>
                <p>查看和管理所有测试结果，包括详细的测试报告和统计信息。</p>
                <ul class="features">
                    <li>测试结果列表和详情</li>
                    <li>测试统计和分析</li>
                    <li>结果导出和分享</li>
                </ul>
            </a>
            
            <a href="/scripts" class="nav-card">
                <h3>
                    <span class="icon"></span>
                    剧本项目管理
                </h3>
                <p>管理剧本项目，查看项目详情和进度状态。</p>
                <ul class="features">
                    <li>项目列表和筛选</li>
                    <li>项目详情和状态</li>
                    <li>项目统计信息</li>
                </ul>
            </a>
            
            <a href="/api/health" class="nav-card">
                <h3>
                    <span class="icon"></span>
                    系统监控
                </h3>
                <p>监控系统健康状态和性能指标。</p>
                <ul class="features">
                    <li>系统健康检查</li>
                    <li>性能监控</li>
                    <li>API状态检查</li>
                </ul>
            </a>
        </section>
        
        <footer class="footer">
            <p>Producer Web Framework - 优雅的内容管理平台</p>
            <p>版本: {{ version }} | 构建时间: {{ build_time }}</p>
        </footer>
    </div>
</body>
</html>