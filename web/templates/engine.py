"""模板引擎

基于Jinja2的模板渲染引擎，支持模板继承、组件化和自定义过滤器。
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path

try:
    from jinja2 import Environment, FileSystemLoader, select_autoescape
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

from templates.context import TemplateContext
from templates.filters import register_custom_filters


class SimpleTemplateEngine:
    """简单模板引擎（当Jinja2不可用时的后备方案）"""
    
    def __init__(self, template_dir: Path):
        self.template_dir = template_dir
    
    def render(self, template_name: str, context: Dict[str, Any] = None) -> str:
        """渲染模板"""
        context = context or {}
        template_path = self.template_dir / template_name
        
        if not template_path.exists():
            return self._render_fallback_template(template_name, context)
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 简单的变量替换
            for key, value in context.items():
                placeholder = f"{{{{{key}}}}}"
                template_content = template_content.replace(placeholder, str(value))
            
            return template_content
        
        except Exception as e:
            return self._render_error_template(template_name, str(e))
    
    def _render_fallback_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """渲染后备模板"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>{context.get('title', 'Producer Web')}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .error {{ color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="error">
            <h2>模板未找到</h2>
            <p>模板文件 <code>{template_name}</code> 不存在。</p>
            <h3>上下文数据:</h3>
            <pre>{context}</pre>
        </div>
    </div>
</body>
</html>
        """.strip()
    
    def _render_error_template(self, template_name: str, error: str) -> str:
        """渲染错误模板"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>模板错误</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .error {{ color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="error">
        <h2>模板渲染错误</h2>
        <p>模板 <code>{template_name}</code> 渲染失败。</p>
        <p><strong>错误信息:</strong> {error}</p>
    </div>
</body>
</html>
        """.strip()


class TemplateEngine:
    """模板引擎"""
    
    def __init__(self, config=None):
        self.config = config
        
        # 处理config为None的情况
        if config is None:
            self.template_dir = Path('web/templates')
            self.template_auto_reload = True
            self.template_cache_size = 400
            self.debug = True
        else:
            self.template_dir = getattr(config, 'TEMPLATES_DIR', Path('web/templates'))
            self.template_auto_reload = getattr(config, 'TEMPLATE_AUTO_RELOAD', True)
            self.template_cache_size = getattr(config, 'TEMPLATE_CACHE_SIZE', 400)
            self.debug = getattr(config.server if hasattr(config, 'server') else config, 'debug', True)
        
        # 确保模板目录存在
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        if JINJA2_AVAILABLE:
            self._setup_jinja2()
        else:
            self._setup_simple_engine()
    
    def _setup_jinja2(self):
        """设置Jinja2环境"""
        self.env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=select_autoescape(['html', 'xml']),
            auto_reload=self.template_auto_reload,
            cache_size=self.template_cache_size,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 注册自定义过滤器
        register_custom_filters(self.env)
        
        # 添加全局变量
        self.env.globals.update({
            'config': self.config,
            'app_name': getattr(self.config, 'name', 'Web应用') if self.config else 'Web应用',
            'app_version': getattr(self.config, 'version', '1.0.0') if self.config else '1.0.0',
            'debug': self.debug
        })
        
        self.engine_type = 'jinja2'
    
    def _setup_simple_engine(self):
        """设置简单模板引擎"""
        self.simple_engine = SimpleTemplateEngine(self.template_dir)
        self.engine_type = 'simple'
        
        print("⚠️  Jinja2未安装，使用简单模板引擎。建议安装Jinja2以获得完整功能。")
    
    def render(self, template_name: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """渲染模板"""
        # 合并上下文
        template_context = TemplateContext(self.config)
        template_context.update(context or {})
        template_context.update(kwargs)
        
        if self.engine_type == 'jinja2':
            return self._render_jinja2(template_name, template_context.to_dict())
        else:
            return self._render_simple(template_name, template_context.to_dict())
    
    def _render_jinja2(self, template_name: str, context: Dict[str, Any]) -> str:
        """使用Jinja2渲染模板"""
        try:
            template = self.env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            # 安全地检查debug模式
            debug_mode = False
            if self.config:
                if hasattr(self.config, 'server') and hasattr(self.config.server, 'debug'):
                    debug_mode = self.config.server.debug
                elif hasattr(self.config, 'debug'):
                    debug_mode = self.config.debug
            
            if debug_mode:
                raise
            return self._render_error_template(template_name, str(e))
    
    def _render_simple(self, template_name: str, context: Dict[str, Any]) -> str:
        """使用简单引擎渲染模板"""
        return self.simple_engine.render(template_name, context)
    
    def _render_error_template(self, template_name: str, error: str) -> str:
        """渲染错误模板"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>模板错误 - {self.config.name}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0; padding: 40px; background: #f8f9fa;
        }}
        .error-container {{ 
            max-width: 800px; margin: 0 auto; background: white;
            border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .error-header {{ 
            background: #e74c3c; color: white; padding: 20px;
        }}
        .error-body {{ padding: 30px; }}
        .error-code {{ font-family: 'Monaco', 'Consolas', monospace; }}
        pre {{ 
            background: #f8f9fa; padding: 15px; border-radius: 5px;
            overflow-x: auto; border-left: 4px solid #e74c3c;
        }}
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-header">
            <h1>🚨 模板渲染错误</h1>
        </div>
        <div class="error-body">
            <p><strong>模板文件:</strong> <code>{template_name}</code></p>
            <p><strong>错误信息:</strong></p>
            <pre class="error-code">{error}</pre>
            <hr>
            <p><small>💡 提示: 检查模板语法或确保模板文件存在</small></p>
        </div>
    </div>
</body>
</html>
        """.strip()
    
    def render_string(self, template_string: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """渲染模板字符串"""
        template_context = TemplateContext(self.config)
        template_context.update(context or {})
        template_context.update(kwargs)
        
        if self.engine_type == 'jinja2':
            try:
                template = self.env.from_string(template_string)
                return template.render(**template_context.to_dict())
            except Exception as e:
                # 安全地检查debug模式
                debug_mode = False
                if self.config:
                    if hasattr(self.config, 'server') and hasattr(self.config.server, 'debug'):
                        debug_mode = self.config.server.debug
                    elif hasattr(self.config, 'debug'):
                        debug_mode = self.config.debug
                
                if debug_mode:
                    raise
                return f"模板渲染错误: {str(e)}"
        else:
            # 简单字符串替换
            result = template_string
            for key, value in template_context.to_dict().items():
                placeholder = f"{{{{{key}}}}}"
                result = result.replace(placeholder, str(value))
            return result
    
    def template_exists(self, template_name: str) -> bool:
        """检查模板是否存在"""
        template_path = self.template_dir / template_name
        return template_path.exists()
    
    def list_templates(self) -> list:
        """列出所有模板文件"""
        templates = []
        for root, dirs, files in os.walk(self.template_dir):
            for file in files:
                if file.endswith(('.html', '.htm', '.j2', '.jinja', '.jinja2')):
                    rel_path = os.path.relpath(os.path.join(root, file), self.template_dir)
                    templates.append(rel_path.replace(os.sep, '/'))
        return sorted(templates)
    
    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息"""
        info = {
            'engine_type': self.engine_type,
            'template_dir': str(self.template_dir),
            'auto_reload': getattr(self.config, 'TEMPLATE_AUTO_RELOAD', True) if self.config else True,
            'cache_size': getattr(self.config, 'TEMPLATE_CACHE_SIZE', 400) if self.config else 400,
            'templates_count': len(self.list_templates())
        }
        
        if self.engine_type == 'jinja2':
            info['jinja2_version'] = getattr(__import__('jinja2'), '__version__', 'unknown')
        
        return info