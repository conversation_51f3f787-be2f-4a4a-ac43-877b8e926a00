{% extends "base.html" %}

{% block title %}搜索 - 文件管理器{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .search-header {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .search-form {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .search-input {
        flex: 1;
        padding: 0.75rem 1rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1rem;
    }
    
    .search-input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    }
    
    .search-btn {
        padding: 0.75rem 2rem;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: background 0.2s;
    }
    
    .search-btn:hover {
        background: #0056b3;
    }
    
    .search-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }
    
    .search-options {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: center;
    }
    
    .search-option {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .search-option input[type="checkbox"] {
        margin: 0;
    }
    
    .search-option label {
        margin: 0;
        font-size: 0.9rem;
        color: #666;
    }
    
    .search-filters {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .filter-group label {
        font-size: 0.8rem;
        color: #666;
        margin: 0;
    }
    
    .filter-group select,
    .filter-group input {
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 0.9rem;
    }
    
    .search-results {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .results-header {
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .results-count {
        font-weight: 500;
        color: #333;
    }
    
    .results-time {
        font-size: 0.9rem;
        color: #666;
    }
    
    .results-list {
        max-height: 600px;
        overflow-y: auto;
    }
    
    .result-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.2s;
        cursor: pointer;
    }
    
    .result-item:hover {
        background: #f8f9fa;
    }
    
    .result-item:last-child {
        border-bottom: none;
    }
    
    .result-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }
    
    .result-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
    }
    
    .result-name {
        font-weight: 500;
        color: #333;
        flex: 1;
    }
    
    .result-size {
        font-size: 0.9rem;
        color: #666;
    }
    
    .result-path {
        font-size: 0.9rem;
        color: #666;
        font-family: 'Monaco', 'Menlo', monospace;
        margin-bottom: 0.5rem;
    }
    
    .result-preview {
        font-size: 0.9rem;
        color: #333;
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
        border-left: 3px solid #007bff;
        white-space: pre-wrap;
        max-height: 100px;
        overflow: hidden;
    }
    
    .result-highlight {
        background: #fff3cd;
        padding: 0.1rem 0.2rem;
        border-radius: 2px;
    }
    
    .no-results {
        text-align: center;
        padding: 3rem;
        color: #666;
    }
    
    .no-results i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #ccc;
    }
    
    .loading {
        text-align: center;
        padding: 2rem;
        color: #666;
    }
    
    .loading i {
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .search-tips {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .search-tips h6 {
        margin: 0 0 0.5rem 0;
        color: #1976d2;
    }
    
    .search-tips ul {
        margin: 0;
        padding-left: 1.5rem;
    }
    
    .search-tips li {
        font-size: 0.9rem;
        color: #1565c0;
        margin-bottom: 0.25rem;
    }
    
    @media (max-width: 768px) {
        .search-container {
            padding: 1rem;
        }
        
        .search-form {
            flex-direction: column;
        }
        
        .search-options {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .search-filters {
            flex-direction: column;
        }
        
        .results-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="search-container">
    <!-- 搜索表单 -->
    <div class="search-header">
        <form class="search-form" id="searchForm">
            <input type="text" class="search-input" id="searchQuery" 
                   placeholder="输入搜索关键词..." value="{{ query or '' }}" title="输入搜索关键词">
            <button type="submit" class="search-btn" id="searchBtn" title="开始搜索">
                <i class="fas fa-search"></i> 搜索
            </button>
        </form>
        
        <!-- 搜索选项 -->
        <div class="search-options">
            <div class="search-option">
                <input type="checkbox" id="caseSensitive">
                <label for="caseSensitive">区分大小写</label>
            </div>
            <div class="search-option">
                <input type="checkbox" id="useRegex">
                <label for="useRegex">正则表达式</label>
            </div>
            <div class="search-option">
                <input type="checkbox" id="searchContent" checked>
                <label for="searchContent">搜索文件内容</label>
            </div>
            <div class="search-option">
                <input type="checkbox" id="includeHidden">
                <label for="includeHidden">包含隐藏文件</label>
            </div>
        </div>
        
        <!-- 搜索过滤器 -->
        <div class="search-filters">
            <div class="filter-group">
                <label>文件类型</label>
                <select id="fileType" title="选择文件类型">
                    <option value="">所有类型</option>
                    <option value="text">文本文件</option>
                    <option value="image">图片文件</option>
                    <option value="video">视频文件</option>
                    <option value="audio">音频文件</option>
                    <option value="document">文档文件</option>
                    <option value="archive">压缩文件</option>
                </select>
            </div>
            <div class="filter-group">
                <label>文件大小</label>
                <select id="fileSize" title="选择文件大小范围">
                    <option value="">任意大小</option>
                    <option value="small">小于 1MB</option>
                    <option value="medium">1MB - 10MB</option>
                    <option value="large">10MB - 100MB</option>
                    <option value="huge">大于 100MB</option>
                </select>
            </div>
            <div class="filter-group">
                <label>修改时间</label>
                <select id="modifiedTime" title="选择修改时间范围">
                    <option value="">任意时间</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="year">今年</option>
                </select>
            </div>
            <div class="filter-group">
                <label>搜索路径</label>
                <input type="text" id="searchPath" placeholder="留空搜索所有位置" title="指定搜索路径">
            </div>
        </div>
        
        <!-- 搜索提示 -->
        <div class="search-tips">
            <h6>搜索提示</h6>
            <ul>
                <li>使用引号搜索精确短语："exact phrase"</li>
                <li>使用 * 作为通配符：*.txt 或 image*</li>
                <li>使用 - 排除关键词：python -test</li>
                <li>正则表达式示例：\.(js|css)$ 搜索 JS 和 CSS 文件</li>
            </ul>
        </div>
    </div>
    
    <!-- 搜索结果 -->
    <div class="search-results" id="searchResults" style="display: none;">
        <div class="results-header">
            <div class="results-count" id="resultsCount">找到 0 个结果</div>
            <div class="results-time" id="resultsTime">搜索用时: 0ms</div>
        </div>
        <div class="results-list" id="resultsList">
            <!-- 搜索结果将在这里显示 -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class SearchManager {
    constructor() {
        this.searchForm = document.getElementById('searchForm');
        this.searchQuery = document.getElementById('searchQuery');
        this.searchBtn = document.getElementById('searchBtn');
        this.searchResults = document.getElementById('searchResults');
        this.resultsList = document.getElementById('resultsList');
        this.resultsCount = document.getElementById('resultsCount');
        this.resultsTime = document.getElementById('resultsTime');
        
        this.initializeEvents();
        
        // 如果有初始查询，执行搜索
        if (this.searchQuery.value.trim()) {
            this.performSearch();
        }
    }
    
    initializeEvents() {
        // 搜索表单提交
        this.searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });
        
        // 实时搜索（延迟执行）
        let searchTimeout;
        this.searchQuery.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.searchQuery.value.trim().length >= 2) {
                    this.performSearch();
                }
            }, 500);
        });
        
        // 过滤器变化时重新搜索
        const filters = ['caseSensitive', 'useRegex', 'searchContent', 'includeHidden', 
                        'fileType', 'fileSize', 'modifiedTime', 'searchPath'];
        filters.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    if (this.searchQuery.value.trim()) {
                        this.performSearch();
                    }
                });
            }
        });
    }
    
    async performSearch() {
        const query = this.searchQuery.value.trim();
        if (!query) {
            this.searchResults.style.display = 'none';
            return;
        }
        
        this.showLoading();
        
        try {
            const params = this.getSearchParams();
            const response = await fetch('/api/files/search?' + new URLSearchParams(params));
            const data = await response.json();
            
            if (data.success) {
                this.displayResults(data.data.results, data.data.search_time);
            } else {
                this.showError(data.message || '搜索失败');
            }
        } catch (error) {
            console.error('搜索错误:', error);
            this.showError('搜索请求失败');
        }
    }
    
    getSearchParams() {
        return {
            q: this.searchQuery.value.trim(),
            case_sensitive: document.getElementById('caseSensitive').checked,
            use_regex: document.getElementById('useRegex').checked,
            search_content: document.getElementById('searchContent').checked,
            include_hidden: document.getElementById('includeHidden').checked,
            file_type: document.getElementById('fileType').value,
            file_size: document.getElementById('fileSize').value,
            modified_time: document.getElementById('modifiedTime').value,
            search_path: document.getElementById('searchPath').value
        };
    }
    
    showLoading() {
        this.searchResults.style.display = 'block';
        this.searchBtn.disabled = true;
        this.searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 搜索中...';
        
        this.resultsList.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner"></i>
                正在搜索，请稍候...
            </div>
        `;
    }
    
    displayResults(results, searchTime) {
        this.searchBtn.disabled = false;
        this.searchBtn.innerHTML = '<i class="fas fa-search"></i> 搜索';
        
        this.resultsCount.textContent = `找到 ${results.length} 个结果`;
        this.resultsTime.textContent = `搜索用时: ${searchTime}ms`;
        
        if (results.length === 0) {
            this.resultsList.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h5>未找到匹配的文件</h5>
                    <p>尝试调整搜索关键词或搜索选项</p>
                </div>
            `;
            return;
        }
        
        this.resultsList.innerHTML = results.map(result => this.renderResultItem(result)).join('');
        
        // 添加点击事件
        this.resultsList.querySelectorAll('.result-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.openFile(results[index]);
            });
        });
    }
    
    renderResultItem(result) {
        const icon = this.getFileIcon(result.name, result.is_directory);
        const size = result.is_directory ? '' : this.formatFileSize(result.size);
        const preview = result.preview ? `<div class="result-preview">${this.highlightMatches(result.preview)}</div>` : '';
        
        return `
            <div class="result-item" data-path="${result.path}">
                <div class="result-header">
                    <div class="result-icon">
                        <i class="${icon}"></i>
                    </div>
                    <div class="result-name">${this.highlightMatches(result.name)}</div>
                    <div class="result-size">${size}</div>
                </div>
                <div class="result-path">${result.path}</div>
                ${preview}
            </div>
        `;
    }
    
    getFileIcon(filename, isDirectory) {
        if (isDirectory) return 'fas fa-folder';
        
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            // 文本文件
            'txt': 'fas fa-file-alt',
            'md': 'fab fa-markdown',
            'json': 'fas fa-file-code',
            'xml': 'fas fa-file-code',
            'csv': 'fas fa-file-csv',
            
            // 代码文件
            'js': 'fab fa-js-square',
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'py': 'fab fa-python',
            'java': 'fab fa-java',
            'php': 'fab fa-php',
            
            // 图片文件
            'jpg': 'fas fa-file-image',
            'jpeg': 'fas fa-file-image',
            'png': 'fas fa-file-image',
            'gif': 'fas fa-file-image',
            'svg': 'fas fa-file-image',
            
            // 文档文件
            'pdf': 'fas fa-file-pdf',
            'doc': 'fas fa-file-word',
            'docx': 'fas fa-file-word',
            'xls': 'fas fa-file-excel',
            'xlsx': 'fas fa-file-excel',
            'ppt': 'fas fa-file-powerpoint',
            'pptx': 'fas fa-file-powerpoint',
            
            // 压缩文件
            'zip': 'fas fa-file-archive',
            'rar': 'fas fa-file-archive',
            '7z': 'fas fa-file-archive',
            'tar': 'fas fa-file-archive',
            'gz': 'fas fa-file-archive',
            
            // 音视频文件
            'mp3': 'fas fa-file-audio',
            'wav': 'fas fa-file-audio',
            'mp4': 'fas fa-file-video',
            'avi': 'fas fa-file-video',
            'mov': 'fas fa-file-video'
        };
        
        return iconMap[ext] || 'fas fa-file';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    highlightMatches(text) {
        const query = this.searchQuery.value.trim();
        if (!query || !text) return text;
        
        const caseSensitive = document.getElementById('caseSensitive').checked;
        const useRegex = document.getElementById('useRegex').checked;
        
        try {
            let pattern;
            if (useRegex) {
                pattern = new RegExp(query, caseSensitive ? 'g' : 'gi');
            } else {
                const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                pattern = new RegExp(escapedQuery, caseSensitive ? 'g' : 'gi');
            }
            
            return text.replace(pattern, '<span class="result-highlight">$&</span>');
        } catch (error) {
            return text;
        }
    }
    
    openFile(result) {
        if (result.is_directory) {
            window.location.href = `/?path=${encodeURIComponent(result.path)}`;
        } else {
            window.location.href = `/file_viewer?path=${encodeURIComponent(result.path)}`;
        }
    }
    
    showError(message) {
        this.searchBtn.disabled = false;
        this.searchBtn.innerHTML = '<i class="fas fa-search"></i> 搜索';
        
        this.resultsList.innerHTML = `
            <div class="no-results">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>搜索出错</h5>
                <p>${message}</p>
            </div>
        `;
    }
}

// 初始化搜索管理器
document.addEventListener('DOMContentLoaded', () => {
    new SearchManager();
});

// 快捷键支持
document.addEventListener('keydown', (e) => {
    // Escape 键清空搜索
    if (e.key === 'Escape') {
        document.getElementById('searchQuery').value = '';
        document.getElementById('searchResults').style.display = 'none';
    }
});
</script>
{% endblock %}