{% extends "base.html" %}

{% block title %}设置 - {{ app_name }}{% endblock %}

{% block extra_css %}
<style>
    .settings-container {
        max-width: 900px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .settings-nav {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }
    
    .settings-nav .nav-link {
        color: #495057;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        margin-right: 0.5rem;
        transition: all 0.2s ease;
    }
    
    .settings-nav .nav-link:hover {
        background: #e9ecef;
        color: #007bff;
    }
    
    .settings-nav .nav-link.active {
        background: #007bff;
        color: #ffffff;
    }
    
    .settings-section {
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        display: none;
    }
    
    .settings-section.active {
        display: block;
    }
    
    .section-header {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .section-description {
        color: #6c757d;
        margin: 0;
    }
    
    .setting-group {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .setting-group:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .setting-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .setting-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }
    
    .setting-control {
        max-width: 400px;
    }
    
    .theme-preview {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .theme-option {
        border: 2px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        min-width: 120px;
    }
    
    .theme-option:hover {
        border-color: #007bff;
    }
    
    .theme-option.selected {
        border-color: #007bff;
        background: #e7f3ff;
    }
    
    .theme-preview-box {
        width: 60px;
        height: 40px;
        border-radius: 0.25rem;
        margin: 0 auto 0.5rem;
        position: relative;
        overflow: hidden;
    }
    
    .theme-light .theme-preview-box {
        background: linear-gradient(135deg, #ffffff 50%, #f8f9fa 50%);
        border: 1px solid #dee2e6;
    }
    
    .theme-dark .theme-preview-box {
        background: linear-gradient(135deg, #343a40 50%, #495057 50%);
    }
    
    .theme-auto .theme-preview-box {
        background: linear-gradient(90deg, #ffffff 50%, #343a40 50%);
        border: 1px solid #dee2e6;
    }
    
    .storage-info {
        background: #f8f9fa;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .storage-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .storage-item:last-child {
        border-bottom: none;
    }
    
    .storage-label {
        font-weight: 500;
        color: #495057;
    }
    
    .storage-value {
        color: #6c757d;
    }
    
    .danger-zone {
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 0.375rem;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .danger-zone h6 {
        color: #c53030;
        margin-bottom: 1rem;
    }
    
    .btn-group-toggle .btn {
        border-radius: 0.375rem !important;
    }
    
    .btn-group-toggle .btn:not(:last-child) {
        margin-right: 0.5rem;
    }
    
    .save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 1050;
    }
    
    .save-indicator.show {
        transform: translateX(0);
    }
    
    @media (max-width: 768px) {
        .settings-container {
            margin: 1rem auto;
        }
        
        .settings-nav {
            overflow-x: auto;
        }
        
        .settings-nav .nav {
            flex-wrap: nowrap;
        }
        
        .theme-preview {
            flex-direction: column;
        }
        
        .storage-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="settings-container">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-cog"></i> 设置</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="/" class="text-decoration-none">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                </li>
            </ol>
        </nav>
    </div>
    
    <!-- 设置导航 -->
    <div class="settings-nav">
        <ul class="nav nav-pills" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="pill" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-sliders-h"></i> 常规
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="appearance-tab" data-bs-toggle="pill" data-bs-target="#appearance" type="button" role="tab">
                    <i class="fas fa-palette"></i> 外观
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab">
                    <i class="fas fa-shield-alt"></i> 安全
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="storage-tab" data-bs-toggle="pill" data-bs-target="#storage" type="button" role="tab">
                    <i class="fas fa-hdd"></i> 存储
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="advanced-tab" data-bs-toggle="pill" data-bs-target="#advanced" type="button" role="tab">
                    <i class="fas fa-cogs"></i> 高级
                </button>
            </li>
        </ul>
    </div>
    
    <!-- 设置内容 -->
    <div class="tab-content">
        <!-- 常规设置 -->
        <div class="tab-pane fade show active" id="general" role="tabpanel">
            <div class="settings-section active">
                <div class="section-header">
                    <h3 class="section-title">常规设置</h3>
                    <p class="section-description">配置应用的基本行为和默认选项</p>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">默认视图模式</label>
                    <p class="setting-description">选择文件列表的默认显示方式</p>
                    <div class="setting-control">
                        <div class="btn-group btn-group-toggle" data-bs-toggle="buttons">
                            <input type="radio" class="btn-check" name="viewMode" id="viewList" value="list" checked>
                            <label class="btn btn-outline-primary" for="viewList">
                                <i class="fas fa-list"></i> 列表
                            </label>
                            
                            <input type="radio" class="btn-check" name="viewMode" id="viewGrid" value="grid">
                            <label class="btn btn-outline-primary" for="viewGrid">
                                <i class="fas fa-th"></i> 网格
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label" for="itemsPerPage">每页显示项目数</label>
                    <p class="setting-description">设置文件列表每页显示的文件数量</p>
                    <div class="setting-control">
                        <select class="form-select" id="itemsPerPage">
                            <option value="25">25</option>
                            <option value="50" selected>50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                        </select>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">文件操作确认</label>
                    <p class="setting-description">在执行危险操作时显示确认对话框</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" checked>
                            <label class="form-check-label" for="confirmDelete">
                                删除文件时确认
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="confirmOverwrite" checked>
                            <label class="form-check-label" for="confirmOverwrite">
                                覆盖文件时确认
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">自动刷新</label>
                    <p class="setting-description">自动刷新文件列表以显示最新内容</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefresh">
                            <label class="form-check-label" for="autoRefresh">
                                启用自动刷新
                            </label>
                        </div>
                        <select class="form-select mt-2" id="refreshInterval" disabled title="选择自动刷新间隔">
                            <option value="30">30秒</option>
                            <option value="60" selected>1分钟</option>
                            <option value="300">5分钟</option>
                            <option value="600">10分钟</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 外观设置 -->
        <div class="tab-pane fade" id="appearance" role="tabpanel">
            <div class="settings-section">
                <div class="section-header">
                    <h3 class="section-title">外观设置</h3>
                    <p class="section-description">自定义应用的外观和主题</p>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">主题模式</label>
                    <p class="setting-description">选择应用的颜色主题</p>
                    <div class="setting-control">
                        <div class="theme-preview">
                            <div class="theme-option theme-light selected" data-theme="light">
                                <div class="theme-preview-box"></div>
                                <div>浅色</div>
                            </div>
                            <div class="theme-option theme-dark" data-theme="dark">
                                <div class="theme-preview-box"></div>
                                <div>深色</div>
                            </div>
                            <div class="theme-option theme-auto" data-theme="auto">
                                <div class="theme-preview-box"></div>
                                <div>自动</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label" for="fontSize">字体大小</label>
                    <p class="setting-description">调整界面文字的大小</p>
                    <div class="setting-control">
                        <select class="form-select" id="fontSize">
                            <option value="small">小</option>
                            <option value="medium" selected>中</option>
                            <option value="large">大</option>
                        </select>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">界面密度</label>
                    <p class="setting-description">调整界面元素的间距</p>
                    <div class="setting-control">
                        <div class="btn-group btn-group-toggle" data-bs-toggle="buttons">
                            <input type="radio" class="btn-check" name="density" id="densityCompact" value="compact">
                            <label class="btn btn-outline-primary" for="densityCompact">
                                紧凑
                            </label>
                            
                            <input type="radio" class="btn-check" name="density" id="densityNormal" value="normal" checked>
                            <label class="btn btn-outline-primary" for="densityNormal">
                                正常
                            </label>
                            
                            <input type="radio" class="btn-check" name="density" id="densityComfortable" value="comfortable">
                            <label class="btn btn-outline-primary" for="densityComfortable">
                                舒适
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">显示选项</label>
                    <p class="setting-description">控制界面元素的显示</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showHiddenFiles">
                            <label class="form-check-label" for="showHiddenFiles">
                                显示隐藏文件
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showFileExtensions" checked>
                            <label class="form-check-label" for="showFileExtensions">
                                显示文件扩展名
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showFileSizes" checked>
                            <label class="form-check-label" for="showFileSizes">
                                显示文件大小
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div class="tab-pane fade" id="security" role="tabpanel">
            <div class="settings-section">
                <div class="section-header">
                    <h3 class="section-title">安全设置</h3>
                    <p class="section-description">配置访问控制和安全选项</p>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">访问控制</label>
                    <p class="setting-description">控制对文件系统的访问权限</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="readOnlyMode">
                            <label class="form-check-label" for="readOnlyMode">
                                只读模式
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="restrictToHome" checked>
                            <label class="form-check-label" for="restrictToHome">
                                限制在用户目录
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">文件类型限制</label>
                    <p class="setting-description">限制可以访问的文件类型</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="blockExecutables" checked>
                            <label class="form-check-label" for="blockExecutables">
                                阻止可执行文件
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="blockSystemFiles" checked>
                            <label class="form-check-label" for="blockSystemFiles">
                                阻止系统文件
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label" for="maxFileSize">最大文件大小</label>
                    <p class="setting-description">限制可以上传的单个文件大小</p>
                    <div class="setting-control">
                        <select class="form-select" id="maxFileSize">
                            <option value="10">10 MB</option>
                            <option value="50">50 MB</option>
                            <option value="100" selected>100 MB</option>
                            <option value="500">500 MB</option>
                            <option value="1000">1 GB</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 存储设置 -->
        <div class="tab-pane fade" id="storage" role="tabpanel">
            <div class="settings-section">
                <div class="section-header">
                    <h3 class="section-title">存储设置</h3>
                    <p class="section-description">管理存储空间和缓存</p>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">存储信息</label>
                    <p class="setting-description">查看当前存储使用情况</p>
                    <div class="storage-info">
                        <div class="storage-item">
                            <span class="storage-label">总空间</span>
                            <span class="storage-value" id="totalSpace">加载中...</span>
                        </div>
                        <div class="storage-item">
                            <span class="storage-label">已使用</span>
                            <span class="storage-value" id="usedSpace">加载中...</span>
                        </div>
                        <div class="storage-item">
                            <span class="storage-label">可用空间</span>
                            <span class="storage-value" id="freeSpace">加载中...</span>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">缓存管理</label>
                    <p class="setting-description">管理应用缓存以提高性能</p>
                    <div class="setting-control">
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="clearThumbnailCache()">
                                <i class="fas fa-trash"></i> 清除缩略图缓存
                            </button>
                            <button class="btn btn-outline-primary" onclick="clearPreviewCache()">
                                <i class="fas fa-trash"></i> 清除预览缓存
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">自动清理</label>
                    <p class="setting-description">自动清理临时文件和缓存</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoCleanup" checked>
                            <label class="form-check-label" for="autoCleanup">
                                启用自动清理
                            </label>
                        </div>
                        <select class="form-select mt-2" id="cleanupInterval">
                            <option value="1">每天</option>
                            <option value="7" selected>每周</option>
                            <option value="30">每月</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 高级设置 -->
        <div class="tab-pane fade" id="advanced" role="tabpanel">
            <div class="settings-section">
                <div class="section-header">
                    <h3 class="section-title">高级设置</h3>
                    <p class="section-description">高级用户选项和实验性功能</p>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">性能优化</label>
                    <p class="setting-description">调整性能相关设置</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableLazyLoading" checked>
                            <label class="form-check-label" for="enableLazyLoading">
                                启用懒加载
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableVirtualScrolling">
                            <label class="form-check-label" for="enableVirtualScrolling">
                                启用虚拟滚动
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">调试选项</label>
                    <p class="setting-description">开发和调试相关设置</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableDebugMode">
                            <label class="form-check-label" for="enableDebugMode">
                                启用调试模式
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableConsoleLogging">
                            <label class="form-check-label" for="enableConsoleLogging">
                                启用控制台日志
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">实验性功能</label>
                    <p class="setting-description">尚在开发中的实验性功能</p>
                    <div class="setting-control">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableWebGL">
                            <label class="form-check-label" for="enableWebGL">
                                启用 WebGL 渲染
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableServiceWorker">
                            <label class="form-check-label" for="enableServiceWorker">
                                启用 Service Worker
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 危险区域 -->
                <div class="danger-zone">
                    <h6><i class="fas fa-exclamation-triangle"></i> 危险区域</h6>
                    <p class="mb-3">以下操作可能会影响应用的正常使用，请谨慎操作。</p>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-danger" onclick="resetSettings()">
                            <i class="fas fa-undo"></i> 重置所有设置
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearAllData()">
                            <i class="fas fa-trash"></i> 清除所有数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存指示器 -->
<div class="save-indicator" id="saveIndicator">
    <i class="fas fa-check"></i> 设置已保存
</div>
{% endblock %}

{% block extra_js %}
<script>
class SettingsManager {
    constructor() {
        this.settings = this.loadSettings();
        this.initializeUI();
        this.bindEvents();
        this.loadStorageInfo();
    }
    
    loadSettings() {
        const defaultSettings = {
            viewMode: 'list',
            itemsPerPage: 50,
            confirmDelete: true,
            confirmOverwrite: true,
            autoRefresh: false,
            refreshInterval: 60,
            theme: 'light',
            fontSize: 'medium',
            density: 'normal',
            showHiddenFiles: false,
            showFileExtensions: true,
            showFileSizes: true,
            readOnlyMode: false,
            restrictToHome: true,
            blockExecutables: true,
            blockSystemFiles: true,
            maxFileSize: 100,
            autoCleanup: true,
            cleanupInterval: 7,
            enableLazyLoading: true,
            enableVirtualScrolling: false,
            enableDebugMode: false,
            enableConsoleLogging: false,
            enableWebGL: false,
            enableServiceWorker: false
        };
        
        const saved = localStorage.getItem('fileManagerSettings');
        return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }
    
    saveSettings() {
        localStorage.setItem('fileManagerSettings', JSON.stringify(this.settings));
        this.showSaveIndicator();
        
        // 发送到服务器
        fetch('/api/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.settings)
        }).catch(error => {
            console.error('Failed to save settings to server:', error);
        });
    }
    
    initializeUI() {
        // 设置所有控件的初始值
        Object.keys(this.settings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = this.settings[key];
                } else if (element.type === 'radio') {
                    if (element.value === this.settings[key]) {
                        element.checked = true;
                    }
                } else {
                    element.value = this.settings[key];
                }
            }
        });
        
        // 设置主题选择
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.theme === this.settings.theme);
        });
        
        // 设置自动刷新间隔的启用状态
        document.getElementById('refreshInterval').disabled = !this.settings.autoRefresh;
    }
    
    bindEvents() {
        // 绑定所有设置控件的变化事件
        document.querySelectorAll('input, select').forEach(element => {
            if (element.id && this.settings.hasOwnProperty(element.id)) {
                element.addEventListener('change', (e) => {
                    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
                    this.updateSetting(e.target.id, value);
                });
            }
        });
        
        // 主题选择
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                this.updateSetting('theme', option.dataset.theme);
            });
        });
        
        // 视图模式单选按钮
        document.querySelectorAll('input[name="viewMode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.updateSetting('viewMode', e.target.value);
                }
            });
        });
        
        // 界面密度单选按钮
        document.querySelectorAll('input[name="density"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.updateSetting('density', e.target.value);
                }
            });
        });
        
        // 自动刷新开关
        document.getElementById('autoRefresh').addEventListener('change', (e) => {
            document.getElementById('refreshInterval').disabled = !e.target.checked;
        });
    }
    
    updateSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();
    }
    
    showSaveIndicator() {
        const indicator = document.getElementById('saveIndicator');
        indicator.classList.add('show');
        setTimeout(() => {
            indicator.classList.remove('show');
        }, 2000);
    }
    
    async loadStorageInfo() {
        try {
            const response = await fetch('/api/system/disk-usage');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('totalSpace').textContent = this.formatBytes(data.total);
                document.getElementById('usedSpace').textContent = this.formatBytes(data.used);
                document.getElementById('freeSpace').textContent = this.formatBytes(data.free);
            }
        } catch (error) {
            console.error('Failed to load storage info:', error);
            document.getElementById('totalSpace').textContent = '无法获取';
            document.getElementById('usedSpace').textContent = '无法获取';
            document.getElementById('freeSpace').textContent = '无法获取';
        }
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化设置管理器
const settingsManager = new SettingsManager();

// 清除缩略图缓存
function clearThumbnailCache() {
    if (confirm('确定要清除所有缩略图缓存吗？')) {
        fetch('/api/cache/thumbnails', { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('缩略图缓存已清除');
                } else {
                    alert('清除失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('清除时发生错误');
            });
    }
}

// 清除预览缓存
function clearPreviewCache() {
    if (confirm('确定要清除所有预览缓存吗？')) {
        fetch('/api/cache/previews', { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('预览缓存已清除');
                } else {
                    alert('清除失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('清除时发生错误');
            });
    }
}

// 重置所有设置
function resetSettings() {
    if (confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
        localStorage.removeItem('fileManagerSettings');
        location.reload();
    }
}

// 清除所有数据
function clearAllData() {
    if (confirm('确定要清除所有应用数据吗？这将删除所有设置、缓存和临时文件。此操作不可撤销。')) {
        if (confirm('这是最后一次确认。确定要继续吗？')) {
            fetch('/api/data/clear', { method: 'DELETE' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        localStorage.clear();
                        alert('所有数据已清除');
                        location.reload();
                    } else {
                        alert('清除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('清除时发生错误');
                });
        }
    }
}
</script>
{% endblock %}