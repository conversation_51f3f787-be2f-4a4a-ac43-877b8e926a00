<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{% block title %}{{ app_name }}{% endblock %}</title>
    
    <!-- Meta tags -->
    <meta name="description" content="{% block description %}Producer Web - 文件管理和预览系统{% endblock %}">
    <meta name="keywords" content="{% block keywords %}文件管理,预览,Producer{% endblock %}">
    <meta name="author" content="Producer Team">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ static_url('favicon.ico') }}">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ static_url('css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('css/main.css') }}">
    {% block extra_css %}{% endblock %}
    
    <!-- Custom styles -->
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            
            --sidebar-width: 250px;
            --header-height: 60px;
            --footer-height: 50px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .main-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            height: var(--header-height);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content-wrapper {
            margin-top: var(--header-height);
            flex: 1;
            display: flex;
        }
        
        .sidebar {
            width: var(--sidebar-width);
            background: #fff;
            border-right: 1px solid #dee2e6;
            position: fixed;
            top: var(--header-height);
            left: 0;
            bottom: 0;
            overflow-y: auto;
            z-index: 999;
        }
        
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: calc(100vh - var(--header-height) - var(--footer-height));
        }
        
        .footer {
            background: #fff;
            border-top: 1px solid #dee2e6;
            height: var(--footer-height);
            margin-left: var(--sidebar-width);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 0.875rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content,
            .footer {
                margin-left: 0;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 工具提示 */
        .tooltip {
            position: relative;
            display: inline-block;
        }
        
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
        }
        
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="main-wrapper">
        <!-- Header -->
        <header class="header">
            <div class="container-fluid h-100">
                <div class="row h-100 align-items-center">
                    <div class="col-auto">
                        <button class="btn btn-link d-md-none" id="sidebar-toggle" aria-label="切换侧边栏">
                            <i class="fas fa-bars"></i>
                        </button>
                        <a href="{{ url_for('/') }}" class="navbar-brand">
                            <i class="fas fa-cube text-primary"></i>
                            {{ app_name }}
                        </a>
                    </div>
                    
                    <div class="col">
                        {% block header_content %}
                        <!-- 搜索框 -->
                        <div class="search-box mx-auto" style="max-width: 400px;">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="搜索文件..." id="global-search">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" aria-label="搜索">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endblock %}
                    </div>
                    
                    <div class="col-auto">
                        {% block header_actions %}
                        <div class="dropdown">
                            <button class="btn btn-link dropdown-toggle" type="button" data-toggle="dropdown" aria-label="用户菜单">
                                <i class="fas fa-user-circle"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a class="dropdown-item" href="{{ url_for('/settings') }}">
                                    <i class="fas fa-cog"></i> 设置
                                </a>
                                <a class="dropdown-item" href="{{ url_for('/help') }}">
                                    <i class="fas fa-question-circle"></i> 帮助
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('/about') }}">
                                    <i class="fas fa-info-circle"></i> 关于
                                </a>
                            </div>
                        </div>
                        {% endblock %}
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                {% block sidebar %}
                <div class="p-3">
                    <h6 class="text-muted mb-3">导航</h6>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="{{ url_for('/') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                        <a class="nav-link" href="{{ url_for('/files') }}">
                            <i class="fas fa-folder"></i> 文件管理
                        </a>
                        <a class="nav-link" href="{{ url_for('/upload') }}">
                            <i class="fas fa-upload"></i> 上传文件
                        </a>
                        <a class="nav-link" href="{{ url_for('/recent') }}">
                            <i class="fas fa-clock"></i> 最近访问
                        </a>
                        <a class="nav-link" href="{{ url_for('/favorites') }}">
                            <i class="fas fa-star"></i> 收藏夹
                        </a>
                    </nav>
                    
                    <hr>
                    
                    <h6 class="text-muted mb-3">工具</h6>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="{{ url_for('/search') }}">
                            <i class="fas fa-search"></i> 高级搜索
                        </a>
                        <a class="nav-link" href="{{ url_for('/stats') }}">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </a>
                        <a class="nav-link" href="{{ url_for('/logs') }}">
                            <i class="fas fa-list-alt"></i> 操作日志
                        </a>
                    </nav>
                </div>
                {% endblock %}
            </aside>
            
            <!-- Main Content -->
            <main class="main-content">
                <!-- 面包屑导航 -->
                {% block breadcrumb %}
                {% if breadcrumb_items %}
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        {% for item in breadcrumb_items %}
                        {% if loop.last %}
                        <li class="breadcrumb-item active" aria-current="page">{{ item.name }}</li>
                        {% else %}
                        <li class="breadcrumb-item">
                            <a href="{{ item.url }}">{{ item.name }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ol>
                </nav>
                {% endif %}
                {% endblock %}
                
                <!-- 消息提示 -->
                {% block messages %}
                {% if messages %}
                <div class="messages mb-3">
                    {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="关闭">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                {% endblock %}
                
                <!-- 页面内容 -->
                {% block content %}
                <div class="text-center py-5">
                    <h1 class="display-4">欢迎使用 {{ app_name }}</h1>
                    <p class="lead">现代化的文件管理和预览系统</p>
                </div>
                {% endblock %}
            </main>
        </div>
        
        <!-- Footer -->
        <footer class="footer">
            {% block footer %}
            <div class="text-center">
                &copy; {{ now.year }} {{ app_name }} v{{ app_version }}
                {% if debug %}
                <span class="badge badge-warning ml-2">DEBUG</span>
                {% endif %}
            </div>
            {% endblock %}
        </footer>
    </div>
    
    <!-- Modals -->
    {% block modals %}{% endblock %}
    
    <!-- JavaScript -->
    <script src="{{ static_url('js/jquery.min.js') }}"></script>
    <script src="{{ static_url('js/bootstrap.bundle.min.js') }}"></script>
    
    <!-- 全局JavaScript -->
    <script>
        $(document).ready(function() {
            // 侧边栏切换
            $('#sidebar-toggle').click(function() {
                $('#sidebar').toggleClass('show');
            });
            
            // 点击外部关闭侧边栏（移动端）
            $(document).click(function(e) {
                if ($(window).width() <= 768) {
                    if (!$(e.target).closest('#sidebar, #sidebar-toggle').length) {
                        $('#sidebar').removeClass('show');
                    }
                }
            });
            
            // 全局搜索
            $('#global-search').on('keypress', function(e) {
                if (e.which === 13) {
                    var query = $(this).val().trim();
                    if (query) {
                        window.location.href = '{{ url_for("/search") }}?q=' + encodeURIComponent(query);
                    }
                }
            });
            
            // 自动隐藏消息
            $('.alert').each(function() {
                var $alert = $(this);
                setTimeout(function() {
                    $alert.fadeOut();
                }, 5000);
            });
            
            // 工具提示
            $('[data-toggle="tooltip"]').tooltip();
            
            // 确认对话框
            $('[data-confirm]').click(function(e) {
                var message = $(this).data('confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
        
        // 全局函数
        window.showLoading = function(element) {
            $(element).html('<span class="loading"></span> 加载中...');
        };
        
        window.hideLoading = function(element, originalText) {
            $(element).html(originalText || '确定');
        };
        
        window.showMessage = function(message, type) {
            type = type || 'info';
            var alertClass = type === 'error' ? 'danger' : type;
            var $alert = $('<div class="alert alert-' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message +
                '<button type="button" class="close" data-dismiss="alert">' +
                '<span aria-hidden="true">&times;</span>' +
                '</button>' +
                '</div>');
            
            $('.main-content').prepend($alert);
            
            setTimeout(function() {
                $alert.fadeOut();
            }, 5000);
        };
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>