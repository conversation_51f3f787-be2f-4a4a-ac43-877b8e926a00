{% extends "base.html" %}

{% block title %}{{ page_title }} - {{ app_name }}{% endblock %}

{% block extra_css %}
<style>
    .file-viewer {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
    }
    
    .viewer-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .file-info-header {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .file-icon-large {
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.5rem;
        font-size: 1.5rem;
    }
    
    .file-icon-large.text {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .file-icon-large.image {
        background-color: #d4edda;
        color: #155724;
    }
    
    .file-icon-large.video {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .file-icon-large.audio {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .file-icon-large.binary {
        background-color: #e6e6e6;
        color: #6c757d;
    }
    
    .file-details h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
    }
    
    .file-meta-info {
        display: flex;
        gap: 1.5rem;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .viewer-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .viewer-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    .content-area {
        flex: 1;
        overflow: auto;
        padding: 1rem;
    }
    
    .text-content {
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-height: 100%;
        overflow: auto;
    }
    
    .image-content {
        text-align: center;
        padding: 2rem;
    }
    
    .image-content img {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .video-content {
        text-align: center;
        padding: 2rem;
    }
    
    .video-content video {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 0.375rem;
    }
    
    .audio-content {
        text-align: center;
        padding: 2rem;
    }
    
    .audio-content audio {
        width: 100%;
        max-width: 500px;
    }
    
    .binary-content {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .binary-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .file-properties {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .property-row {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .property-row:last-child {
        border-bottom: none;
    }
    
    .property-label {
        font-weight: 500;
        color: #495057;
    }
    
    .property-value {
        color: #6c757d;
        word-break: break-all;
    }
    
    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "/";
        color: #6c757d;
    }
    
    .line-numbers {
        background: #f8f9fa;
        border-right: 1px solid #dee2e6;
        padding: 1rem 0.5rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        color: #6c757d;
        -webkit-user-select: none;
        user-select: none;
        min-width: 3rem;
        text-align: right;
    }
    
    .code-container {
        display: flex;
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    
    .code-content {
        flex: 1;
        padding: 1rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        white-space: pre;
        overflow: auto;
    }
    
    @media (max-width: 768px) {
        .viewer-header {
            flex-direction: column;
            align-items: stretch;
        }
        
        .file-info-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .file-meta-info {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .viewer-actions {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="file-viewer">
    <!-- 查看器头部 -->
    <div class="viewer-header">
        <div class="file-info-header">
            <div class="file-icon-large {{ file_type }}">
                {% if file_type == 'text' %}
                    <i class="fas fa-file-alt"></i>
                {% elif file_type == 'image' %}
                    <i class="fas fa-file-image"></i>
                {% elif file_type == 'video' %}
                    <i class="fas fa-file-video"></i>
                {% elif file_type == 'audio' %}
                    <i class="fas fa-file-audio"></i>
                {% else %}
                    <i class="fas fa-file"></i>
                {% endif %}
            </div>
            
            <div class="file-details">
                <h4>{{ file_info.name }}</h4>
                <div class="file-meta-info">
                    <span><i class="fas fa-hdd"></i> {{ format_file_size(file_info.size) }}</span>
                    <span><i class="fas fa-clock"></i> {{ format_datetime(file_info.modified) }}</span>
                    {% if file_info.extension %}
                        <span><i class="fas fa-tag"></i> {{ file_info.extension.upper() }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="viewer-actions">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb" class="me-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/?path={{ parent_path }}" class="text-decoration-none">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </li>
                </ol>
            </nav>
            
            <!-- 操作按钮 -->
            <button class="btn btn-outline-primary btn-sm" onclick="downloadFile()" title="下载文件">
                <i class="fas fa-download"></i> 下载
            </button>
            
            <button class="btn btn-outline-secondary btn-sm" onclick="editFile()" title="编辑文件">
                <i class="fas fa-edit"></i> 编辑
            </button>
            
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="更多操作">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="renameFile()"><i class="fas fa-edit"></i> 重命名</a></li>
                    <li><a class="dropdown-item" href="#" onclick="copyFile()"><i class="fas fa-copy"></i> 复制</a></li>
                    <li><a class="dropdown-item" href="#" onclick="moveFile()"><i class="fas fa-cut"></i> 移动</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="showProperties()"><i class="fas fa-info-circle"></i> 属性</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteFile()"><i class="fas fa-trash"></i> 删除</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- 查看器内容 -->
    <div class="viewer-content">
        <div class="content-area">
            {% if can_preview %}
                {% if file_type == 'text' %}
                    <!-- 文本文件预览 -->
                    {% if content %}
                        <div class="code-container">
                            <div class="line-numbers">
                                {% set lines = content.split('\n') %}
                                {% for line in lines %}
                                    {{ loop.index }}<br>
                                {% endfor %}
                            </div>
                            <div class="code-content">{{ content }}</div>
                        </div>
                    {% else %}
                        <div class="text-content">
                            <p class="text-muted">无法读取文件内容</p>
                        </div>
                    {% endif %}
                    
                {% elif file_type == 'image' %}
                    <!-- 图片文件预览 -->
                    <div class="image-content">
                        <img src="/api/files/content?path={{ file_path }}" alt="{{ file_info.name }}" loading="lazy">
                        <div class="mt-3">
                            <small class="text-muted">{{ file_info.name }}</small>
                        </div>
                    </div>
                    
                {% elif file_type == 'video' %}
                    <!-- 视频文件预览 -->
                    <div class="video-content">
                        <video controls>
                            <source src="/api/files/content?path={{ file_path }}" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                        <div class="mt-3">
                            <small class="text-muted">{{ file_info.name }}</small>
                        </div>
                    </div>
                    
                {% elif file_type == 'audio' %}
                    <!-- 音频文件预览 -->
                    <div class="audio-content">
                        <audio controls>
                            <source src="/api/files/content?path={{ file_path }}" type="audio/mpeg">
                            您的浏览器不支持音频播放。
                        </audio>
                        <div class="mt-3">
                            <small class="text-muted">{{ file_info.name }}</small>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <!-- 无法预览的文件 -->
                <div class="binary-content">
                    <div class="binary-icon">
                        <i class="fas fa-file"></i>
                    </div>
                    <h5>无法预览此文件</h5>
                    <p class="text-muted">此文件类型不支持在线预览，您可以下载后查看。</p>
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="downloadFile()">
                            <i class="fas fa-download"></i> 下载文件
                        </button>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 文件属性模态框 -->
<div class="modal fade" id="propertiesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">文件属性</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="file-properties">
                    <div class="property-row">
                        <span class="property-label">文件名:</span>
                        <span class="property-value">{{ file_info.name }}</span>
                    </div>
                    <div class="property-row">
                        <span class="property-label">文件大小:</span>
                        <span class="property-value">{{ format_file_size(file_info.size) }} ({{ file_info.size }} 字节)</span>
                    </div>
                    <div class="property-row">
                        <span class="property-label">文件类型:</span>
                        <span class="property-value">{{ file_info.extension or '无扩展名' }}</span>
                    </div>
                    <div class="property-row">
                        <span class="property-label">修改时间:</span>
                        <span class="property-value">{{ format_datetime(file_info.modified) }}</span>
                    </div>
                    <div class="property-row">
                        <span class="property-label">文件路径:</span>
                        <span class="property-value">{{ file_path }}</span>
                    </div>
                    {% if file_info.permissions %}
                    <div class="property-row">
                        <span class="property-label">权限:</span>
                        <span class="property-value">{{ file_info.permissions }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 重命名模态框 -->
<div class="modal fade" id="renameModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重命名文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newFileName" class="form-label">新文件名</label>
                    <input type="text" class="form-control" id="newFileName" value="{{ file_info.name }}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmRename()">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const filePath = '{{ file_path }}';
const fileName = '{{ file_info.name }}';

// 下载文件
function downloadFile() {
    const link = document.createElement('a');
    link.href = `/api/files/content?path=${encodeURIComponent(filePath)}&download=true`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 编辑文件
function editFile() {
    // 这里可以实现在线编辑功能
    alert('在线编辑功能待实现');
}

// 显示属性
function showProperties() {
    const modal = new bootstrap.Modal(document.getElementById('propertiesModal'));
    modal.show();
}

// 重命名文件
function renameFile() {
    const modal = new bootstrap.Modal(document.getElementById('renameModal'));
    modal.show();
    
    // 选中文件名（不包括扩展名）
    setTimeout(() => {
        const input = document.getElementById('newFileName');
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            input.setSelectionRange(0, lastDotIndex);
        } else {
            input.select();
        }
        input.focus();
    }, 500);
}

// 确认重命名
function confirmRename() {
    const newName = document.getElementById('newFileName').value.trim();
    
    if (!newName) {
        alert('请输入新文件名');
        return;
    }
    
    if (newName === fileName) {
        bootstrap.Modal.getInstance(document.getElementById('renameModal')).hide();
        return;
    }
    
    // 验证文件名
    if (!/^[^<>:"/\\|?*]+$/.test(newName)) {
        alert('文件名包含非法字符');
        return;
    }
    
    // 发送重命名请求
    fetch('/api/files/rename', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            old_path: filePath,
            new_name: newName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('renameModal')).hide();
            // 重定向到新文件
            const newPath = filePath.replace(fileName, newName);
            window.location.href = `/file?path=${encodeURIComponent(newPath)}`;
        } else {
            alert(data.message || '重命名失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('重命名时发生错误');
    });
}

// 复制文件
function copyFile() {
    const newPath = prompt('请输入目标路径:', filePath + '_copy');
    if (newPath && newPath !== filePath) {
        fetch('/api/files/copy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                source_path: filePath,
                target_path: newPath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('文件复制成功');
            } else {
                alert(data.message || '复制失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('复制时发生错误');
        });
    }
}

// 移动文件
function moveFile() {
    const newPath = prompt('请输入目标路径:', filePath);
    if (newPath && newPath !== filePath) {
        fetch('/api/files/move', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                source_path: filePath,
                target_path: newPath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 重定向到新位置
                window.location.href = `/file?path=${encodeURIComponent(newPath)}`;
            } else {
                alert(data.message || '移动失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('移动时发生错误');
        });
    }
}

// 删除文件
function deleteFile() {
    if (confirm(`确定要删除文件 "${fileName}" 吗？此操作不可撤销。`)) {
        fetch(`/api/files/delete?path=${encodeURIComponent(filePath)}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 返回上级目录
                window.location.href = `/?path=${encodeURIComponent('{{ parent_path }}')}`;
            } else {
                alert(data.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除时发生错误');
        });
    }
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // Escape 返回
    if (e.key === 'Escape') {
        window.history.back();
    }
    
    // F2 重命名
    if (e.key === 'F2') {
        e.preventDefault();
        renameFile();
    }
    
    // Ctrl+D 下载
    if (e.ctrlKey && e.key === 'd') {
        e.preventDefault();
        downloadFile();
    }
    
    // Delete 删除
    if (e.key === 'Delete') {
        e.preventDefault();
        deleteFile();
    }
});
</script>
{% endblock %}