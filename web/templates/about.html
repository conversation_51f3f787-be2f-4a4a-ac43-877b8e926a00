{% extends "base.html" %}

{% block title %}关于 - 文件管理器{% endblock %}

{% block extra_css %}
<style>
    .about-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .about-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .app-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
    }
    
    .version-badge {
        display: inline-block;
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }
    
    .feature-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .feature-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .feature-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 1rem;
        background: #f5f5f5;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: #666;
    }
    
    .info-section {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 500;
        color: #666;
    }
    
    .info-value {
        color: #333;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.9rem;
    }
    
    .shortcuts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }
    
    .shortcut-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 6px;
    }
    
    .shortcut-key {
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.8rem;
    }
    
    .contact-links {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .contact-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        text-decoration: none;
        color: #666;
        transition: all 0.2s;
    }
    
    .contact-link:hover {
        background: #f8f9fa;
        color: #333;
        text-decoration: none;
    }
    
    @media (max-width: 768px) {
        .about-container {
            padding: 1rem;
        }
        
        .contact-links {
            flex-direction: column;
            align-items: center;
        }
        
        .shortcuts-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="about-container">
    <!-- 应用头部 -->
    <div class="about-header">
        <div class="app-logo">
            <i class="fas fa-folder-open"></i>
        </div>
        <h1>文件管理器</h1>
        <p class="text-muted">现代化的Web文件管理解决方案</p>
        <span class="version-badge">版本 1.0.0</span>
    </div>
    
    <!-- 功能特性 -->
    <div class="feature-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-files-o"></i>
            </div>
            <h5>文件管理</h5>
            <p class="text-muted">浏览、上传、下载、重命名、删除文件和文件夹</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-eye"></i>
            </div>
            <h5>文件预览</h5>
            <p class="text-muted">支持图片、文本、代码等多种文件格式的在线预览</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-search"></i>
            </div>
            <h5>智能搜索</h5>
            <p class="text-muted">快速搜索文件名和内容，支持正则表达式</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-mobile"></i>
            </div>
            <h5>响应式设计</h5>
            <p class="text-muted">完美适配桌面、平板和手机等各种设备</p>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="info-section">
        <h4 class="mb-3">系统信息</h4>
        <div class="info-row">
            <span class="info-label">应用版本</span>
            <span class="info-value">1.0.0</span>
        </div>
        <div class="info-row">
            <span class="info-label">Python版本</span>
            <span class="info-value">{{ python_version }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">操作系统</span>
            <span class="info-value">{{ os_info }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">启动时间</span>
            <span class="info-value">{{ start_time }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">运行时长</span>
            <span class="info-value" id="uptime">{{ uptime }}</span>
        </div>
    </div>
    
    <!-- 快捷键 -->
    <div class="info-section">
        <h4 class="mb-3">快捷键</h4>
        <div class="shortcuts-grid">
            <div class="shortcut-item">
                <span>新建文件夹</span>
                <span class="shortcut-key">Ctrl + Shift + N</span>
            </div>
            <div class="shortcut-item">
                <span>上传文件</span>
                <span class="shortcut-key">Ctrl + U</span>
            </div>
            <div class="shortcut-item">
                <span>搜索</span>
                <span class="shortcut-key">Ctrl + F</span>
            </div>
            <div class="shortcut-item">
                <span>刷新</span>
                <span class="shortcut-key">F5</span>
            </div>
            <div class="shortcut-item">
                <span>返回上级</span>
                <span class="shortcut-key">Alt + ←</span>
            </div>
            <div class="shortcut-item">
                <span>选择全部</span>
                <span class="shortcut-key">Ctrl + A</span>
            </div>
        </div>
    </div>
    
    <!-- 联系方式 -->
    <div class="info-section">
        <h4 class="mb-3 text-center">联系我们</h4>
        <div class="contact-links">
            <a href="#" class="contact-link" title="GitHub仓库">
                <i class="fab fa-github"></i>
                <span>GitHub</span>
            </a>
            <a href="#" class="contact-link" title="问题反馈">
                <i class="fas fa-bug"></i>
                <span>反馈</span>
            </a>
            <a href="#" class="contact-link" title="使用文档">
                <i class="fas fa-book"></i>
                <span>文档</span>
            </a>
        </div>
    </div>
    
    <!-- 版权信息 -->
    <div class="text-center text-muted mt-4">
        <p>&copy; 2024 文件管理器. 保留所有权利.</p>
        <p>基于 Python Flask 构建</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 更新运行时长
function updateUptime() {
    const uptimeElement = document.getElementById('uptime');
    if (uptimeElement) {
        fetch('/api/system/info')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.uptime) {
                    uptimeElement.textContent = data.data.uptime;
                }
            })
            .catch(error => {
                console.error('获取运行时长失败:', error);
            });
    }
}

// 每分钟更新一次运行时长
setInterval(updateUptime, 60000);

// 快捷键处理
document.addEventListener('keydown', function(e) {
    // Ctrl + Shift + N - 新建文件夹
    if (e.ctrlKey && e.shiftKey && e.key === 'N') {
        e.preventDefault();
        window.location.href = '/?action=new_folder';
    }
    
    // Ctrl + U - 上传文件
    if (e.ctrlKey && e.key === 'u') {
        e.preventDefault();
        window.location.href = '/upload';
    }
    
    // Ctrl + F - 搜索
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        window.location.href = '/search';
    }
    
    // Alt + ← - 返回上级
    if (e.altKey && e.key === 'ArrowLeft') {
        e.preventDefault();
        window.history.back();
    }
});
</script>
{% endblock %}