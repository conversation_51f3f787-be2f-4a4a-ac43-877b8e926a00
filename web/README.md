# Producer Web 架构设计

## 当前问题分析

### 现有架构问题
1. **单一职责违反**: `UnifiedViewerHandler` 类承担了路由、控制器、视图渲染等多重职责
2. **代码耦合度高**: HTML模板硬编码在Python代码中，难以维护
3. **缺乏错误处理**: 没有统一的异常处理机制
4. **配置管理缺失**: 硬编码的配置信息散布在代码中
5. **测试困难**: 紧耦合的设计导致单元测试困难
6. **扩展性差**: 添加新功能需要修改核心类

## 新架构设计

### 设计原则
- **单一职责原则**: 每个类只负责一个功能
- **开闭原则**: 对扩展开放，对修改关闭
- **依赖倒置**: 依赖抽象而非具体实现
- **关注点分离**: 路由、业务逻辑、视图渲染分离

### 架构模式: MVC + 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                        Web Layer                           │
├─────────────────────────────────────────────────────────────┤
│  Router → Controller → Service → Repository → Model        │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构
```
web/                      # Web应用根目录（与producer同级）
├── __init__.py
├── app.py                 # 应用入口
├── config/
│   ├── __init__.py
│   ├── base.py           # 基础配置
│   ├── development.py    # 开发环境配置
│   └── production.py     # 生产环境配置
├── core/
│   ├── __init__.py
│   ├── server.py         # HTTP服务器
│   ├── router.py         # 路由系统
│   ├── middleware.py     # 中间件
│   └── exceptions.py     # 异常处理
├── controllers/
│   ├── __init__.py
│   ├── base.py          # 基础控制器
│   ├── home.py          # 首页控制器
│   ├── results.py       # 测试结果控制器
│   └── scripts.py       # 剧本控制器
├── services/
│   ├── __init__.py
│   ├── results.py       # 测试结果服务
│   └── scripts.py       # 剧本服务
├── templates/
│   ├── base.html        # 基础模板
│   ├── components/      # 可复用组件
│   ├── pages/          # 页面模板
│   └── layouts/        # 布局模板
├── static/
│   ├── css/
│   ├── js/
│   └── images/
└── utils/
    ├── __init__.py
    ├── helpers.py       # 工具函数
    └── validators.py    # 验证器
```

### 核心组件设计

#### 1. 路由系统 (Router)
- 支持RESTful路由
- 路由参数解析
- 中间件支持
- 路由组织和命名空间

#### 2. 控制器 (Controller)
- 处理HTTP请求
- 参数验证
- 调用服务层
- 返回响应

#### 3. 服务层 (Service)
- 业务逻辑处理
- 数据聚合
- 缓存管理
- 外部API调用

#### 4. 模板系统 (Template)
- Jinja2模板引擎
- 组件化设计
- 模板继承
- 自定义过滤器

#### 5. 配置系统 (Config)
- 环境变量支持
- 多环境配置
- 配置验证
- 热重载支持

#### 6. 中间件系统 (Middleware)
- 请求/响应处理
- 认证授权
- 日志记录
- 错误处理

### 技术选型
- **Web框架**: 自定义轻量级框架（基于http.server）
- **模板引擎**: Jinja2
- **配置管理**: Pydantic Settings
- **日志系统**: Python logging
- **静态资源**: 内置静态文件服务
- **前端**: 原生JavaScript + CSS3

### 实现计划
1. 创建核心框架组件
2. 实现MVC模式
3. 创建模板系统
4. 实现API层
5. 添加错误处理
6. 创建配置系统
7. 测试和优化

### 与Producer的集成
- Web应用通过导入producer模块来访问核心功能
- 保持松耦合，Web层只依赖producer的公共接口
- 支持独立部署和测试