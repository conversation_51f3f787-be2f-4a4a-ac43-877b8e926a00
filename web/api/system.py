"""系统信息API控制器

提供系统状态、信息查询和配置管理的API接口。
"""

import os
import sys
import platform
import psutil
import time
from typing import Dict, Any, List
from datetime import datetime
from .base import APIController, APIResponse
from ..core.exceptions import ForbiddenError


class SystemAPIController(APIController):
    """系统信息API控制器"""
    
    def __init__(self):
        super().__init__()
        self.start_time = time.time()
    
    def get_system_info(self, request) -> APIResponse:
        """获取系统基本信息"""
        try:
            system_info = {
                'platform': {
                    'system': platform.system(),
                    'release': platform.release(),
                    'version': platform.version(),
                    'machine': platform.machine(),
                    'processor': platform.processor(),
                    'architecture': platform.architecture(),
                    'node': platform.node()
                },
                'python': {
                    'version': sys.version,
                    'version_info': {
                        'major': sys.version_info.major,
                        'minor': sys.version_info.minor,
                        'micro': sys.version_info.micro
                    },
                    'executable': sys.executable,
                    'path': sys.path[:5]  # 只显示前5个路径
                },
                'process': {
                    'pid': os.getpid(),
                    'cwd': os.getcwd(),
                    'user': os.getenv('USER', 'unknown'),
                    'uptime': time.time() - self.start_time
                },
                'timestamp': datetime.now().isoformat()
            }
            
            return self.success(system_info, '成功获取系统信息')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_system_status(self, request) -> APIResponse:
        """获取系统状态"""
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 内存信息
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # 磁盘信息
            disk_usage = psutil.disk_usage('/')
            
            # 网络信息
            network_io = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            status_info = {
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': cpu_count,
                    'frequency': {
                        'current': cpu_freq.current if cpu_freq else None,
                        'min': cpu_freq.min if cpu_freq else None,
                        'max': cpu_freq.max if cpu_freq else None
                    } if cpu_freq else None
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'used': memory.used,
                    'free': memory.free,
                    'percent': memory.percent,
                    'swap': {
                        'total': swap.total,
                        'used': swap.used,
                        'free': swap.free,
                        'percent': swap.percent
                    }
                },
                'disk': {
                    'total': disk_usage.total,
                    'used': disk_usage.used,
                    'free': disk_usage.free,
                    'percent': (disk_usage.used / disk_usage.total) * 100
                },
                'network': {
                    'bytes_sent': network_io.bytes_sent,
                    'bytes_recv': network_io.bytes_recv,
                    'packets_sent': network_io.packets_sent,
                    'packets_recv': network_io.packets_recv
                },
                'processes': {
                    'count': process_count
                },
                'uptime': time.time() - self.start_time,
                'timestamp': datetime.now().isoformat()
            }
            
            return self.success(status_info, '成功获取系统状态')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_disk_usage(self, request) -> APIResponse:
        """获取磁盘使用情况"""
        try:
            path = self.get_query_param(request, 'path', '/')
            
            if not os.path.exists(path):
                return self.not_found('路径不存在')
            
            # 获取磁盘分区信息
            partitions = psutil.disk_partitions()
            disk_info = []
            
            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100 if usage.total > 0 else 0
                    })
                except PermissionError:
                    continue
            
            # 特定路径的使用情况
            if path != '/':
                try:
                    path_usage = psutil.disk_usage(path)
                    path_info = {
                        'path': path,
                        'total': path_usage.total,
                        'used': path_usage.used,
                        'free': path_usage.free,
                        'percent': (path_usage.used / path_usage.total) * 100
                    }
                except Exception:
                    path_info = None
            else:
                path_info = None
            
            return self.success({
                'partitions': disk_info,
                'path_usage': path_info,
                'timestamp': datetime.now().isoformat()
            }, '成功获取磁盘使用情况')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_process_list(self, request) -> APIResponse:
        """获取进程列表"""
        try:
            params = self.validate_query_params(request, {
                'page': {'type': int, 'default': 1, 'min': 1},
                'per_page': {'type': int, 'default': 20, 'min': 1, 'max': 100},
                'sort': {'type': str, 'default': 'cpu', 'choices': ['pid', 'name', 'cpu', 'memory']},
                'order': {'type': str, 'default': 'desc', 'choices': ['asc', 'desc']}
            })
            
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status', 'create_time']):
                try:
                    proc_info = proc.info
                    proc_info['create_time'] = datetime.fromtimestamp(proc_info['create_time']).isoformat()
                    processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 排序
            sort_key = params['sort']
            reverse = params['order'] == 'desc'
            
            if sort_key == 'pid':
                processes.sort(key=lambda x: x['pid'], reverse=reverse)
            elif sort_key == 'name':
                processes.sort(key=lambda x: x['name'] or '', reverse=reverse)
            elif sort_key == 'cpu':
                processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=reverse)
            elif sort_key == 'memory':
                processes.sort(key=lambda x: x['memory_percent'] or 0, reverse=reverse)
            
            # 分页
            page = params['page']
            per_page = params['per_page']
            total = len(processes)
            start = (page - 1) * per_page
            end = start + per_page
            paginated_processes = processes[start:end]
            
            return self.paginated_response(
                items=paginated_processes,
                total=total,
                page=page,
                per_page=per_page,
                message=f'成功获取进程列表，共 {total} 个进程'
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_environment_variables(self, request) -> APIResponse:
        """获取环境变量"""
        try:
            # 过滤敏感信息
            sensitive_keys = ['password', 'secret', 'key', 'token', 'auth']
            
            env_vars = {}
            for key, value in os.environ.items():
                # 检查是否为敏感信息
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    env_vars[key] = '***HIDDEN***'
                else:
                    env_vars[key] = value
            
            return self.success({
                'variables': env_vars,
                'count': len(env_vars),
                'timestamp': datetime.now().isoformat()
            }, '成功获取环境变量')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_network_info(self, request) -> APIResponse:
        """获取网络信息"""
        try:
            # 网络接口信息
            interfaces = psutil.net_if_addrs()
            interface_stats = psutil.net_if_stats()
            
            network_info = []
            for interface_name, addresses in interfaces.items():
                interface_data = {
                    'name': interface_name,
                    'addresses': [],
                    'stats': None
                }
                
                # 地址信息
                for addr in addresses:
                    interface_data['addresses'].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })
                
                # 统计信息
                if interface_name in interface_stats:
                    stats = interface_stats[interface_name]
                    interface_data['stats'] = {
                        'is_up': stats.isup,
                        'duplex': str(stats.duplex),
                        'speed': stats.speed,
                        'mtu': stats.mtu
                    }
                
                network_info.append(interface_data)
            
            # 网络连接信息
            connections = []
            try:
                for conn in psutil.net_connections()[:20]:  # 限制数量
                    connections.append({
                        'fd': conn.fd,
                        'family': str(conn.family),
                        'type': str(conn.type),
                        'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                        'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                        'status': conn.status,
                        'pid': conn.pid
                    })
            except psutil.AccessDenied:
                connections = []  # 权限不足时返回空列表
            
            return self.success({
                'interfaces': network_info,
                'connections': connections,
                'io_counters': {
                    'bytes_sent': psutil.net_io_counters().bytes_sent,
                    'bytes_recv': psutil.net_io_counters().bytes_recv,
                    'packets_sent': psutil.net_io_counters().packets_sent,
                    'packets_recv': psutil.net_io_counters().packets_recv
                },
                'timestamp': datetime.now().isoformat()
            }, '成功获取网络信息')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_application_config(self, request) -> APIResponse:
        """获取应用配置信息"""
        try:
            config_info = {
                'application': {
                    'name': 'Producer Web Server',
                    'version': '1.0.0',
                    'debug': os.getenv('DEBUG', 'False').lower() == 'true',
                    'environment': os.getenv('ENVIRONMENT', 'development')
                },
                'server': {
                    'host': os.getenv('HOST', '0.0.0.0'),
                    'port': int(os.getenv('PORT', 8000)),
                    'workers': int(os.getenv('WORKERS', 1))
                },
                'paths': {
                    'base_path': os.getcwd(),
                    'config_path': os.getenv('CONFIG_PATH', './config'),
                    'log_path': os.getenv('LOG_PATH', './logs'),
                    'upload_path': os.getenv('UPLOAD_PATH', './uploads')
                },
                'features': {
                    'file_management': True,
                    'system_monitoring': True,
                    'api_access': True,
                    'web_interface': True
                },
                'limits': {
                    'max_file_size': int(os.getenv('MAX_FILE_SIZE', 100 * 1024 * 1024)),  # 100MB
                    'max_upload_files': int(os.getenv('MAX_UPLOAD_FILES', 10)),
                    'session_timeout': int(os.getenv('SESSION_TIMEOUT', 3600))  # 1小时
                },
                'timestamp': datetime.now().isoformat()
            }
            
            return self.success(config_info, '成功获取应用配置')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def health_check(self, request) -> APIResponse:
        """健康检查"""
        try:
            # 基本健康检查
            health_status = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'uptime': time.time() - self.start_time,
                'checks': {
                    'disk_space': self._check_disk_space(),
                    'memory_usage': self._check_memory_usage(),
                    'cpu_usage': self._check_cpu_usage(),
                    'process_status': self._check_process_status()
                }
            }
            
            # 检查是否有任何检查失败
            failed_checks = [name for name, check in health_status['checks'].items() if not check['healthy']]
            
            if failed_checks:
                health_status['status'] = 'unhealthy'
                health_status['failed_checks'] = failed_checks
            
            return self.success(health_status, '健康检查完成')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            disk_usage = psutil.disk_usage('/')
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            return {
                'healthy': usage_percent < 90,  # 磁盘使用率小于90%
                'usage_percent': usage_percent,
                'free_space': disk_usage.free,
                'message': 'OK' if usage_percent < 90 else 'Disk space low'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Failed to check disk space'
            }
    
    def _check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用"""
        try:
            memory = psutil.virtual_memory()
            
            return {
                'healthy': memory.percent < 90,  # 内存使用率小于90%
                'usage_percent': memory.percent,
                'available': memory.available,
                'message': 'OK' if memory.percent < 90 else 'Memory usage high'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Failed to check memory usage'
            }
    
    def _check_cpu_usage(self) -> Dict[str, Any]:
        """检查CPU使用率"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            
            return {
                'healthy': cpu_percent < 90,  # CPU使用率小于90%
                'usage_percent': cpu_percent,
                'message': 'OK' if cpu_percent < 90 else 'CPU usage high'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Failed to check CPU usage'
            }
    
    def _check_process_status(self) -> Dict[str, Any]:
        """检查进程状态"""
        try:
            current_process = psutil.Process()
            
            return {
                'healthy': current_process.is_running(),
                'pid': current_process.pid,
                'status': current_process.status(),
                'memory_percent': current_process.memory_percent(),
                'message': 'OK' if current_process.is_running() else 'Process not running'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Failed to check process status'
            }