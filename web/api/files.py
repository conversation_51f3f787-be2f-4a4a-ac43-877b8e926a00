"""文件管理API控制器

提供文件和目录操作的RESTful API接口。
"""

import os
import shutil
import mimetypes
from typing import List, Dict, Any, Optional
from datetime import datetime
from .base import APIController, APIResponse
from ..core.exceptions import NotFoundError, BadRequestError, ForbiddenError


class FilesAPIController(APIController):
    """文件管理API控制器"""
    
    def __init__(self, base_path: str = None):
        super().__init__()
        self.base_path = base_path or os.getcwd()
        self.allowed_extensions = {
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
            'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'],
            'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
            'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.md'],
            'code': ['.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.php', '.rb', '.go'],
            'archive': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
        }
    
    def list_files(self, request) -> APIResponse:
        """列出目录文件"""
        try:
            # 验证查询参数
            params = self.validate_query_params(request, {
                'path': {'type': str, 'default': ''},
                'page': {'type': int, 'default': 1, 'min': 1},
                'per_page': {'type': int, 'default': 50, 'min': 1, 'max': 200},
                'sort': {'type': str, 'default': 'name', 'choices': ['name', 'size', 'modified', 'type']},
                'order': {'type': str, 'default': 'asc', 'choices': ['asc', 'desc']},
                'filter': {'type': str, 'default': ''},
                'type': {'type': str, 'default': '', 'choices': ['', 'file', 'directory']}
            })
            
            # 构建完整路径
            target_path = self._get_safe_path(params['path'])
            
            if not os.path.exists(target_path):
                return self.not_found('目录不存在')
            
            if not os.path.isdir(target_path):
                return self.bad_request('路径不是目录')
            
            # 获取文件列表
            files = self._get_directory_contents(target_path, params)
            
            # 分页
            page = params['page']
            per_page = params['per_page']
            total = len(files)
            start = (page - 1) * per_page
            end = start + per_page
            paginated_files = files[start:end]
            
            return self.paginated_response(
                items=paginated_files,
                total=total,
                page=page,
                per_page=per_page,
                message=f'成功获取目录内容，共 {total} 项'
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_file_info(self, request) -> APIResponse:
        """获取文件信息"""
        try:
            file_path = self.get_query_param(request, 'path')
            if not file_path:
                return self.bad_request('缺少path参数')
            
            target_path = self._get_safe_path(file_path)
            
            if not os.path.exists(target_path):
                return self.not_found('文件不存在')
            
            file_info = self.format_file_info(target_path)
            
            # 添加额外信息
            if os.path.isfile(target_path):
                file_info['content_preview'] = self._get_content_preview(target_path)
                file_info['can_preview'] = self._can_preview_file(target_path)
            
            return self.success(file_info, '成功获取文件信息')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def create_directory(self, request) -> APIResponse:
        """创建目录"""
        try:
            data = self.validate_json(request, ['path'])
            
            target_path = self._get_safe_path(data['path'])
            
            if os.path.exists(target_path):
                return self.bad_request('目录已存在')
            
            os.makedirs(target_path, exist_ok=True)
            
            return self.success(
                self.format_file_info(target_path),
                '目录创建成功'
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def delete_file(self, request) -> APIResponse:
        """删除文件或目录"""
        try:
            file_path = self.get_query_param(request, 'path')
            if not file_path:
                return self.bad_request('缺少path参数')
            
            target_path = self._get_safe_path(file_path)
            
            if not os.path.exists(target_path):
                return self.not_found('文件不存在')
            
            # 检查是否为系统重要文件
            if self._is_system_file(target_path):
                return self.forbidden('不能删除系统文件')
            
            if os.path.isdir(target_path):
                shutil.rmtree(target_path)
                message = '目录删除成功'
            else:
                os.remove(target_path)
                message = '文件删除成功'
            
            return self.success(message=message)
            
        except Exception as e:
            return self.handle_exception(e)
    
    def rename_file(self, request) -> APIResponse:
        """重命名文件或目录"""
        try:
            data = self.validate_json(request, ['old_path', 'new_name'])
            
            old_path = self._get_safe_path(data['old_path'])
            new_name = data['new_name']
            
            if not os.path.exists(old_path):
                return self.not_found('文件不存在')
            
            # 验证新名称
            if not self._is_valid_filename(new_name):
                return self.bad_request('文件名包含非法字符')
            
            # 构建新路径
            parent_dir = os.path.dirname(old_path)
            new_path = os.path.join(parent_dir, new_name)
            
            if os.path.exists(new_path):
                return self.bad_request('目标文件已存在')
            
            os.rename(old_path, new_path)
            
            return self.success(
                self.format_file_info(new_path),
                '重命名成功'
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def copy_file(self, request) -> APIResponse:
        """复制文件或目录"""
        try:
            data = self.validate_json(request, ['source_path', 'target_path'])
            
            source_path = self._get_safe_path(data['source_path'])
            target_path = self._get_safe_path(data['target_path'])
            
            if not os.path.exists(source_path):
                return self.not_found('源文件不存在')
            
            if os.path.exists(target_path):
                return self.bad_request('目标文件已存在')
            
            if os.path.isdir(source_path):
                shutil.copytree(source_path, target_path)
                message = '目录复制成功'
            else:
                shutil.copy2(source_path, target_path)
                message = '文件复制成功'
            
            return self.success(
                self.format_file_info(target_path),
                message
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def move_file(self, request) -> APIResponse:
        """移动文件或目录"""
        try:
            data = self.validate_json(request, ['source_path', 'target_path'])
            
            source_path = self._get_safe_path(data['source_path'])
            target_path = self._get_safe_path(data['target_path'])
            
            if not os.path.exists(source_path):
                return self.not_found('源文件不存在')
            
            if os.path.exists(target_path):
                return self.bad_request('目标文件已存在')
            
            shutil.move(source_path, target_path)
            
            return self.success(
                self.format_file_info(target_path),
                '移动成功'
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def get_file_content(self, request) -> APIResponse:
        """获取文件内容（文本文件）"""
        try:
            file_path = self.get_query_param(request, 'path')
            if not file_path:
                return self.bad_request('缺少path参数')
            
            target_path = self._get_safe_path(file_path)
            
            if not os.path.exists(target_path):
                return self.not_found('文件不存在')
            
            if not os.path.isfile(target_path):
                return self.bad_request('路径不是文件')
            
            if not self._is_text_file(target_path):
                return self.bad_request('文件不是文本文件')
            
            # 读取文件内容
            try:
                with open(target_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 尝试其他编码
                with open(target_path, 'r', encoding='gbk') as f:
                    content = f.read()
            
            return self.success({
                'content': content,
                'encoding': 'utf-8',
                'lines': len(content.splitlines()),
                'size': len(content.encode('utf-8'))
            }, '成功获取文件内容')
            
        except Exception as e:
            return self.handle_exception(e)
    
    def search_files(self, request) -> APIResponse:
        """搜索文件"""
        try:
            params = self.validate_query_params(request, {
                'q': {'type': str, 'required': True},
                'path': {'type': str, 'default': ''},
                'type': {'type': str, 'default': '', 'choices': ['', 'file', 'directory']},
                'extension': {'type': str, 'default': ''},
                'size_min': {'type': int, 'default': 0, 'min': 0},
                'size_max': {'type': int, 'default': 0, 'min': 0},
                'modified_after': {'type': str, 'default': ''},
                'modified_before': {'type': str, 'default': ''},
                'page': {'type': int, 'default': 1, 'min': 1},
                'per_page': {'type': int, 'default': 50, 'min': 1, 'max': 200}
            })
            
            search_path = self._get_safe_path(params['path'])
            
            if not os.path.exists(search_path):
                return self.not_found('搜索路径不存在')
            
            # 执行搜索
            results = self._search_files(search_path, params)
            
            # 分页
            page = params['page']
            per_page = params['per_page']
            total = len(results)
            start = (page - 1) * per_page
            end = start + per_page
            paginated_results = results[start:end]
            
            return self.paginated_response(
                items=paginated_results,
                total=total,
                page=page,
                per_page=per_page,
                message=f'搜索完成，找到 {total} 个结果'
            )
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _get_safe_path(self, path: str) -> str:
        """获取安全路径，防止路径遍历攻击"""
        if not path:
            return self.base_path
        
        # 移除危险字符
        path = path.replace('..', '').replace('//', '/')
        
        # 构建绝对路径
        if os.path.isabs(path):
            full_path = path
        else:
            full_path = os.path.join(self.base_path, path.lstrip('/'))
        
        # 规范化路径
        full_path = os.path.normpath(full_path)
        
        # 确保路径在基础目录内
        if not full_path.startswith(self.base_path):
            raise ForbiddenError('访问被拒绝')
        
        return full_path
    
    def _get_directory_contents(self, path: str, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取目录内容"""
        files = []
        
        try:
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                
                # 跳过隐藏文件（可选）
                if item.startswith('.') and not params.get('show_hidden', False):
                    continue
                
                file_info = self.format_file_info(item_path)
                if file_info:
                    # 应用过滤器
                    if self._should_include_file(file_info, params):
                        files.append(file_info)
        
        except PermissionError:
            pass  # 忽略权限错误
        
        # 排序
        sort_key = params['sort']
        reverse = params['order'] == 'desc'
        
        if sort_key == 'name':
            files.sort(key=lambda x: x['name'].lower(), reverse=reverse)
        elif sort_key == 'size':
            files.sort(key=lambda x: x['size'], reverse=reverse)
        elif sort_key == 'modified':
            files.sort(key=lambda x: x['modified'], reverse=reverse)
        elif sort_key == 'type':
            files.sort(key=lambda x: (x['type'], x['name'].lower()), reverse=reverse)
        
        return files
    
    def _should_include_file(self, file_info: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """判断是否应该包含文件"""
        # 类型过滤
        if params['type'] and file_info['type'] != params['type']:
            return False
        
        # 名称过滤
        if params['filter']:
            filter_text = params['filter'].lower()
            if filter_text not in file_info['name'].lower():
                return False
        
        return True
    
    def _search_files(self, search_path: str, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索文件"""
        results = []
        query = params['q'].lower()
        
        for root, dirs, files in os.walk(search_path):
            # 搜索目录
            if not params['type'] or params['type'] == 'directory':
                for dir_name in dirs:
                    if query in dir_name.lower():
                        dir_path = os.path.join(root, dir_name)
                        file_info = self.format_file_info(dir_path)
                        if file_info and self._matches_search_criteria(file_info, params):
                            results.append(file_info)
            
            # 搜索文件
            if not params['type'] or params['type'] == 'file':
                for file_name in files:
                    if query in file_name.lower():
                        file_path = os.path.join(root, file_name)
                        file_info = self.format_file_info(file_path)
                        if file_info and self._matches_search_criteria(file_info, params):
                            results.append(file_info)
        
        return results
    
    def _matches_search_criteria(self, file_info: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """检查文件是否匹配搜索条件"""
        # 扩展名过滤
        if params['extension']:
            if file_info.get('extension', '').lower() != params['extension'].lower():
                return False
        
        # 大小过滤
        if params['size_min'] > 0 and file_info['size'] < params['size_min']:
            return False
        
        if params['size_max'] > 0 and file_info['size'] > params['size_max']:
            return False
        
        # 时间过滤（简化实现）
        # 这里可以添加更复杂的时间过滤逻辑
        
        return True
    
    def _get_content_preview(self, file_path: str, max_length: int = 500) -> str:
        """获取文件内容预览"""
        if not self._is_text_file(file_path):
            return ''
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(max_length)
                if len(content) == max_length:
                    content += '...'
                return content
        except (UnicodeDecodeError, PermissionError):
            return ''
    
    def _can_preview_file(self, file_path: str) -> bool:
        """判断文件是否可以预览"""
        if not os.path.isfile(file_path):
            return False
        
        # 检查文件大小（避免预览过大文件）
        if os.path.getsize(file_path) > 10 * 1024 * 1024:  # 10MB
            return False
        
        # 检查文件类型
        return self._is_text_file(file_path) or self._is_image_file(file_path)
    
    def _is_text_file(self, file_path: str) -> bool:
        """判断是否为文本文件"""
        text_extensions = ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.yml', '.yaml', '.ini', '.cfg']
        _, ext = os.path.splitext(file_path)
        return ext.lower() in text_extensions
    
    def _is_image_file(self, file_path: str) -> bool:
        """判断是否为图片文件"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        _, ext = os.path.splitext(file_path)
        return ext.lower() in image_extensions
    
    def _is_system_file(self, file_path: str) -> bool:
        """判断是否为系统重要文件"""
        system_files = ['/', '/bin', '/etc', '/usr', '/var', '/sys', '/proc']
        return any(file_path.startswith(sys_path) for sys_path in system_files)
    
    def _is_valid_filename(self, filename: str) -> bool:
        """验证文件名是否合法"""
        if not filename or filename in ['.', '..']:
            return False
        
        # 检查非法字符
        illegal_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        return not any(char in filename for char in illegal_chars)