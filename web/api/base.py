"""基础API控制器

提供RESTful API的基础功能和通用响应格式。
"""

import json
import traceback
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from ..controllers.base import BaseController
from ..core.exceptions import WebException, BadRequestError, NotFoundError, UnauthorizedError


class APIResponse:
    """API响应格式"""
    
    def __init__(self, success: bool = True, data: Any = None, message: str = '', 
                 error_code: str = None, status_code: int = 200):
        self.success = success
        self.data = data
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            'success': self.success,
            'timestamp': self.timestamp
        }
        
        if self.success:
            result['data'] = self.data
            if self.message:
                result['message'] = self.message
        else:
            result['error'] = {
                'message': self.message,
                'code': self.error_code
            }
        
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, default=str)


class APIController(BaseController):
    """基础API控制器"""
    
    def __init__(self):
        super().__init__()
        self.content_type = 'application/json'
    
    def success(self, data: Any = None, message: str = '', status_code: int = 200) -> APIResponse:
        """成功响应"""
        return APIResponse(
            success=True,
            data=data,
            message=message,
            status_code=status_code
        )
    
    def error(self, message: str, error_code: str = None, status_code: int = 400, data: Any = None) -> APIResponse:
        """错误响应"""
        return APIResponse(
            success=False,
            data=data,
            message=message,
            error_code=error_code,
            status_code=status_code
        )
    
    def not_found(self, message: str = '资源不存在') -> APIResponse:
        """404响应"""
        return self.error(message, 'NOT_FOUND', 404)
    
    def bad_request(self, message: str = '请求参数错误') -> APIResponse:
        """400响应"""
        return self.error(message, 'BAD_REQUEST', 400)
    
    def unauthorized(self, message: str = '未授权访问') -> APIResponse:
        """401响应"""
        return self.error(message, 'UNAUTHORIZED', 401)
    
    def forbidden(self, message: str = '禁止访问') -> APIResponse:
        """403响应"""
        return self.error(message, 'FORBIDDEN', 403)
    
    def internal_error(self, message: str = '服务器内部错误') -> APIResponse:
        """500响应"""
        return self.error(message, 'INTERNAL_ERROR', 500)
    
    def paginated_response(self, items: List[Any], total: int, page: int, per_page: int, 
                          message: str = '') -> APIResponse:
        """分页响应"""
        total_pages = (total + per_page - 1) // per_page
        
        data = {
            'items': items,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        }
        
        return self.success(data, message)
    
    def validate_json(self, request, required_fields: List[str] = None) -> Dict[str, Any]:
        """验证JSON请求体"""
        try:
            if hasattr(request, 'get_json'):
                data = request.get_json()
            else:
                # 简化版本的JSON解析
                content_type = getattr(request, 'content_type', '')
                if 'application/json' not in content_type:
                    raise BadRequestError('Content-Type必须是application/json')
                
                body = getattr(request, 'body', b'')
                if isinstance(body, str):
                    body = body.encode('utf-8')
                
                data = json.loads(body.decode('utf-8'))
            
            if data is None:
                raise BadRequestError('请求体不能为空')
            
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    raise BadRequestError(f'缺少必需字段: {", ".join(missing_fields)}')
            
            return data
            
        except json.JSONDecodeError:
            raise BadRequestError('无效的JSON格式')
        except Exception as e:
            raise BadRequestError(f'解析请求失败: {str(e)}')
    
    def validate_query_params(self, request, schema: Dict[str, Dict]) -> Dict[str, Any]:
        """验证查询参数"""
        params = {}
        
        for param_name, param_config in schema.items():
            param_type = param_config.get('type', str)
            required = param_config.get('required', False)
            default = param_config.get('default')
            choices = param_config.get('choices')
            min_value = param_config.get('min')
            max_value = param_config.get('max')
            
            # 获取参数值
            value = self.get_query_param(request, param_name)
            
            if value is None:
                if required:
                    raise BadRequestError(f'缺少必需参数: {param_name}')
                params[param_name] = default
                continue
            
            # 类型转换
            try:
                if param_type == int:
                    value = int(value)
                elif param_type == float:
                    value = float(value)
                elif param_type == bool:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                # str类型不需要转换
            except (ValueError, TypeError):
                raise BadRequestError(f'参数 {param_name} 类型错误，期望 {param_type.__name__}')
            
            # 验证选项
            if choices and value not in choices:
                raise BadRequestError(f'参数 {param_name} 值无效，可选值: {", ".join(map(str, choices))}')
            
            # 验证范围
            if min_value is not None and value < min_value:
                raise BadRequestError(f'参数 {param_name} 不能小于 {min_value}')
            
            if max_value is not None and value > max_value:
                raise BadRequestError(f'参数 {param_name} 不能大于 {max_value}')
            
            params[param_name] = value
        
        return params
    
    def handle_exception(self, e: Exception) -> APIResponse:
        """处理异常"""
        if isinstance(e, WebException):
            return self.error(
                message=str(e),
                error_code=e.__class__.__name__,
                status_code=e.status_code
            )
        else:
            # 记录详细错误信息
            error_details = {
                'type': e.__class__.__name__,
                'message': str(e),
                'traceback': traceback.format_exc()
            }
            
            # 在开发环境下返回详细错误信息
            if getattr(self.config, 'DEBUG', False):
                return self.error(
                    message=f'{e.__class__.__name__}: {str(e)}',
                    error_code='INTERNAL_ERROR',
                    status_code=500,
                    data=error_details
                )
            else:
                return self.internal_error()
    
    def get_client_ip(self, request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = getattr(request, 'headers', {}).get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = getattr(request, 'headers', {}).get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # 返回远程地址
        return getattr(request, 'remote_addr', '127.0.0.1')
    
    def get_user_agent(self, request) -> str:
        """获取用户代理"""
        return getattr(request, 'headers', {}).get('User-Agent', '')
    
    def cors_headers(self) -> Dict[str, str]:
        """CORS头部"""
        return {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
            'Access-Control-Max-Age': '86400'
        }
    
    def options(self, request) -> APIResponse:
        """处理OPTIONS请求（CORS预检）"""
        response = self.success()
        return response
    
    def format_file_info(self, file_path: str, file_stat: Any = None) -> Dict[str, Any]:
        """格式化文件信息"""
        import os
        from datetime import datetime
        
        if not os.path.exists(file_path):
            return None
        
        stat = file_stat or os.stat(file_path)
        is_dir = os.path.isdir(file_path)
        
        info = {
            'name': os.path.basename(file_path),
            'path': file_path,
            'type': 'directory' if is_dir else 'file',
            'size': 0 if is_dir else stat.st_size,
            'size_formatted': self.format_filesize(0 if is_dir else stat.st_size),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'modified_formatted': self.format_datetime(datetime.fromtimestamp(stat.st_mtime)),
            'permissions': oct(stat.st_mode)[-3:],
            'is_readable': os.access(file_path, os.R_OK),
            'is_writable': os.access(file_path, os.W_OK),
            'is_executable': os.access(file_path, os.X_OK)
        }
        
        if not is_dir:
            # 添加文件扩展名
            name = info['name']
            if '.' in name:
                info['extension'] = name.rsplit('.', 1)[-1].lower()
            else:
                info['extension'] = ''
            
            # 添加MIME类型
            info['mime_type'] = self.get_mime_type(file_path)
        
        return info
    
    def get_mime_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        import mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    def format_filesize(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return '0 B'
        
        size_names = ['B', 'KB', 'MB', 'GB', 'TB']
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def format_datetime(self, dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化日期时间"""
        return dt.strftime(format_str)