"""异常处理模块

定义自定义异常类和异常处理器
"""

import traceback
from typing import Dict, Any, Optional
from flask import jsonify, request
from werkzeug.exceptions import HTTPException


class WebAppException(Exception):
    """Web应用基础异常类"""
    
    def __init__(self, message: str, code: int = 500, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.code = code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error': True,
            'message': self.message,
            'code': self.code,
            'details': self.details
        }


class ValidationError(WebAppException):
    """验证错误异常"""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        details = {}
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)
        
        super().__init__(message, 400, details)


class FileOperationError(WebAppException):
    """文件操作错误异常"""
    
    def __init__(self, message: str, path: Optional[str] = None, operation: Optional[str] = None):
        details = {}
        if path:
            details['path'] = path
        if operation:
            details['operation'] = operation
        
        super().__init__(message, 500, details)


class SecurityError(WebAppException):
    """安全错误异常"""
    
    def __init__(self, message: str, threat_type: Optional[str] = None):
        details = {}
        if threat_type:
            details['threat_type'] = threat_type
        
        super().__init__(message, 403, details)


class AuthenticationError(WebAppException):
    """认证错误异常"""
    
    def __init__(self, message: str, auth_type: Optional[str] = None):
        details = {}
        if auth_type:
            details['auth_type'] = auth_type
        
        super().__init__(message, 401, details)


class ConfigurationError(WebAppException):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        details = {}
        if config_key:
            details['config_key'] = config_key
        
        super().__init__(message, 500, details)


class APIError(WebAppException):
    """API错误异常"""
    
    def __init__(self, message: str, code: int = 400, endpoint: Optional[str] = None):
        details = {}
        if endpoint:
            details['endpoint'] = endpoint
        
        super().__init__(message, code, details)


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, app=None, logger=None):
        self.app = app
        self.logger = logger
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用的异常处理"""
        self.app = app
        
        # 注册异常处理器
        app.errorhandler(WebAppException)(self.handle_webapp_exception)
        app.errorhandler(ValidationError)(self.handle_validation_error)
        app.errorhandler(FileOperationError)(self.handle_file_operation_error)
        app.errorhandler(SecurityError)(self.handle_security_error)
        app.errorhandler(APIError)(self.handle_api_error)
        app.errorhandler(HTTPException)(self.handle_http_exception)
        app.errorhandler(Exception)(self.handle_generic_exception)
    
    def handle_webapp_exception(self, e: WebAppException):
        """处理Web应用异常"""
        self._log_exception(e, 'WebApp Exception')
        
        if request.path.startswith('/api/'):
            return jsonify(e.to_dict()), e.code
        else:
            return self._render_error_page(e.message, e.code)
    
    def handle_validation_error(self, e: ValidationError):
        """处理验证错误"""
        self._log_exception(e, 'Validation Error')
        
        if request.path.startswith('/api/'):
            return jsonify(e.to_dict()), e.code
        else:
            return self._render_error_page(f"验证错误: {e.message}", e.code)
    
    def handle_file_operation_error(self, e: FileOperationError):
        """处理文件操作错误"""
        self._log_exception(e, 'File Operation Error')
        
        if request.path.startswith('/api/'):
            return jsonify(e.to_dict()), e.code
        else:
            return self._render_error_page(f"文件操作失败: {e.message}", e.code)
    
    def handle_security_error(self, e: SecurityError):
        """处理安全错误"""
        self._log_exception(e, 'Security Error', level='warning')
        
        if request.path.startswith('/api/'):
            return jsonify(e.to_dict()), e.code
        else:
            return self._render_error_page("访问被拒绝", e.code)
    
    def handle_api_error(self, e: APIError):
        """处理API错误"""
        self._log_exception(e, 'API Error')
        return jsonify(e.to_dict()), e.code
    
    def handle_http_exception(self, e: HTTPException):
        """处理HTTP异常"""
        self._log_exception(e, f'HTTP {e.code} Error')
        
        error_data = {
            'error': True,
            'message': e.description or 'HTTP错误',
            'code': e.code
        }
        
        if request.path.startswith('/api/'):
            return jsonify(error_data), e.code
        else:
            return self._render_error_page(error_data['message'], e.code)
    
    def handle_generic_exception(self, e: Exception):
        """处理通用异常"""
        self._log_exception(e, 'Unhandled Exception', level='error')
        
        error_data = {
            'error': True,
            'message': '服务器内部错误',
            'code': 500
        }
        
        if request.path.startswith('/api/'):
            return jsonify(error_data), 500
        else:
            return self._render_error_page('服务器内部错误', 500)
    
    def _log_exception(self, e: Exception, error_type: str, level: str = 'error'):
        """记录异常日志"""
        if not self.logger:
            return
        
        # 收集请求信息
        request_info = {
            'method': request.method,
            'url': request.url,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'referrer': request.headers.get('Referer', '')
        }
        
        # 构建日志消息
        log_message = f"{error_type}: {str(e)}"
        
        # 记录异常详情
        exception_details = {
            'exception_type': type(e).__name__,
            'exception_message': str(e),
            'request_info': request_info,
            'traceback': traceback.format_exc()
        }
        
        # 根据级别记录日志
        if level == 'error':
            self.logger.error(log_message, extra={'details': exception_details})
        elif level == 'warning':
            self.logger.warning(log_message, extra={'details': exception_details})
        else:
            self.logger.info(log_message, extra={'details': exception_details})
    
    def _render_error_page(self, message: str, code: int):
        """渲染错误页面"""
        try:
            from flask import render_template
            return render_template('error.html', 
                                 error_message=message, 
                                 error_code=code), code
        except Exception:
            # 如果模板渲染失败，返回简单的HTML
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>错误 {code}</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                    .error-container {{ max-width: 500px; margin: 0 auto; }}
                    .error-code {{ font-size: 72px; color: #dc3545; margin-bottom: 20px; }}
                    .error-message {{ font-size: 18px; color: #666; margin-bottom: 30px; }}
                    .back-link {{ color: #007bff; text-decoration: none; }}
                    .back-link:hover {{ text-decoration: underline; }}
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-code">{code}</div>
                    <div class="error-message">{message}</div>
                    <a href="/" class="back-link">返回首页</a>
                </div>
            </body>
            </html>
            """
            return html, code


def handle_exception(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except WebAppException:
            # 重新抛出自定义异常，让异常处理器处理
            raise
        except Exception as e:
            # 将通用异常包装为WebAppException
            raise WebAppException(f"操作失败: {str(e)}")
    
    wrapper.__name__ = func.__name__
    wrapper.__doc__ = func.__doc__
    return wrapper


# 全局异常处理器实例
exception_handler = ExceptionHandler()