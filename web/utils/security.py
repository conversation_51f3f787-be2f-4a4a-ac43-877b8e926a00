"""安全工具模块

提供认证、授权和安全检查功能
"""

import os
import hashlib
import hmac
import secrets
import time
import jwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from functools import wraps
from urllib.parse import urlparse

from .exceptions import SecurityError, AuthenticationError
from .logger import get_logger

logger = get_logger(__name__)


class PasswordManager:
    """密码管理器"""
    
    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple:
        """哈希密码"""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # 使用PBKDF2进行密码哈希
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """验证密码"""
        try:
            computed_hash, _ = PasswordManager.hash_password(password, salt)
            return hmac.compare_digest(computed_hash, password_hash)
        except Exception:
            return False
    
    @staticmethod
    def generate_password(length: int = 12) -> str:
        """生成随机密码"""
        import string
        
        # 确保密码包含各种字符类型
        chars = string.ascii_letters + string.digits + '!@#$%^&*'
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 确保至少包含一个数字和一个特殊字符
        if not any(c.isdigit() for c in password):
            password = password[:-1] + secrets.choice(string.digits)
        
        if not any(c in '!@#$%^&*' for c in password):
            password = password[:-1] + secrets.choice('!@#$%^&*')
        
        return password
    
    @staticmethod
    def check_password_strength(password: str) -> Dict[str, Any]:
        """检查密码强度"""
        score = 0
        feedback = []
        
        # 长度检查
        if len(password) >= 8:
            score += 1
        else:
            feedback.append("密码长度至少8位")
        
        if len(password) >= 12:
            score += 1
        
        # 字符类型检查
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        
        if has_lower:
            score += 1
        else:
            feedback.append("需要包含小写字母")
        
        if has_upper:
            score += 1
        else:
            feedback.append("需要包含大写字母")
        
        if has_digit:
            score += 1
        else:
            feedback.append("需要包含数字")
        
        if has_special:
            score += 1
        else:
            feedback.append("需要包含特殊字符")
        
        # 常见密码检查
        common_passwords = [
            'password', '123456', 'password123', 'admin', 'qwerty',
            '12345678', '123456789', 'password1', 'abc123'
        ]
        
        if password.lower() in common_passwords:
            score = max(0, score - 2)
            feedback.append("不要使用常见密码")
        
        # 评级
        if score >= 6:
            strength = "强"
        elif score >= 4:
            strength = "中等"
        elif score >= 2:
            strength = "弱"
        else:
            strength = "很弱"
        
        return {
            'score': score,
            'strength': strength,
            'feedback': feedback
        }


class TokenManager:
    """令牌管理器"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def generate_token(self, payload: Dict[str, Any], 
                      expires_in: int = 3600) -> str:
        """生成JWT令牌"""
        try:
            # 添加过期时间
            payload['exp'] = datetime.utcnow() + timedelta(seconds=expires_in)
            payload['iat'] = datetime.utcnow()
            
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            return token
        except Exception as e:
            raise SecurityError(f"生成令牌失败: {str(e)}")
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("令牌已过期")
        except jwt.InvalidTokenError:
            raise AuthenticationError("无效的令牌")
        except Exception as e:
            raise SecurityError(f"验证令牌失败: {str(e)}")
    
    def generate_csrf_token(self) -> str:
        """生成CSRF令牌"""
        return secrets.token_urlsafe(32)
    
    def verify_csrf_token(self, token: str, session_token: str) -> bool:
        """验证CSRF令牌"""
        return hmac.compare_digest(token, session_token)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self.requests = {}  # {client_id: [(timestamp, count), ...]}
    
    def is_allowed(self, client_id: str, max_requests: int = 100, 
                  window_seconds: int = 3600) -> bool:
        """检查是否允许请求"""
        now = time.time()
        window_start = now - window_seconds
        
        # 清理过期记录
        if client_id in self.requests:
            self.requests[client_id] = [
                (timestamp, count) for timestamp, count in self.requests[client_id]
                if timestamp > window_start
            ]
        else:
            self.requests[client_id] = []
        
        # 计算当前窗口内的请求数
        total_requests = sum(count for _, count in self.requests[client_id])
        
        if total_requests >= max_requests:
            return False
        
        # 记录新请求
        self.requests[client_id].append((now, 1))
        return True
    
    def get_remaining_requests(self, client_id: str, max_requests: int = 100,
                             window_seconds: int = 3600) -> int:
        """获取剩余请求数"""
        now = time.time()
        window_start = now - window_seconds
        
        if client_id not in self.requests:
            return max_requests
        
        # 计算当前窗口内的请求数
        current_requests = sum(
            count for timestamp, count in self.requests[client_id]
            if timestamp > window_start
        )
        
        return max(0, max_requests - current_requests)


class SecurityValidator:
    """安全验证器"""
    
    @staticmethod
    def validate_url(url: str, allowed_schemes: List[str] = None) -> bool:
        """验证URL安全性"""
        if allowed_schemes is None:
            allowed_schemes = ['http', 'https']
        
        try:
            parsed = urlparse(url)
            
            # 检查协议
            if parsed.scheme not in allowed_schemes:
                return False
            
            # 检查主机名
            if not parsed.netloc:
                return False
            
            # 检查是否为本地地址（可选）
            hostname = parsed.hostname
            if hostname:
                # 阻止访问本地地址
                local_addresses = [
                    'localhost', '127.0.0.1', '0.0.0.0',
                    '::1', '::ffff:127.0.0.1'
                ]
                if hostname.lower() in local_addresses:
                    return False
                
                # 检查私有IP范围
                import ipaddress
                try:
                    ip = ipaddress.ip_address(hostname)
                    if ip.is_private or ip.is_loopback or ip.is_link_local:
                        return False
                except ValueError:
                    pass  # 不是IP地址，继续
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        import re
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名"""
        import re
        
        # 移除危险字符
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        
        # 移除控制字符
        filename = ''.join(char for char in filename if ord(char) >= 32)
        
        # 限制长度
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        # 避免保留名称
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        
        name_without_ext = os.path.splitext(filename)[0].upper()
        if name_without_ext in reserved_names:
            filename = f"_{filename}"
        
        return filename
    
    @staticmethod
    def validate_input(input_str: str, max_length: int = 1000,
                      allow_html: bool = False) -> str:
        """验证和清理用户输入"""
        if not isinstance(input_str, str):
            raise SecurityError("输入必须是字符串")
        
        # 长度检查
        if len(input_str) > max_length:
            raise SecurityError(f"输入长度超过限制 ({max_length})")
        
        # HTML转义
        if not allow_html:
            import html
            input_str = html.escape(input_str)
        
        return input_str
    
    @staticmethod
    def check_sql_injection(query: str) -> bool:
        """检查SQL注入"""
        dangerous_patterns = [
            r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
            r'(--|#|/\*|\*/)',
            r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
            r'(\b(OR|AND)\s+[\'"]\w+[\'"]\s*=\s*[\'"]\w+[\'"])',
        ]
        
        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return True
        
        return False


class SessionManager:
    """会话管理器"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.sessions = {}  # {session_id: session_data}
    
    def create_session(self, user_id: str, user_data: Dict[str, Any] = None) -> str:
        """创建会话"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            'user_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'last_accessed': datetime.utcnow().isoformat(),
            'user_data': user_data or {}
        }
        
        self.sessions[session_id] = session_data
        logger.info(f"创建会话: {session_id} for user {user_id}")
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话"""
        if session_id not in self.sessions:
            return None
        
        session_data = self.sessions[session_id]
        
        # 更新最后访问时间
        session_data['last_accessed'] = datetime.utcnow().isoformat()
        
        return session_data
    
    def update_session(self, session_id: str, data: Dict[str, Any]):
        """更新会话数据"""
        if session_id in self.sessions:
            self.sessions[session_id]['user_data'].update(data)
            self.sessions[session_id]['last_accessed'] = datetime.utcnow().isoformat()
    
    def delete_session(self, session_id: str):
        """删除会话"""
        if session_id in self.sessions:
            user_id = self.sessions[session_id].get('user_id')
            del self.sessions[session_id]
            logger.info(f"删除会话: {session_id} for user {user_id}")
    
    def cleanup_expired_sessions(self, max_age_hours: int = 24):
        """清理过期会话"""
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        expired_sessions = []
        
        for session_id, session_data in self.sessions.items():
            last_accessed = datetime.fromisoformat(session_data['last_accessed'])
            if last_accessed < cutoff_time:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        if expired_sessions:
            logger.info(f"清理了 {len(expired_sessions)} 个过期会话")


# 装饰器
def require_auth(f):
    """需要认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 这里应该检查请求中的认证信息
        # 具体实现取决于使用的Web框架
        return f(*args, **kwargs)
    return decorated_function


def rate_limit(max_requests: int = 100, window_seconds: int = 3600):
    """速率限制装饰器"""
    limiter = RateLimiter()
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 获取客户端ID（IP地址或用户ID）
            client_id = "default"  # 这里应该从请求中获取实际的客户端ID
            
            if not limiter.is_allowed(client_id, max_requests, window_seconds):
                raise SecurityError("请求过于频繁，请稍后再试")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


# 便捷函数
def hash_password(password: str) -> tuple:
    """哈希密码"""
    return PasswordManager.hash_password(password)


def verify_password(password: str, password_hash: str, salt: str) -> bool:
    """验证密码"""
    return PasswordManager.verify_password(password, password_hash, salt)


def generate_token(payload: Dict[str, Any], secret_key: str, 
                  expires_in: int = 3600) -> str:
    """生成令牌"""
    token_manager = TokenManager(secret_key)
    return token_manager.generate_token(payload, expires_in)


def verify_token(token: str, secret_key: str) -> Dict[str, Any]:
    """验证令牌"""
    token_manager = TokenManager(secret_key)
    return token_manager.verify_token(token)