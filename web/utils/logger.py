"""日志管理模块

提供统一的日志记录功能
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path
import json


class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外的详细信息
        if hasattr(record, 'details'):
            log_entry['details'] = record.details
        
        # 添加进程和线程信息
        log_entry['process'] = record.process
        log_entry['thread'] = record.thread
        
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredFormatter(logging.Formatter):
    """带颜色的控制台日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',     # 青色
        'INFO': '\033[32m',      # 绿色
        'WARNING': '\033[33m',   # 黄色
        'ERROR': '\033[31m',     # 红色
        'CRITICAL': '\033[35m',  # 紫色
        'RESET': '\033[0m'       # 重置
    }
    
    def format(self, record):
        """格式化带颜色的日志"""
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建日志消息
        log_message = f"{color}[{timestamp}] {record.levelname:8} {record.name}: {record.getMessage()}{reset}"
        
        # 添加位置信息（仅对ERROR和CRITICAL级别）
        if record.levelno >= logging.ERROR:
            log_message += f" ({record.module}:{record.funcName}:{record.lineno})"
        
        return log_message


class Logger:
    """日志管理器"""
    
    def __init__(self, name: str = 'webapp', log_dir: Optional[str] = None):
        self.name = name
        self.log_dir = Path(log_dir) if log_dir else Path('logs')
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 避免重复设置
        if self.logger.handlers:
            return
        
        self.logger.setLevel(logging.DEBUG)
        
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ColoredFormatter())
        self.logger.addHandler(console_handler)
        
        # 文件处理器（所有级别）
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f'{self.name}.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(JSONFormatter())
        self.logger.addHandler(file_handler)
        
        # 错误文件处理器（仅ERROR和CRITICAL）
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f'{self.name}_error.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(JSONFormatter())
        self.logger.addHandler(error_handler)
    
    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        self.logger.error(message, extra=kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self.logger.critical(message, extra=kwargs)
    
    def exception(self, message: str, **kwargs):
        """记录异常日志"""
        self.logger.exception(message, extra=kwargs)
    
    def log_request(self, request, response_code: int = None, response_time: float = None):
        """记录HTTP请求日志"""
        request_info = {
            'method': request.method,
            'url': request.url,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'referrer': request.headers.get('Referer', ''),
            'content_length': request.content_length
        }
        
        if response_code:
            request_info['response_code'] = response_code
        
        if response_time:
            request_info['response_time'] = f'{response_time:.3f}s'
        
        self.info(f"{request.method} {request.path}", details={'request': request_info})
    
    def log_api_call(self, endpoint: str, params: Dict[str, Any], 
                     response_time: float = None, success: bool = True):
        """记录API调用日志"""
        api_info = {
            'endpoint': endpoint,
            'params': params,
            'success': success
        }
        
        if response_time:
            api_info['response_time'] = f'{response_time:.3f}s'
        
        level = 'info' if success else 'error'
        getattr(self, level)(f"API调用: {endpoint}", details={'api': api_info})
    
    def log_file_operation(self, operation: str, path: str, 
                          success: bool = True, error: str = None):
        """记录文件操作日志"""
        file_info = {
            'operation': operation,
            'path': path,
            'success': success
        }
        
        if error:
            file_info['error'] = error
        
        level = 'info' if success else 'error'
        message = f"文件操作: {operation} - {path}"
        getattr(self, level)(message, details={'file_operation': file_info})
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          severity: str = 'warning'):
        """记录安全事件日志"""
        security_info = {
            'event_type': event_type,
            'severity': severity,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        
        message = f"安全事件: {event_type}"
        getattr(self, severity)(message, details={'security': security_info})


class LoggerManager:
    """日志管理器管理类"""
    
    _loggers: Dict[str, Logger] = {}
    _default_log_dir: Optional[str] = None
    
    @classmethod
    def set_default_log_dir(cls, log_dir: str):
        """设置默认日志目录"""
        cls._default_log_dir = log_dir
    
    @classmethod
    def get_logger(cls, name: str = 'webapp', log_dir: Optional[str] = None) -> Logger:
        """获取日志器实例"""
        if name not in cls._loggers:
            actual_log_dir = log_dir or cls._default_log_dir
            cls._loggers[name] = Logger(name, actual_log_dir)
        return cls._loggers[name]
    
    @classmethod
    def setup_logging(cls, log_dir: str = 'logs', level: str = 'INFO'):
        """设置全局日志配置"""
        cls.set_default_log_dir(log_dir)
        
        # 设置根日志器级别
        logging.getLogger().setLevel(getattr(logging, level.upper()))
        
        # 禁用第三方库的详细日志
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
    
    @classmethod
    def shutdown(cls):
        """关闭所有日志器"""
        for logger in cls._loggers.values():
            for handler in logger.logger.handlers:
                handler.close()
        cls._loggers.clear()


# 便捷函数
def get_logger(name: str = 'webapp', log_dir: Optional[str] = None) -> Logger:
    """获取日志器实例"""
    return LoggerManager.get_logger(name, log_dir)


def setup_logging(log_dir: str = 'logs', level: str = 'INFO'):
    """设置全局日志配置"""
    LoggerManager.setup_logging(log_dir, level)


# 默认日志器实例
default_logger = get_logger()