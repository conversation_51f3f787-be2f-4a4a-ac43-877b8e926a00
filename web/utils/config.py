"""配置管理模块

提供环境变量和配置文件支持
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from .exceptions import ConfigurationError
from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = '0.0.0.0'
    port: int = 8080
    debug: bool = False
    workers: int = 1
    max_request_size: int = 100 * 1024 * 1024  # 100MB
    request_timeout: int = 300  # 5分钟
    

@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = ''
    allowed_hosts: list = field(default_factory=lambda: ['*'])
    cors_origins: list = field(default_factory=lambda: ['*'])
    max_login_attempts: int = 5
    session_timeout: int = 3600  # 1小时
    csrf_protection: bool = True
    

@dataclass
class FileConfig:
    """文件配置"""
    upload_path: str = './uploads'
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_extensions: list = field(default_factory=lambda: [
        '.txt', '.md', '.json', '.xml', '.csv', '.log',
        '.py', '.js', '.html', '.css', '.java', '.cpp',
        '.jpg', '.jpeg', '.png', '.gif', '.pdf'
    ])
    thumbnail_size: tuple = (200, 200)
    

@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str = 'sqlite:///app.db'
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    

@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    file_path: str = './logs/app.log'
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    console_output: bool = True
    json_format: bool = False
    

@dataclass
class CacheConfig:
    """缓存配置"""
    type: str = 'memory'  # memory, redis, memcached
    url: str = ''
    default_timeout: int = 300  # 5分钟
    max_entries: int = 1000
    

@dataclass
class AppConfig:
    """应用配置"""
    name: str = 'Web File Manager'
    version: str = '1.0.0'
    description: str = 'A modern web-based file manager'
    timezone: str = 'UTC'
    language: str = 'zh-CN'
    theme: str = 'light'
    
    # 子配置
    server: ServerConfig = field(default_factory=ServerConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    file: FileConfig = field(default_factory=FileConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.config = AppConfig()
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            # 1. 加载默认配置
            logger.info("加载默认配置")
            
            # 2. 加载配置文件
            if self.config_file and os.path.exists(self.config_file):
                self._load_config_file()
            else:
                # 尝试加载常见的配置文件
                config_files = [
                    'config.yaml',
                    'config.yml',
                    'config.json',
                    'app.yaml',
                    'app.yml',
                    'app.json'
                ]
                
                for config_file in config_files:
                    if os.path.exists(config_file):
                        self.config_file = config_file
                        self._load_config_file()
                        break
            
            # 3. 加载环境变量
            self._load_env_vars()
            
            # 4. 验证配置
            self._validate_config()
            
            logger.info(f"配置加载完成: {self.config_file or '默认配置'}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {str(e)}")
            raise ConfigurationError(f"配置加载失败: {str(e)}")
    
    def _load_config_file(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.endswith(('.yaml', '.yml')):
                    config_data = yaml.safe_load(f)
                elif self.config_file.endswith('.json'):
                    config_data = json.load(f)
                else:
                    raise ConfigurationError(f"不支持的配置文件格式: {self.config_file}")
            
            # 更新配置
            self._update_config_from_dict(config_data)
            logger.info(f"从文件加载配置: {self.config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise ConfigurationError(f"加载配置文件失败: {str(e)}")
    
    def _load_env_vars(self):
        """加载环境变量"""
        env_mappings = {
            # 服务器配置
            'HOST': ('server', 'host'),
            'PORT': ('server', 'port'),
            'DEBUG': ('server', 'debug'),
            'WORKERS': ('server', 'workers'),
            
            # 安全配置
            'SECRET_KEY': ('security', 'secret_key'),
            'ALLOWED_HOSTS': ('security', 'allowed_hosts'),
            'CORS_ORIGINS': ('security', 'cors_origins'),
            
            # 文件配置
            'UPLOAD_PATH': ('file', 'upload_path'),
            'MAX_FILE_SIZE': ('file', 'max_file_size'),
            
            # 数据库配置
            'DATABASE_URL': ('database', 'url'),
            
            # 日志配置
            'LOG_LEVEL': ('logging', 'level'),
            'LOG_FILE': ('logging', 'file_path'),
            
            # 应用配置
            'APP_NAME': ('name',),
            'APP_VERSION': ('version',),
            'TIMEZONE': ('timezone',),
            'LANGUAGE': ('language',)
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                self._set_config_value(config_path, value)
        
        logger.info("环境变量配置加载完成")
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in config_data.items():
            if hasattr(self.config, key):
                if isinstance(value, dict):
                    # 递归更新子配置
                    sub_config = getattr(self.config, key)
                    for sub_key, sub_value in value.items():
                        if hasattr(sub_config, sub_key):
                            setattr(sub_config, sub_key, sub_value)
                else:
                    setattr(self.config, key, value)
    
    def _set_config_value(self, config_path: tuple, value: str):
        """设置配置值"""
        try:
            if len(config_path) == 1:
                # 顶级配置
                attr_name = config_path[0]
                if hasattr(self.config, attr_name):
                    # 类型转换
                    current_value = getattr(self.config, attr_name)
                    converted_value = self._convert_value(value, type(current_value))
                    setattr(self.config, attr_name, converted_value)
            
            elif len(config_path) == 2:
                # 子配置
                section, attr_name = config_path
                if hasattr(self.config, section):
                    sub_config = getattr(self.config, section)
                    if hasattr(sub_config, attr_name):
                        # 类型转换
                        current_value = getattr(sub_config, attr_name)
                        converted_value = self._convert_value(value, type(current_value))
                        setattr(sub_config, attr_name, converted_value)
        
        except Exception as e:
            logger.warning(f"设置配置值失败 {config_path}: {str(e)}")
    
    def _convert_value(self, value: str, target_type: type) -> Any:
        """转换配置值类型"""
        if target_type == bool:
            return value.lower() in ('true', '1', 'yes', 'on')
        elif target_type == int:
            return int(value)
        elif target_type == float:
            return float(value)
        elif target_type == list:
            # 支持逗号分隔的列表
            return [item.strip() for item in value.split(',') if item.strip()]
        else:
            return value
    
    def _validate_config(self):
        """验证配置"""
        # 验证必需的配置
        if not self.config.security.secret_key:
            # 生成随机密钥
            import secrets
            self.config.security.secret_key = secrets.token_hex(32)
            logger.warning("未设置SECRET_KEY，已生成随机密钥")
        
        # 验证端口范围
        if not (1 <= self.config.server.port <= 65535):
            raise ConfigurationError(f"无效的端口号: {self.config.server.port}")
        
        # 验证上传路径
        upload_path = Path(self.config.file.upload_path)
        if not upload_path.exists():
            upload_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建上传目录: {upload_path}")
        
        # 验证日志路径
        log_path = Path(self.config.logging.file_path)
        if not log_path.parent.exists():
            log_path.parent.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建日志目录: {log_path.parent}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if hasattr(value, k):
                    value = getattr(value, k)
                else:
                    return default
            
            return value
        except Exception:
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        try:
            keys = key.split('.')
            config_obj = self.config
            
            # 导航到父对象
            for k in keys[:-1]:
                if hasattr(config_obj, k):
                    config_obj = getattr(config_obj, k)
                else:
                    raise ConfigurationError(f"配置路径不存在: {key}")
            
            # 设置值
            final_key = keys[-1]
            if hasattr(config_obj, final_key):
                setattr(config_obj, final_key, value)
            else:
                raise ConfigurationError(f"配置键不存在: {key}")
        
        except Exception as e:
            raise ConfigurationError(f"设置配置失败: {str(e)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        def dataclass_to_dict(obj):
            if hasattr(obj, '__dataclass_fields__'):
                return {k: dataclass_to_dict(v) for k, v in obj.__dict__.items()}
            elif isinstance(obj, (list, tuple)):
                return [dataclass_to_dict(item) for item in obj]
            else:
                return obj
        
        return dataclass_to_dict(self.config)
    
    def save_config(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        try:
            file_path = file_path or self.config_file or 'config.yaml'
            config_dict = self.to_dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.endswith(('.yaml', '.yml')):
                    yaml.dump(config_dict, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif file_path.endswith('.json'):
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                else:
                    raise ConfigurationError(f"不支持的配置文件格式: {file_path}")
            
            logger.info(f"配置已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            raise ConfigurationError(f"保存配置失败: {str(e)}")
    
    def reload(self):
        """重新加载配置"""
        logger.info("重新加载配置")
        self._load_config()


# 全局配置实例
_config_manager = None


def get_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    
    return _config_manager


def get_config() -> AppConfig:
    """获取应用配置"""
    return get_config_manager().config


def get_setting(key: str, default: Any = None) -> Any:
    """获取配置设置"""
    return get_config_manager().get(key, default)


def set_setting(key: str, value: Any):
    """设置配置值"""
    get_config_manager().set(key, value)