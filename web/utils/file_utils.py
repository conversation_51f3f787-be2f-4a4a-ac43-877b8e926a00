"""文件操作工具模块

提供安全的文件操作功能
"""

import os
import shutil
import mimetypes
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from .exceptions import FileOperationError, SecurityError


class FileUtils:
    """文件操作工具类"""
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {
        'text': ['.txt', '.md', '.json', '.xml', '.csv', '.log'],
        'code': ['.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.php', '.rb', '.go'],
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'],
        'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
        'archive': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'],
        'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
        'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
    }
    
    # 危险的文件扩展名
    DANGEROUS_EXTENSIONS = ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js']
    
    # 最大文件大小（字节）
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    @classmethod
    def is_safe_path(cls, path: str, base_path: str = None) -> bool:
        """检查路径是否安全"""
        try:
            # 规范化路径
            normalized_path = os.path.normpath(path)
            
            # 检查是否包含危险字符
            dangerous_patterns = ['..', '~', '$']
            for pattern in dangerous_patterns:
                if pattern in normalized_path:
                    return False
            
            # 如果指定了基础路径，检查是否在允许范围内
            if base_path:
                base_path = os.path.abspath(base_path)
                full_path = os.path.abspath(os.path.join(base_path, normalized_path))
                return full_path.startswith(base_path)
            
            return True
        except Exception:
            return False
    
    @classmethod
    def safe_path_join(cls, base_path: str, *paths: str) -> str:
        """安全地连接路径"""
        try:
            # 规范化基础路径
            base_path = os.path.abspath(base_path)
            
            # 连接路径
            joined_path = os.path.join(base_path, *paths)
            full_path = os.path.abspath(joined_path)
            
            # 检查是否在基础路径内
            if not full_path.startswith(base_path):
                raise SecurityError(f"路径 {joined_path} 超出了允许的范围")
            
            return full_path
        except Exception as e:
            raise FileOperationError(f"路径连接失败: {str(e)}")
    
    @classmethod
    def get_file_info(cls, file_path: str) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                raise FileOperationError(f"文件不存在: {file_path}")
            
            stat = path.stat()
            
            # 基本信息
            info = {
                'name': path.name,
                'path': str(path.absolute()),
                'size': stat.st_size,
                'is_directory': path.is_dir(),
                'is_file': path.is_file(),
                'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed_time': datetime.fromtimestamp(stat.st_atime).isoformat(),
                'permissions': oct(stat.st_mode)[-3:]
            }
            
            # 文件特定信息
            if path.is_file():
                info.update({
                    'extension': path.suffix.lower(),
                    'mime_type': cls.get_mime_type(file_path),
                    'file_type': cls.get_file_type(path.suffix),
                    'is_text': cls.is_text_file(file_path),
                    'is_binary': cls.is_binary_file(file_path),
                    'hash_md5': cls.get_file_hash(file_path, 'md5'),
                    'hash_sha256': cls.get_file_hash(file_path, 'sha256')
                })
            
            # 目录特定信息
            elif path.is_dir():
                try:
                    children = list(path.iterdir())
                    info.update({
                        'child_count': len(children),
                        'has_children': len(children) > 0
                    })
                except PermissionError:
                    info.update({
                        'child_count': 0,
                        'has_children': False,
                        'permission_denied': True
                    })
            
            return info
        except Exception as e:
            raise FileOperationError(f"获取文件信息失败: {str(e)}", file_path)
    
    @classmethod
    def get_mime_type(cls, file_path: str) -> str:
        """获取文件MIME类型"""
        try:
            # 使用Python标准库mimetypes
            mime_type, _ = mimetypes.guess_type(file_path)
            return mime_type or 'application/octet-stream'
        except Exception:
            return 'application/octet-stream'
    
    @classmethod
    def get_file_type(cls, extension: str) -> str:
        """根据扩展名获取文件类型"""
        extension = extension.lower()
        
        for file_type, extensions in cls.ALLOWED_EXTENSIONS.items():
            if extension in extensions:
                return file_type
        
        return 'unknown'
    
    @classmethod
    def is_text_file(cls, file_path: str) -> bool:
        """检查是否为文本文件"""
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                if b'\0' in chunk:
                    return False
                
                # 尝试解码为UTF-8
                try:
                    chunk.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    return False
        except Exception:
            return False
    
    @classmethod
    def is_binary_file(cls, file_path: str) -> bool:
        """检查是否为二进制文件"""
        return not cls.is_text_file(file_path)
    
    @classmethod
    def get_file_hash(cls, file_path: str, algorithm: str = 'md5') -> str:
        """计算文件哈希值"""
        try:
            hash_func = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    hash_func.update(chunk)
            
            return hash_func.hexdigest()
        except Exception as e:
            raise FileOperationError(f"计算文件哈希失败: {str(e)}", file_path)
    
    @classmethod
    def is_allowed_file(cls, filename: str) -> bool:
        """检查文件是否被允许"""
        extension = Path(filename).suffix.lower()
        
        # 检查是否为危险文件
        if extension in cls.DANGEROUS_EXTENSIONS:
            return False
        
        # 检查是否在允许列表中
        all_allowed = []
        for extensions in cls.ALLOWED_EXTENSIONS.values():
            all_allowed.extend(extensions)
        
        return extension in all_allowed or extension == ''
    
    @classmethod
    def validate_file_size(cls, file_path: str) -> bool:
        """验证文件大小"""
        try:
            size = os.path.getsize(file_path)
            return size <= cls.MAX_FILE_SIZE
        except Exception:
            return False
    
    @classmethod
    def create_directory(cls, dir_path: str, parents: bool = True) -> bool:
        """创建目录"""
        try:
            Path(dir_path).mkdir(parents=parents, exist_ok=True)
            return True
        except Exception as e:
            raise FileOperationError(f"创建目录失败: {str(e)}", dir_path, 'create_directory')
    
    @classmethod
    def delete_file(cls, file_path: str) -> bool:
        """删除文件"""
        try:
            path = Path(file_path)
            
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
            else:
                raise FileOperationError(f"路径不存在: {file_path}")
            
            return True
        except Exception as e:
            raise FileOperationError(f"删除文件失败: {str(e)}", file_path, 'delete')
    
    @classmethod
    def copy_file(cls, src_path: str, dst_path: str) -> bool:
        """复制文件"""
        try:
            src = Path(src_path)
            dst = Path(dst_path)
            
            if src.is_file():
                shutil.copy2(src, dst)
            elif src.is_dir():
                shutil.copytree(src, dst, dirs_exist_ok=True)
            else:
                raise FileOperationError(f"源路径不存在: {src_path}")
            
            return True
        except Exception as e:
            raise FileOperationError(f"复制文件失败: {str(e)}", src_path, 'copy')
    
    @classmethod
    def move_file(cls, src_path: str, dst_path: str) -> bool:
        """移动文件"""
        try:
            shutil.move(src_path, dst_path)
            return True
        except Exception as e:
            raise FileOperationError(f"移动文件失败: {str(e)}", src_path, 'move')
    
    @classmethod
    def rename_file(cls, old_path: str, new_path: str) -> bool:
        """重命名文件"""
        try:
            Path(old_path).rename(new_path)
            return True
        except Exception as e:
            raise FileOperationError(f"重命名文件失败: {str(e)}", old_path, 'rename')
    
    @classmethod
    def read_file_content(cls, file_path: str, encoding: str = 'utf-8', 
                         max_size: int = 1024*1024) -> str:
        """读取文件内容"""
        try:
            # 检查文件大小
            if os.path.getsize(file_path) > max_size:
                raise FileOperationError(f"文件太大，超过 {max_size} 字节")
            
            # 检查是否为文本文件
            if not cls.is_text_file(file_path):
                raise FileOperationError("不能读取二进制文件内容")
            
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            raise FileOperationError(f"读取文件内容失败: {str(e)}", file_path, 'read')
    
    @classmethod
    def write_file_content(cls, file_path: str, content: str, 
                          encoding: str = 'utf-8') -> bool:
        """写入文件内容"""
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            return True
        except Exception as e:
            raise FileOperationError(f"写入文件内容失败: {str(e)}", file_path, 'write')
    
    @classmethod
    def list_directory(cls, dir_path: str, include_hidden: bool = False, 
                      sort_by: str = 'name') -> List[Dict[str, Any]]:
        """列出目录内容"""
        try:
            path = Path(dir_path)
            
            if not path.exists():
                raise FileOperationError(f"目录不存在: {dir_path}")
            
            if not path.is_dir():
                raise FileOperationError(f"路径不是目录: {dir_path}")
            
            items = []
            
            for item in path.iterdir():
                # 跳过隐藏文件（如果不包含隐藏文件）
                if not include_hidden and item.name.startswith('.'):
                    continue
                
                try:
                    item_info = cls.get_file_info(str(item))
                    items.append(item_info)
                except Exception:
                    # 跳过无法访问的文件
                    continue
            
            # 排序
            if sort_by == 'name':
                items.sort(key=lambda x: x['name'].lower())
            elif sort_by == 'size':
                items.sort(key=lambda x: x['size'], reverse=True)
            elif sort_by == 'modified':
                items.sort(key=lambda x: x['modified_time'], reverse=True)
            elif sort_by == 'type':
                items.sort(key=lambda x: (not x['is_directory'], x['name'].lower()))
            
            return items
        except Exception as e:
            raise FileOperationError(f"列出目录内容失败: {str(e)}", dir_path, 'list')
    
    @classmethod
    def search_files(cls, search_path: str, pattern: str, 
                    include_content: bool = False, 
                    case_sensitive: bool = False,
                    max_results: int = 100) -> List[Dict[str, Any]]:
        """搜索文件"""
        try:
            results = []
            search_pattern = pattern if case_sensitive else pattern.lower()
            
            def search_in_directory(dir_path: Path):
                if len(results) >= max_results:
                    return
                
                try:
                    for item in dir_path.iterdir():
                        if len(results) >= max_results:
                            break
                        
                        # 搜索文件名
                        item_name = item.name if case_sensitive else item.name.lower()
                        
                        if search_pattern in item_name:
                            try:
                                item_info = cls.get_file_info(str(item))
                                
                                # 如果需要搜索内容且是文本文件
                                if include_content and item.is_file() and cls.is_text_file(str(item)):
                                    try:
                                        content = cls.read_file_content(str(item), max_size=1024*1024)
                                        search_content = content if case_sensitive else content.lower()
                                        
                                        if search_pattern in search_content:
                                            # 提取匹配的上下文
                                            lines = content.split('\n')
                                            matching_lines = []
                                            
                                            for i, line in enumerate(lines):
                                                line_to_search = line if case_sensitive else line.lower()
                                                if search_pattern in line_to_search:
                                                    matching_lines.append(f"{i+1}: {line.strip()}")
                                                    if len(matching_lines) >= 3:  # 最多显示3行
                                                        break
                                            
                                            item_info['preview'] = '\n'.join(matching_lines)
                                    except Exception:
                                        pass
                                
                                results.append(item_info)
                            except Exception:
                                continue
                        
                        # 递归搜索子目录
                        if item.is_dir() and not item.name.startswith('.'):
                            search_in_directory(item)
                
                except PermissionError:
                    pass
            
            search_in_directory(Path(search_path))
            return results
        except Exception as e:
            raise FileOperationError(f"搜索文件失败: {str(e)}", search_path, 'search')


# 便捷函数
def safe_path_join(base_path: str, *paths: str) -> str:
    """安全地连接路径"""
    return FileUtils.safe_path_join(base_path, *paths)


def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    return FileUtils.get_file_info(file_path)


def is_safe_path(path: str, base_path: str = None) -> bool:
    """检查路径是否安全"""
    return FileUtils.is_safe_path(path, base_path)