"""开发环境配置

针对开发环境的特定配置，包括调试模式、详细日志等。
"""

from .base import BaseConfig


class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    
    # 调试模式
    DEBUG = True
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    
    # 模板配置
    TEMPLATE_AUTO_RELOAD = True
    TEMPLATE_CACHE_SIZE = 0  # 开发环境不缓存模板
    
    # 静态文件配置
    STATIC_MAX_AGE = 0  # 开发环境不缓存静态文件
    
    # 缓存配置
    CACHE_ENABLED = False  # 开发环境禁用缓存
    
    # 错误处理
    SHOW_ERROR_DETAILS = True
    
    # 热重载
    AUTO_RELOAD = True
    
    # 开发工具
    ENABLE_PROFILER = False
    ENABLE_DEBUGGER = True
    
    def __init__(self):
        super().__init__()
        print(f"🚀 启动开发环境 - {self.APP_NAME} v{self.APP_VERSION}")
        print(f"📍 服务地址: http://{self.HOST}:{self.PORT}")
        print(f"📁 项目根目录: {self.PRODUCER_ROOT}")
        print(f"🎨 模板目录: {self.TEMPLATES_DIR}")
        print(f"📦 静态文件目录: {self.STATIC_DIR}")