"""生产环境配置

针对生产环境的优化配置，包括性能优化、安全设置等。
"""

from .base import BaseConfig


class ProductionConfig(BaseConfig):
    """生产环境配置"""
    
    # 调试模式
    DEBUG = False
    
    # 日志配置
    LOG_LEVEL = 'WARNING'
    
    # 模板配置
    TEMPLATE_AUTO_RELOAD = False
    TEMPLATE_CACHE_SIZE = 1000  # 生产环境增大缓存
    
    # 静态文件配置
    STATIC_MAX_AGE = 86400  # 24小时缓存
    
    # 缓存配置
    CACHE_ENABLED = True
    CACHE_TTL = 1800  # 30分钟
    
    # 错误处理
    SHOW_ERROR_DETAILS = False
    
    # 性能优化
    ENABLE_GZIP = True
    ENABLE_ETAG = True
    
    # 安全配置
    SECURE_HEADERS = True
    CSRF_PROTECTION = True
    
    # 监控配置
    ENABLE_METRICS = True
    HEALTH_CHECK_ENABLED = True
    
    def __init__(self):
        super().__init__()
        # 生产环境验证必要的环境变量
        self._validate_production_config()
    
    def _validate_production_config(self):
        """验证生产环境配置"""
        import os
        
        # 检查必要的环境变量
        required_env_vars = ['SECRET_KEY']
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"生产环境缺少必要的环境变量: {', '.join(missing_vars)}")
        
        # 验证安全密钥
        if self.SECRET_KEY == 'dev-secret-key-change-in-production':
            raise ValueError("生产环境必须设置安全的SECRET_KEY")