"""配置管理模块

提供多环境配置支持，包括开发、测试和生产环境。
"""

from .base import BaseConfig
from .development import DevelopmentConfig
from .production import ProductionConfig

# 配置映射
CONFIG_MAP = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config(env: str = 'default') -> BaseConfig:
    """获取指定环境的配置"""
    config_class = CONFIG_MAP.get(env, DevelopmentConfig)
    return config_class()