"""基础配置类

定义所有环境共享的配置项和默认值。
"""

import os
from pathlib import Path
from typing import Dict, Any


class BaseConfig:
    """基础配置类"""
    
    # 应用基础配置
    APP_NAME = "Producer Web"
    APP_VERSION = "1.0.0"
    
    # 服务器配置
    HOST = os.getenv('WEB_HOST', 'localhost')
    PORT = int(os.getenv('WEB_PORT', 9000))
    
    # 路径配置
    BASE_DIR = Path(__file__).parent.parent
    TEMPLATES_DIR = BASE_DIR / 'templates'
    STATIC_DIR = BASE_DIR / 'static'
    
    # Producer项目根目录
    PRODUCER_ROOT = BASE_DIR.parent
    
    # 模板配置
    TEMPLATE_AUTO_RELOAD = True
    TEMPLATE_CACHE_SIZE = 400
    
    # 静态文件配置
    STATIC_URL_PREFIX = '/static'
    STATIC_MAX_AGE = 3600  # 1小时
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 安全配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # 缓存配置
    CACHE_ENABLED = True
    CACHE_TTL = 300  # 5分钟
    
    # API配置
    API_PREFIX = '/api'
    API_VERSION = 'v1'
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    def __init__(self):
        """初始化配置"""
        self._validate_config()
    
    def _validate_config(self):
        """验证配置"""
        # 确保必要的目录存在
        self.TEMPLATES_DIR.mkdir(parents=True, exist_ok=True)
        self.STATIC_DIR.mkdir(parents=True, exist_ok=True)
        
        # 验证端口范围
        if not (1024 <= self.PORT <= 65535):
            raise ValueError(f"端口号必须在1024-65535范围内，当前值: {self.PORT}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            key: getattr(self, key)
            for key in dir(self)
            if not key.startswith('_') and not callable(getattr(self, key))
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return getattr(self, key, default)