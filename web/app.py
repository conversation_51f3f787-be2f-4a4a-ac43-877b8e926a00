"""主应用入口文件

整合所有模块并提供统一的启动接口
"""

import os
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import get_config, get_config_manager
from utils.logger import get_logger, setup_logging
from utils.exceptions import WebAppException, handle_exception
from core.server import WebServer

logger = get_logger(__name__)


class WebApplication:
    """Web应用主类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.config = None
        self.app = None
        self._initialize()
    
    def _initialize(self):
        """初始化应用"""
        try:
            # 1. 加载配置
            logger.info("正在初始化Web应用...")
            config_manager = get_config_manager(self.config_file)
            self.config = config_manager.config
            
            # 2. 设置日志
            setup_logging(
                log_dir='logs',
                level=self.config.logging.level
            )
            
            logger.info(f"应用配置加载完成: {self.config.name} v{self.config.version}")
            
            # 3. 创建必要的目录
            self._create_directories()
            
            # 4. 创建Web服务器
            self.app = WebServer(self.config)
            
            # 5. 设置路由
            self._setup_routes()
            
            logger.info("Web应用初始化完成")
            
        except Exception as e:
            logger.error(f"创建目录失败: {str(e)}")
            raise
    
    def _setup_routes(self):
        """设置应用路由
        
        从routes模块导入并配置所有路由规则
        """
        try:
            # 修复相对导入问题
            import sys
            from pathlib import Path
            web_dir = Path(__file__).parent
            if str(web_dir) not in sys.path:
                sys.path.insert(0, str(web_dir))
            
            from routes import setup_routes
            setup_routes(self.app.router, self.config)
            logger.info("路由配置完成")
        except ImportError as e:
            logger.warning(f"路由模块导入失败: {str(e)}")
        except Exception as e:
            logger.error(f"路由设置失败: {str(e)}")
            raise WebAppException(f"应用初始化失败: {str(e)}")
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.config.file.upload_path,
            Path(self.config.logging.file_path).parent,
            'static/uploads',
            'static/thumbnails',
            'temp'
        ]
        
        for directory in directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
    
    def run(self, host: Optional[str] = None, port: Optional[int] = None, 
           debug: Optional[bool] = None):
        """运行应用"""
        try:
            # 使用配置中的默认值
            host = host or self.config.server.host
            port = port or self.config.server.port
            debug = debug if debug is not None else self.config.server.debug
            
            logger.info(f"启动Web服务器: http://{host}:{port}")
            logger.info(f"调试模式: {'开启' if debug else '关闭'}")
            
            # 启动Web服务器
            self.app.start(host=host, port=port)
            
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭应用...")
        except Exception as e:
            logger.error(f"运行应用时发生错误: {str(e)}")
            raise
    
    def get_app(self):
        """获取Flask应用实例"""
        return self.app
    
    def get_config(self):
        """获取应用配置"""
        return self.config


def create_application(config_file: Optional[str] = None) -> WebApplication:
    """创建应用实例"""
    return WebApplication(config_file)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Web文件管理器')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--host', default=None, help='服务器主机地址')
    parser.add_argument('--port', type=int, default=None, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--version', action='version', version='Web File Manager 1.0.0')
    
    args = parser.parse_args()
    
    try:
        # 创建应用
        app = create_application(args.config)
        
        # 显示启动信息
        config = app.get_config()
        print(f"\n{config.name} v{config.version}")
        print(f"{config.description}")
        print(f"配置文件: {args.config or '默认配置'}")
        print("-" * 50)
        
        # 运行应用
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()