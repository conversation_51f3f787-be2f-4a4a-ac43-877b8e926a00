# 🎬 Producer - 历史短剧视频制作系统

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![LangChain](https://img.shields.io/badge/LangChain-0.3+-green.svg)](https://langchain.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于LangChain的历史短剧视频自动制作系统，采用**混合优化方案**（本地+云端），实现端到端自动化视频制作。

## ✨ 核心特性

- 🤖 **智能剧本生成**：基于历史背景自动生成完整剧本
- 🎨 **多模态内容创作**：图像、视频、语音一体化生成
- 💰 **成本优化控制**：混合方案，月成本仅$20-50
- ⚡ **高效制作流程**：单集制作时间1-2小时
- 🔧 **模块化架构**：支持多种AI服务适配器
- 📊 **实时监控**：成本追踪和质量控制

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/matrix/producer.git
cd producer

# 安装uv包管理器（推荐）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync
```

### 2. 配置设置

```bash
# 复制环境变量模板
cp .env.template .env

# 编辑配置文件，填入API密钥
nano .env
```

### 3. 运行测试

```bash
# 运行系统测试
uv run python test_workflow.py

# 或使用CLI工具
uv run python -m producer.cli test
```

### 4. 制作第一个视频

```bash
# 使用CLI制作视频
uv run python -m producer.cli produce \
  --title "明朝风云" \
  --theme "宫廷斗争" \
  --era "明朝" \
  --duration 180
```

## 🏗️ 技术架构

### 混合优化方案技术栈

```yaml
文本生成:
  - GPT-4o-mini (主力)
  - Claude-3-Sonnet (备选)
  - 通义千问 (免费额度)

图像生成:
  - FLUX.1 [schnell] (本地免费)
  - SDXL-Lightning (API备选)

视频生成:
  - 可灵(Kling) (长视频+表演)
  - SVD+ComfyUI (转场动效)

语音合成:
  - CosyVoice 2.0 (本地免费)
  - ElevenLabs (高质量付费)
```

### 项目结构

```
producer/
├── 📁 core/                 # 核心引擎
│   ├── chains/             # LangChain链路
│   ├── workflow.py         # 工作流引擎
│   ├── models.py          # 数据模型
│   └── config.py          # 配置管理
├── 📁 adapters/            # AI服务适配器
│   ├── text/              # 文本生成适配器
│   ├── image/             # 图像生成适配器
│   ├── video/             # 视频生成适配器
│   └── voice/             # 语音合成适配器
├── 📁 config/              # 配置文件
├── 📁 templates/           # Jinja2模板
├── 📁 tests/              # 测试套件
├── 📁 doc/                # 文档
└── 📁 manual/             # 用户手册
```

## 🎯 使用指南

### CLI命令

```bash
# 制作单个视频
uv run python -m producer.cli produce -t "剧本标题" -e "历史朝代"

# 批量制作
uv run python -m producer.cli batch scripts.json

# 查看系统状态
uv run python -m producer.cli status

# 运行测试
uv run python -m producer.cli test

# 查看版本信息
uv run python -m producer.cli version
```

### Python API

```python
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController
from core.models import ScriptData

# 初始化系统
config = ConfigManager()
cost_controller = CostController(config)
workflow = WorkflowEngine(config, cost_controller)

# 创建剧本数据
script_data = ScriptData(
    script_id="example_001",
    title="明朝风云",
    theme="宫廷斗争",
    era="明朝",
    total_duration=180
)

# 执行制作流程
result = await workflow.execute_workflow(script_data)
print(f"制作状态: {result.status}")
print(f"总成本: ${result.total_cost:.4f}")
```

### Docker部署

```bash
# 构建镜像
docker build -t producer .

# 使用docker-compose启动
docker-compose up -d

# 查看日志
docker-compose logs -f producer
```

## 📊 性能指标

| 指标 | 混合优化方案 | 零成本方案 |
|------|-------------|-----------|
| 开发时间 | 5.5-7.5天 | 17-25天 |
| 月产能力 | 120-180集 | 30-60集 |
| 质量评分 | 9.5/10 | 9/10 |
| 月成本 | $20-50 | $0 |
| 单集制作时间 | 1-2小时 | 4-8小时 |

## 文档

详细的使用文档和开发文档请参阅 [用户手册](./doc/manual/)。

## 🔧 开发

### 安装开发依赖

```bash
# 安装所有依赖（包括开发工具）
uv sync --all-extras

# 或者选择性安装
uv sync --extra dev --extra comfyui
```

### 代码质量

```bash
# 代码格式化
uv run black .

# 类型检查
uv run mypy producer/

# 代码检查
uv run flake8 producer/

# 运行测试
uv run pytest
```

### 测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/adapters/

# 运行工作流测试
uv run python test_workflow.py
```

> 进一步阅读：图像适配器的更详细测试说明（执行原理、参数解释、流程分解、最佳实践与故障排查），请参见《[图像适配器测试命令手册](doc/图像适配器测试命令手册.md)》。

## 📈 监控和调试

### 成本监控

系统内置实时成本追踪，支持：
- 日预算限制
- 月预算控制
- API调用成本统计
- 智能服务路由

### 日志系统

```bash
# 查看实时日志
tail -f logs/producer.log

# 调试模式运行
LOG_LEVEL=DEBUG uv run python -m producer.cli produce -t "测试"
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [LangChain](https://langchain.com) - 核心框架
- [LiteLLM](https://litellm.ai) - 模型路由
- [FLUX.1](https://github.com/black-forest-labs/flux) - 图像生成
- [CosyVoice](https://github.com/FunAudioLLM/CosyVoice) - 语音合成

---

<div align="center">
  <strong>🎬 让AI为你创作精彩的历史短剧！</strong>
</div>