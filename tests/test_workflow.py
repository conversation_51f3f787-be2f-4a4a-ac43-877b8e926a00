#!/usr/bin/env python3
"""
工作流程测试脚本
测试producer系统的核心工作流程
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.models import ScriptData, ProjectData
from core.workflow import WorkflowEngine
from core.config import ConfigManager
from core.cost_control import CostController


async def test_basic_workflow():
    """测试基本工作流程"""
    print("开始测试基本工作流程...")
    
    try:
        # 初始化配置
        config = ConfigManager()
        
        # 初始化成本控制器
        cost_controller = CostController(config)
        
        # 创建工作流引擎
        workflow = WorkflowEngine(config, cost_controller)
        
        # 创建测试项目数据
        project_data = ProjectData(
            project_id="test_001",
            name="测试历史短剧",
            description="一个用于测试的历史短剧项目",
            budget=1000.0
        )
        
        # 创建测试脚本数据
        script_data = ScriptData(
            script_id="test_script_001",
            title="明朝风云",
            theme="宫廷斗争",
            era="明朝",
            summary="一个关于明朝宫廷斗争的历史短剧",
            characters=[],
            scenes=[],
            dialogues=[],
            media_cues=[],
            total_duration=180  # 3分钟
        )
        
        print(f"项目: {project_data.name}")
        print(f"剧本: {script_data.title}")
        print(f"主题: {script_data.theme}")
        print(f"时长: {script_data.total_duration}秒")
        
        # 执行工作流程
        print("\n开始执行工作流程...")
        result = await workflow.execute_workflow(script_data)
        
        # 输出结果
        print("\n工作流程执行结果:")
        print(f"状态: {result.status}")
        print(f"执行ID: {result.execution_id}")
        print(f"总成本: ${result.total_cost:.4f}")
        print(f"完成步骤: {len(result.completed_steps)}")
        print(f"失败步骤: {len(result.failed_steps)}")
        
        if result.status == 'completed':
            print("\n✅ 工作流程测试成功!")
            
            # 显示步骤结果
            for step_name in result.completed_steps:
                print(f"  {step_name}: completed")
                
        else:
            print("\n❌ 工作流程测试失败!")
            if result.failed_steps:
                print(f"失败步骤: {', '.join(result.failed_steps)}")
        
        return {'status': result.status.value if hasattr(result.status, 'value') else str(result.status)}
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def test_individual_chains():
    """测试各个链路的独立功能"""
    print("\n开始测试各个链路...")
    
    try:
        config = ConfigManager()
        cost_controller = CostController(config)
        workflow = WorkflowEngine(config, cost_controller)
        
        # 测试大纲生成
        print("\n测试大纲生成链路...")
        from core.chains.outline_chain import OutlineRequest
        
        outline_request = OutlineRequest(
            title="测试剧本",
            theme="宫廷斗争",
            dynasty="明朝",
            duration=180,
            genre="历史剧情",
            target_audience="成人",
            tone="严肃",
            key_elements=["权力斗争", "忠诚背叛"],
            constraints=["适合短视频"]
        )
        
        outline_response = await workflow.outline_chain.generate_outline(outline_request)
        print(f"✅ 大纲生成成功: {outline_response.title}")
        print(f"   角色数量: {len(outline_response.characters)}")
        print(f"   场景数量: {len(outline_response.scenes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 链路测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("Producer 工作流程测试")
    print("=" * 50)
    
    # 运行测试
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # 测试各个链路
        chain_result = loop.run_until_complete(test_individual_chains())
        
        # 测试完整工作流程
        workflow_result = loop.run_until_complete(test_basic_workflow())
        
        print("\n" + "=" * 50)
        print("测试总结:")
        print(f"链路测试: {'✅ 通过' if chain_result else '❌ 失败'}")
        print(f"工作流测试: {'✅ 通过' if workflow_result and workflow_result.get('status') == 'completed' else '❌ 失败'}")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生未预期的错误: {str(e)}")
    finally:
        loop.close()


if __name__ == "__main__":
    main()