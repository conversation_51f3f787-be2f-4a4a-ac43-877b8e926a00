#!/usr/bin/env python3
"""
测试AI评价系统的实际调用
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append('/Volumes/mini_matrix/github/producer')

from core.historical_story_evaluator import HistoricalStoryEvaluator

async def test_ai_evaluation():
    """测试AI评价系统"""
    print("🧪 测试AI评价系统...")
    
    # 创建评价器实例
    evaluator = HistoricalStoryEvaluator(model_name="glm-4-flash")
    
    # 准备测试文本
    test_text = """
    李白: 陛下，臣李白有诗一首，愿献于陛下之前。
    唐玄宗: 李白，汝诗才横溢，朕甚喜闻。
    李白: 陛下过誉了，臣只是略抒胸臆。
    唐玄宗: 朕愿闻其详，李白，请吧。
    """
    
    # 调用评价系统
    try:
        # 直接调用_llm_api方法查看原始响应
        prompt = evaluator._build_evaluation_prompt(test_text, "唐朝", "李白在宫廷中向唐玄宗献诗")
        print("\n📝 评价提示词:")
        print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
        
        print("\n🔄 调用AI API...")
        raw_response = await evaluator._call_llm_api(prompt)
        
        print("\n📄 原始API响应:")
        print(repr(raw_response))
        print("\n📄 原始API响应(可读):")
        print(raw_response)
        
        # 解析评价结果
        result = evaluator._parse_evaluation_response(raw_response)
        
        print("\n📊 AI评价结果:")
        print(f"   可读性评分: {result.get('readability_score', 0.0)}")
        print(f"   历史真实性评分: {result.get('authenticity_score', 0.0)}")
        print(f"   吸引力评分: {result.get('engagement_score', 0.0)}")
        
        print("\n💡 AI评价建议:")
        for suggestion in result.get('suggestions', []):
            print(f"   - {suggestion}")
            
        print("\n✅ AI评价系统测试成功!")
        
    except Exception as e:
        print(f"\n❌ AI评价系统测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_evaluation())