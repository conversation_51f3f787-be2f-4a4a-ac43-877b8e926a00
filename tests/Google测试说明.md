# Google 图像生成测试说明

## 测试文件结构

所有测试文件已按照项目规范移动到 `tests/` 目录下：

### 适配器测试 (`tests/adapters/`)
- **test_google_image_adapter.py** - Google图像生成适配器完整功能测试
  - 测试适配器配置和初始化
  - 测试图像生成功能
  - 测试成本估算
  - 测试便捷函数

### API测试 (`tests/api_tests/`)
- **test_google_config.py** - Google API配置验证
  - 检查环境变量配置
  - 验证配置管理器
  - 测试API连接性
  
- **test_google_image_api.py** - 简单Google图像生成API测试
  - 直接API调用测试
  - 避免复杂依赖
  - 基础功能验证

## 运行测试

### 1. 配置验证
```bash
# 验证Google API配置是否正确
python tests/api_tests/test_google_config.py
```

### 2. API基础测试
```bash
# 简单API连接和图像生成测试
python tests/api_tests/test_google_image_api.py
```

### 3. 完整适配器测试
```bash
# 完整的适配器功能测试
python tests/adapters/test_google_image_adapter.py
```

### 4. 使用pytest运行
```bash
# 运行所有Google相关测试
pytest tests/ -k "google" -v

# 运行特定测试文件
pytest tests/adapters/test_google_image_adapter.py -v
pytest tests/api_tests/test_google_config.py -v
```

## 环境要求

确保已设置环境变量：
```bash
export GOOGLE_AI_API_KEY="your_google_ai_api_key_here"
```

或在 `.env` 文件中配置：
```env
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
```

## 测试输出

所有测试生成的图片会保存在：
- `output/test_outputs/google_images/` - 适配器测试输出
- `output/test_outputs/simple_google_test/` - API测试输出

## 项目规范

⚠️ **重要规范**：所有新生成的测试程序都必须放在 `tests/` 目录下，按类型分类：
- 适配器测试 → `tests/adapters/`
- API测试 → `tests/api_tests/`
- 单元测试 → `tests/unit/`
- 集成测试 → `tests/integration/`

## 成本优化方案

### 免费服务组合（推荐）
- **图像生成**: Google Gemini 2.0 Flash (完全免费)
- **语音合成**: EdgeTTS (完全免费) 
- **文本生成**: GLM + DeepSeek (低成本)

### 语音合成选择
- **EdgeTTS**: 免费，支持多语言，质量良好
- **CosyVoice**: 付费但成本低，中文效果更佳
- **ElevenLabs**: 付费高质量，用于重要场景