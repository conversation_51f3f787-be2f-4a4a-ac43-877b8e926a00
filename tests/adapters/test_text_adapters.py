"""文本生成适配器测试

测试QwenAdapter、GPTA<PERSON>pter和ClaudeAdapter的功能。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal

from producer.adapters.text.qwen_adapter import QwenAdapter
from producer.adapters.text.gpt_adapter import GPTAdapter
from producer.adapters.text.claude_adapter import ClaudeAdapter
from producer.adapters.text.base_text import TextGenerationRequest, TextGenerationResponse
from producer.adapters.base import AdapterError, RateLimitError, APIError


class TestQwenAdapter:
    """测试QwenAdapter"""
    
    @pytest.fixture
    def qwen_adapter(self, text_adapter_config, mock_config_manager, mock_cost_controller):
        """创建QwenAdapter实例"""
        config = text_adapter_config
        config.model = "qwen-turbo"
        config.base_url = "https://dashscope.aliyuncs.com/api/v1"
        return QwenAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, qwen_adapter):
        """测试配置验证"""
        result = await qwen_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, qwen_adapter):
        """测试获取支持的模型"""
        models = qwen_adapter.get_supported_models()
        assert "qwen-turbo" in models
        assert "qwen-plus" in models
        assert "qwen-max" in models
    
    def test_estimate_cost(self, qwen_adapter, sample_text_request):
        """测试成本估算"""
        cost = qwen_adapter.estimate_cost(sample_text_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, qwen_adapter, sample_text_request, mock_http_session):
        """测试成功生成文本"""
        # 模拟API响应
        mock_response = {
            "output": {
                "text": "Generated text content"
            },
            "usage": {
                "input_tokens": 10,
                "output_tokens": 20,
                "total_tokens": 30
            }
        }
        
        mock_http_session.post.return_value.json = AsyncMock(return_value=mock_response)
        
        with patch.object(qwen_adapter, 'session', mock_http_session):
            response = await qwen_adapter.generate(sample_text_request)
        
        assert isinstance(response, TextGenerationResponse)
        assert response.text == "Generated text content"
        assert response.input_tokens == 10
        assert response.output_tokens == 20
        assert response.total_tokens == 30
    
    @pytest.mark.asyncio
    async def test_generate_text_api_error(self, qwen_adapter, sample_text_request, mock_http_session):
        """测试API错误处理"""
        mock_http_session.post.return_value.status = 400
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "error": {
                "code": "InvalidParameter",
                "message": "Invalid request parameter"
            }
        })
        
        with patch.object(qwen_adapter, 'session', mock_http_session):
            with pytest.raises(APIError):
                await qwen_adapter.generate(sample_text_request)
    
    @pytest.mark.asyncio
    async def test_generate_text_rate_limit(self, qwen_adapter, sample_text_request, mock_http_session):
        """测试速率限制处理"""
        mock_http_session.post.return_value.status = 429
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "error": {
                "code": "Throttling.User",
                "message": "Request was denied due to request throttling"
            }
        })
        
        with patch.object(qwen_adapter, 'session', mock_http_session):
            with pytest.raises(RateLimitError):
                await qwen_adapter.generate(sample_text_request)


class TestGPTAdapter:
    """测试GPTAdapter"""
    
    @pytest.fixture
    def gpt_adapter(self, text_adapter_config, mock_config_manager, mock_cost_controller):
        """创建GPTAdapter实例"""
        config = text_adapter_config
        config.model = "gpt-4o-mini"
        config.base_url = "https://api.openai.com/v1"
        return GPTAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, gpt_adapter):
        """测试配置验证"""
        result = await gpt_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, gpt_adapter):
        """测试获取支持的模型"""
        models = gpt_adapter.get_supported_models()
        assert "gpt-4o" in models
        assert "gpt-4o-mini" in models
        assert "gpt-3.5-turbo" in models
    
    def test_estimate_cost(self, gpt_adapter, sample_text_request):
        """测试成本估算"""
        cost = gpt_adapter.estimate_cost(sample_text_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, gpt_adapter, sample_text_request, mock_http_session):
        """测试成功生成文本"""
        # 模拟API响应
        mock_response = {
            "choices": [{
                "message": {
                    "content": "Generated text content"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            }
        }
        
        mock_http_session.post.return_value.json = AsyncMock(return_value=mock_response)
        
        with patch.object(gpt_adapter, 'session', mock_http_session):
            response = await gpt_adapter.generate(sample_text_request)
        
        assert isinstance(response, TextGenerationResponse)
        assert response.text == "Generated text content"
        assert response.input_tokens == 10
        assert response.output_tokens == 20
        assert response.total_tokens == 30
        assert response.finish_reason == "stop"
    
    @pytest.mark.asyncio
    async def test_generate_text_with_system_message(self, gpt_adapter, mock_http_session):
        """测试带系统消息的文本生成"""
        request = TextGenerationRequest(
            prompt="Tell me a joke",
            system_message="You are a helpful assistant",
            max_tokens=100,
            model="gpt-4o-mini"
        )
        
        mock_response = {
            "choices": [{
                "message": {
                    "content": "Why did the AI cross the road?"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 15,
                "completion_tokens": 10,
                "total_tokens": 25
            }
        }
        
        mock_http_session.post.return_value.json = AsyncMock(return_value=mock_response)
        
        with patch.object(gpt_adapter, 'session', mock_http_session):
            response = await gpt_adapter.generate(request)
        
        assert response.text == "Why did the AI cross the road?"
        
        # 验证请求包含系统消息
        call_args = mock_http_session.post.call_args
        request_data = call_args[1]['json']
        messages = request_data['messages']
        assert len(messages) == 2
        assert messages[0]['role'] == 'system'
        assert messages[0]['content'] == "You are a helpful assistant"
        assert messages[1]['role'] == 'user'
        assert messages[1]['content'] == "Tell me a joke"


class TestClaudeAdapter:
    """测试ClaudeAdapter"""
    
    @pytest.fixture
    def claude_adapter(self, text_adapter_config, mock_config_manager, mock_cost_controller):
        """创建ClaudeAdapter实例"""
        config = text_adapter_config
        config.model = "claude-3-5-sonnet-20241022"
        config.base_url = "https://api.anthropic.com"
        return ClaudeAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, claude_adapter):
        """测试配置验证"""
        result = await claude_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, claude_adapter):
        """测试获取支持的模型"""
        models = claude_adapter.get_supported_models()
        assert "claude-3-5-sonnet-20241022" in models
        assert "claude-3-5-haiku-20241022" in models
        assert "claude-3-opus-20240229" in models
    
    def test_estimate_cost(self, claude_adapter, sample_text_request):
        """测试成本估算"""
        cost = claude_adapter.estimate_cost(sample_text_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, claude_adapter, sample_text_request, mock_http_session):
        """测试成功生成文本"""
        # 模拟API响应
        mock_response = {
            "content": [{
                "type": "text",
                "text": "Generated text content"
            }],
            "usage": {
                "input_tokens": 10,
                "output_tokens": 20
            },
            "stop_reason": "end_turn"
        }
        
        mock_http_session.post.return_value.json = AsyncMock(return_value=mock_response)
        
        with patch.object(claude_adapter, 'session', mock_http_session):
            response = await claude_adapter.generate(sample_text_request)
        
        assert isinstance(response, TextGenerationResponse)
        assert response.text == "Generated text content"
        assert response.input_tokens == 10
        assert response.output_tokens == 20
        assert response.total_tokens == 30
        assert response.finish_reason == "end_turn"
    
    @pytest.mark.asyncio
    async def test_generate_text_with_system_message(self, claude_adapter, mock_http_session):
        """测试带系统消息的文本生成"""
        request = TextGenerationRequest(
            prompt="Explain quantum computing",
            system_message="You are a physics professor",
            max_tokens=200,
            model="claude-3-5-sonnet-20241022"
        )
        
        mock_response = {
            "content": [{
                "type": "text",
                "text": "Quantum computing is a revolutionary technology..."
            }],
            "usage": {
                "input_tokens": 20,
                "output_tokens": 50
            },
            "stop_reason": "end_turn"
        }
        
        mock_http_session.post.return_value.json = AsyncMock(return_value=mock_response)
        
        with patch.object(claude_adapter, 'session', mock_http_session):
            response = await claude_adapter.generate(request)
        
        assert "Quantum computing is a revolutionary technology" in response.text
        
        # 验证请求包含系统消息
        call_args = mock_http_session.post.call_args
        request_data = call_args[1]['json']
        assert request_data['system'] == "You are a physics professor"
        assert request_data['messages'][0]['role'] == 'user'
        assert request_data['messages'][0]['content'] == "Explain quantum computing"
    
    @pytest.mark.asyncio
    async def test_generate_text_streaming(self, claude_adapter, sample_text_request, mock_http_session):
        """测试流式生成"""
        request = sample_text_request
        request.stream = True
        
        # 模拟流式响应
        async def mock_stream_response():
            chunks = [
                b'data: {"type": "content_block_delta", "delta": {"text": "Hello"}}\n\n',
                b'data: {"type": "content_block_delta", "delta": {"text": " world"}}\n\n',
                b'data: {"type": "message_stop"}\n\n'
            ]
            for chunk in chunks:
                yield chunk
        
        mock_http_session.post.return_value.content.iter_chunked = mock_stream_response
        
        with patch.object(claude_adapter, 'session', mock_http_session):
            response = await claude_adapter.generate(request)
        
        assert response.text == "Hello world"


class TestTextAdapterIntegration:
    """文本适配器集成测试"""
    
    @pytest.mark.asyncio
    async def test_adapter_retry_mechanism(self, text_adapter_config, mock_config_manager, mock_cost_controller):
        """测试重试机制"""
        # 为Qwen适配器设置正确的模型
        config = text_adapter_config
        config.model = "qwen2.5-7b-instruct"
        adapter = QwenAdapter(config, mock_config_manager, mock_cost_controller)
        
        # 模拟前两次请求失败，第三次成功
        call_count = 0
        async def mock_post(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            response = AsyncMock()
            if call_count < 3:
                response.status = 500
                response.json = AsyncMock(return_value={"error": "Internal server error"})
            else:
                response.status = 200
                response.json = AsyncMock(return_value={
                    "output": {"text": "Success after retry"},
                    "usage": {"input_tokens": 10, "output_tokens": 20, "total_tokens": 30}
                })
            return response
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post = mock_post
            
            request = TextGenerationRequest(
                prompt="Test retry",
                max_tokens=100,
                model="qwen-turbo"
            )
            
            response = await adapter.generate(request)
            
            assert response.text == "Success after retry"
            assert call_count == 3  # 验证重试了3次
    
    @pytest.mark.asyncio
    async def test_cost_tracking(self, text_adapter_config, mock_config_manager, mock_cost_controller):
        """测试成本跟踪"""
        # 为Claude适配器设置正确的模型
        config = text_adapter_config
        config.model = "claude-3-5-haiku-20241022"
        adapter = ClaudeAdapter(config, mock_config_manager, mock_cost_controller)
        
        mock_response = {
            "choices": [{
                "message": {"content": "Test response"},
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 15,
                "completion_tokens": 25,
                "total_tokens": 40
            }
        }
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post.return_value.status = 200
            mock_session.post.return_value.json = AsyncMock(return_value=mock_response)
            
            request = TextGenerationRequest(
                prompt="Test cost tracking",
                max_tokens=100,
                model="claude-3-5-haiku-20241022"
            )
            
            await adapter.generate(request)
            
            # 验证成本控制器被调用
            mock_cost_controller.check_cost_limit.assert_called_once()
            mock_cost_controller.record_cost.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, text_adapter_config, mock_config_manager, mock_cost_controller):
        """测试并发请求"""
        # 确保使用正确的模型
        config = text_adapter_config
        config.model = "gpt-4o-mini"
        adapter = GPTAdapter(config, mock_config_manager, mock_cost_controller)
        
        mock_response = {
            "content": [{"type": "text", "text": "Concurrent response"}],
            "usage": {"input_tokens": 10, "output_tokens": 15},
            "stop_reason": "end_turn"
        }
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post.return_value.status = 200
            mock_session.post.return_value.json = AsyncMock(return_value=mock_response)
            
            requests = [
                TextGenerationRequest(
                    prompt=f"Request {i}",
                    max_tokens=50,
                    model="gpt-4o-mini"
                )
                for i in range(5)
            ]
            
            # 并发执行请求
            responses = await asyncio.gather(*[
                adapter.generate(request) for request in requests
            ])
            
            assert len(responses) == 5
            for response in responses:
                assert response.text == "Concurrent response"
                assert response.input_tokens == 10
                assert response.output_tokens == 15