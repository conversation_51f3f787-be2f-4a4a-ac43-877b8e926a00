#!/usr/bin/env python3
"""Google图像生成适配器测试脚本

测试Google Gemini和Imagen模型的图像生成功能。
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from adapters.image.google_adapter import GoogleImageAdapter, create_google_image_adapter
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController


async def test_google_image_adapter():
    """测试Google图像生成适配器"""
    
    # 检查API密钥
    api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 请设置 GOOGLE_AI_API_KEY 或 GEMINI_API_KEY 环境变量")
        print("获取API密钥：https://aistudio.google.com/apikey")
        return False
    
    print("🚀 开始测试 Google 图像生成适配器")
    print(f"API Key: {api_key[:8]}...{api_key[-4:]}")
    
    try:
        # 初始化配置
        config_manager = ConfigManager()
        cost_controller = CostController(config_manager)
        
        # 测试所有可用模型
        models_to_test = [
            "gemini-2.0-flash",  # 免费模型
            # "imagen-4.0-fast",   # 付费模型，暂时注释避免费用
        ]
        
        for model_name in models_to_test:
            print(f"\n📱 测试模型: {model_name}")
            
            # 创建适配器配置
            config = AdapterConfig(
                service_name="google_image",
                model=model_name,
                api_url="https://generativelanguage.googleapis.com/v1beta"
            )
            
            # 创建适配器
            adapter = create_google_image_adapter(config, config_manager, cost_controller)
            
            # 验证配置（简单测试）
            print("🔧 验证适配器配置...")
            
            # 获取模型信息
            model_info = adapter.get_model_info()
            print(f"模型信息: {model_info['name']}")
            print(f"免费层级: {'是' if model_info['free_tier'] else '否'}")
            print(f"每张图片成本: ${model_info['price_per_image']}")
            
            # 测试图像生成
            test_prompts = [
                "A majestic dragon flying over ancient Chinese mountains, traditional ink painting style",
                "A cute robot reading a book in a cozy library, cartoon style",
                "Ancient Chinese architecture with cherry blossoms, realistic photography style"
            ]
            
            for i, prompt in enumerate(test_prompts, 1):
                print(f"\n🎨 测试 {i}: {prompt}")
                
                try:
                    # 估算成本
                    estimated_cost = adapter.estimate_cost(prompt, num_images=1)
                    print(f"估算成本: ${estimated_cost:.4f}")
                    
                    # 生成图像
                    print("正在生成图像...")
                    result = await adapter.generate(
                        prompt=prompt,
                        num_images=1,
                        size="1024x1024",
                        style="realistic"
                    )
                    
                    if result.success:
                        response = result.data
                        print(f"✅ 生成成功!")
                        print(f"   生成时间: {response.generation_time:.2f}秒")
                        print(f"   图像数量: {len(response.images)}")
                        print(f"   实际成本: ${result.cost_usd:.4f}")
                        
                        # 保存图像
                        output_dir = Path("output/test_outputs/google_images")
                        output_dir.mkdir(parents=True, exist_ok=True)
                        
                        saved_paths = response.save_images(
                            output_dir=str(output_dir),
                            prefix=f"{model_name}_test_{i}"
                        )
                        
                        print(f"   图像已保存: {saved_paths}")
                        
                    else:
                        print(f"❌ 生成失败: {result.error_message}")
                        
                except Exception as e:
                    print(f"❌ 测试失败: {str(e)}")
                
                # 添加延时避免API限制
                await asyncio.sleep(2)
        
        print("\n🎉 Google图像生成适配器测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False


async def test_convenience_function():
    """测试便捷函数"""
    print("\n🔧 测试便捷函数...")
    
    try:
        from adapters.image.google_adapter import generate_image_with_google
        
        response = await generate_image_with_google(
            prompt="A simple red rose on white background",
            model="gemini-2.0-flash",
            num_images=1,
            size="512x512"
        )
        
        print(f"✅ 便捷函数测试成功!")
        print(f"   生成图像数: {len(response.images)}")
        
        # 保存测试图像
        output_dir = Path("output/test_outputs/google_images")
        saved_paths = response.save_images(
            output_dir=str(output_dir),
            prefix="convenience_test"
        )
        print(f"   图像已保存: {saved_paths}")
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {str(e)}")


def show_usage_guide():
    """显示使用指南"""
    print("""
📋 Google 图像生成适配器使用指南

1. 设置API密钥:
   export GOOGLE_AI_API_KEY="your_api_key_here"
   # 或者
   export GEMINI_API_KEY="your_api_key_here"

2. 获取API密钥:
   https://aistudio.google.com/apikey

3. 可用模型:
   - gemini-2.0-flash: 免费模型，支持基本图像生成
   - imagen-4.0-fast: $0.02/图片，快速高质量
   - imagen-4.0-standard: $0.04/图片，标准质量
   - imagen-4.0-ultra: $0.06/图片，最高质量
   - imagen-3.0: $0.03/图片，经典版本

4. 使用示例:
   ```python
   from adapters.image.google_adapter import generate_image_with_google
   
   # 免费生成
   response = await generate_image_with_google(
       prompt="A beautiful sunset over mountains",
       model="gemini-2.0-flash",
       num_images=1
   )
   
   # 高质量生成（付费）
   response = await generate_image_with_google(
       prompt="Professional headshot photo",
       model="imagen-4.0-ultra",
       size="2048x2048"
   )
   ```

5. 成本优势:
   - Gemini 2.0 Flash: 完全免费
   - Imagen 4: $0.02-0.06/图片 (比DALL-E便宜)
   - 支持免费层级测试

6. 特性:
   ✅ 免费图像生成 (Gemini 2.0 Flash)
   ✅ 高质量图像生成 (Imagen 4)
   ✅ 多种尺寸支持 (512x512 到 2048x2048)
   ✅ 批量生成 (最多4张/请求)
   ✅ 自动成本控制
   ✅ 异步处理
   """)


async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "guide":
        show_usage_guide()
        return
    
    print("🤖 Google 图像生成适配器测试")
    print("=" * 50)
    
    # 主测试
    success = await test_google_image_adapter()
    
    if success:
        # 测试便捷函数
        await test_convenience_function()
        
        print(f"\n✅ 所有测试通过!")
        print(f"📁 生成的图片保存在: output/test_outputs/google_images/")
    else:
        print(f"\n❌ 测试失败")
        show_usage_guide()


if __name__ == "__main__":
    asyncio.run(main())