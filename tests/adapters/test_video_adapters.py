"""视频生成适配器测试

测试KlingVideoAdapter和RunwayAdapter的功能。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
import base64

from producer.adapters.video.kling_adapter import KlingVideoAdapter
from producer.adapters.video.runway_adapter import RunwayAdapter
from producer.adapters.video.base_video import (
    VideoGenerationRequest, VideoGenerationResponse,
    VideoQuality, VideoStyle
)
from producer.adapters.base import AdapterError, RateLimitError, APIError


class TestKlingVideoAdapter:
    """测试KlingVideoAdapter"""
    
    @pytest.fixture
    def kling_adapter(self, video_adapter_config, mock_config_manager, mock_cost_controller):
        """创建KlingVideoAdapter实例"""
        config = video_adapter_config
        config.model = "kling-v1"
        config.base_url = "https://api.kuaishou.com/ai/kling"
        return KlingVideoAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, kling_adapter):
        """测试配置验证"""
        result = await kling_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, kling_adapter):
        """测试获取支持的模型"""
        models = kling_adapter.get_supported_models()
        assert "kling-v1" in models
        assert "kling-v1-5" in models
        assert "kling-pro" in models
    
    def test_get_supported_resolutions(self, kling_adapter):
        """测试获取支持的分辨率"""
        resolutions = kling_adapter.get_supported_resolutions()
        assert (1280, 720) in resolutions
        assert (720, 1280) in resolutions
        assert (1024, 1024) in resolutions
    
    def test_get_supported_formats(self, kling_adapter):
        """测试获取支持的格式"""
        formats = kling_adapter.get_supported_formats()
        assert "mp4" in formats
        assert "mov" in formats
    
    def test_estimate_cost(self, kling_adapter, sample_video_request):
        """测试成本估算"""
        cost = kling_adapter.estimate_cost(sample_video_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_video_success(self, kling_adapter, sample_video_request, mock_http_session):
        """测试成功生成视频"""
        # 模拟任务创建响应
        task_response = {
            "code": 0,
            "message": "success",
            "data": {
                "task_id": "task_123",
                "task_status": "submitted"
            }
        }
        
        # 模拟任务完成响应
        completed_response = {
            "code": 0,
            "message": "success",
            "data": {
                "task_id": "task_123",
                "task_status": "succeed",
                "task_result": {
                    "videos": [{
                        "url": "https://example.com/generated_video.mp4",
                        "duration": 5.0,
                        "width": 1280,
                        "height": 720
                    }]
                }
            }
        }
        
        # 模拟视频下载
        video_data = b"fake_video_data"
        
        # 设置mock响应
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=completed_response)
        
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=video_data)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = [get_response, download_response]
        
        with patch.object(kling_adapter, 'session', mock_http_session):
            response = await kling_adapter.generate(sample_video_request)
        
        assert isinstance(response, VideoGenerationResponse)
        assert response.video_url == "https://example.com/generated_video.mp4"
        assert response.video_data == base64.b64encode(video_data).decode()
        assert response.duration == 5.0
        assert response.width == 1280
        assert response.height == 720
        assert response.format == "mp4"
    
    @pytest.mark.asyncio
    async def test_generate_video_task_polling(self, kling_adapter, sample_video_request, mock_http_session):
        """测试任务轮询机制"""
        # 模拟任务创建
        task_response = {
            "code": 0,
            "data": {"task_id": "task_123", "task_status": "submitted"}
        }
        
        # 模拟轮询过程：submitted -> processing -> succeed
        poll_responses = [
            {
                "code": 0,
                "data": {"task_id": "task_123", "task_status": "submitted"}
            },
            {
                "code": 0,
                "data": {"task_id": "task_123", "task_status": "processing"}
            },
            {
                "code": 0,
                "data": {
                    "task_id": "task_123",
                    "task_status": "succeed",
                    "task_result": {
                        "videos": [{
                            "url": "https://example.com/video.mp4",
                            "duration": 5.0,
                            "width": 1280,
                            "height": 720
                        }]
                    }
                }
            }
        ]
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_responses = []
        for poll_response in poll_responses:
            get_resp = AsyncMock()
            get_resp.status = 200
            get_resp.json = AsyncMock(return_value=poll_response)
            get_responses.append(get_resp)
        
        # 添加视频下载响应
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=b"video_data")
        get_responses.append(download_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = get_responses
        
        with patch.object(kling_adapter, 'session', mock_http_session):
            with patch('asyncio.sleep', new_callable=AsyncMock):  # 跳过实际等待
                response = await kling_adapter.generate(sample_video_request)
        
        assert response.video_url == "https://example.com/video.mp4"
        # 验证轮询了3次状态
        assert mock_http_session.get.call_count == 4  # 3次状态查询 + 1次下载
    
    @pytest.mark.asyncio
    async def test_generate_video_task_failed(self, kling_adapter, sample_video_request, mock_http_session):
        """测试任务失败处理"""
        task_response = {
            "code": 0,
            "data": {"task_id": "task_123", "task_status": "submitted"}
        }
        
        failed_response = {
            "code": 0,
            "data": {
                "task_id": "task_123",
                "task_status": "failed",
                "fail_reason": "Content violates community guidelines"
            }
        }
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=failed_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.return_value = get_response
        
        with patch.object(kling_adapter, 'session', mock_http_session):
            with pytest.raises(APIError, match="Content violates community guidelines"):
                await kling_adapter.generate(sample_video_request)
    
    @pytest.mark.asyncio
    async def test_generate_video_api_error(self, kling_adapter, sample_video_request, mock_http_session):
        """测试API错误处理"""
        mock_http_session.post.return_value.status = 400
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "code": 40001,
            "message": "Invalid request parameters"
        })
        
        with patch.object(kling_adapter, 'session', mock_http_session):
            with pytest.raises(APIError):
                await kling_adapter.generate(sample_video_request)
    
    @pytest.mark.asyncio
    async def test_generate_video_rate_limit(self, kling_adapter, sample_video_request, mock_http_session):
        """测试速率限制处理"""
        mock_http_session.post.return_value.status = 429
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "code": 42901,
            "message": "Rate limit exceeded"
        })
        
        with patch.object(kling_adapter, 'session', mock_http_session):
            with pytest.raises(RateLimitError):
                await kling_adapter.generate(sample_video_request)


class TestRunwayAdapter:
    """测试RunwayAdapter"""
    
    @pytest.fixture
    def runway_adapter(self, video_adapter_config, mock_config_manager, mock_cost_controller):
        """创建RunwayAdapter实例"""
        config = video_adapter_config
        config.model = "gen3a_turbo"
        config.base_url = "https://api.runwayml.com"
        return RunwayAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, runway_adapter):
        """测试配置验证"""
        result = await runway_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, runway_adapter):
        """测试获取支持的模型"""
        models = runway_adapter.get_supported_models()
        assert "gen3a_turbo" in models
        assert "gen3a" in models
        assert "gen2" in models
    
    def test_get_supported_resolutions(self, runway_adapter):
        """测试获取支持的分辨率"""
        resolutions = runway_adapter.get_supported_resolutions()
        assert (1280, 768) in resolutions
        assert (768, 1280) in resolutions
        assert (1408, 768) in resolutions
    
    def test_get_supported_formats(self, runway_adapter):
        """测试获取支持的格式"""
        formats = runway_adapter.get_supported_formats()
        assert "mp4" in formats
    
    def test_estimate_cost(self, runway_adapter, sample_video_request):
        """测试成本估算"""
        cost = runway_adapter.estimate_cost(sample_video_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_video_success(self, runway_adapter, sample_video_request, mock_http_session):
        """测试成功生成视频"""
        # 模拟任务创建响应
        task_response = {
            "id": "task_123",
            "status": "PENDING",
            "createdAt": "2024-01-01T00:00:00Z"
        }
        
        # 模拟任务完成响应
        completed_response = {
            "id": "task_123",
            "status": "SUCCEEDED",
            "output": ["https://example.com/generated_video.mp4"],
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:01:00Z"
        }
        
        # 模拟视频下载
        video_data = b"fake_video_data"
        
        # 设置mock响应
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=completed_response)
        
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=video_data)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = [get_response, download_response]
        
        with patch.object(runway_adapter, 'session', mock_http_session):
            response = await runway_adapter.generate(sample_video_request)
        
        assert isinstance(response, VideoGenerationResponse)
        assert response.video_url == "https://example.com/generated_video.mp4"
        assert response.video_data == base64.b64encode(video_data).decode()
        assert response.duration == sample_video_request.duration
        assert response.width == sample_video_request.width
        assert response.height == sample_video_request.height
        assert response.format == "mp4"
    
    @pytest.mark.asyncio
    async def test_generate_video_with_image_prompt(self, runway_adapter, mock_http_session):
        """测试带图像提示的视频生成"""
        request = VideoGenerationRequest(
            prompt="A cat walking in the garden",
            image_prompt="https://example.com/cat.jpg",
            duration=5.0,
            width=1280,
            height=720,
            quality=VideoQuality.HIGH,
            model="gen3a_turbo"
        )
        
        task_response = {
            "id": "task_456",
            "status": "PENDING"
        }
        
        completed_response = {
            "id": "task_456",
            "status": "SUCCEEDED",
            "output": ["https://example.com/cat_video.mp4"]
        }
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=completed_response)
        
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=b"cat_video_data")
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = [get_response, download_response]
        
        with patch.object(runway_adapter, 'session', mock_http_session):
            response = await runway_adapter.generate(request)
        
        assert response.video_url == "https://example.com/cat_video.mp4"
        
        # 验证请求包含图像提示
        call_args = mock_http_session.post.call_args
        request_data = call_args[1]['json']
        assert 'promptImage' in request_data
        assert request_data['promptImage'] == "https://example.com/cat.jpg"
    
    @pytest.mark.asyncio
    async def test_generate_video_task_polling(self, runway_adapter, sample_video_request, mock_http_session):
        """测试任务轮询机制"""
        # 模拟任务创建
        task_response = {
            "id": "task_123",
            "status": "PENDING"
        }
        
        # 模拟轮询过程：PENDING -> RUNNING -> SUCCEEDED
        poll_responses = [
            {"id": "task_123", "status": "PENDING"},
            {"id": "task_123", "status": "RUNNING"},
            {
                "id": "task_123",
                "status": "SUCCEEDED",
                "output": ["https://example.com/video.mp4"]
            }
        ]
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_responses = []
        for poll_response in poll_responses:
            get_resp = AsyncMock()
            get_resp.status = 200
            get_resp.json = AsyncMock(return_value=poll_response)
            get_responses.append(get_resp)
        
        # 添加视频下载响应
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=b"video_data")
        get_responses.append(download_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = get_responses
        
        with patch.object(runway_adapter, 'session', mock_http_session):
            with patch('asyncio.sleep', new_callable=AsyncMock):  # 跳过实际等待
                response = await runway_adapter.generate(sample_video_request)
        
        assert response.video_url == "https://example.com/video.mp4"
        # 验证轮询了3次状态
        assert mock_http_session.get.call_count == 4  # 3次状态查询 + 1次下载
    
    @pytest.mark.asyncio
    async def test_generate_video_task_failed(self, runway_adapter, sample_video_request, mock_http_session):
        """测试任务失败处理"""
        task_response = {
            "id": "task_123",
            "status": "PENDING"
        }
        
        failed_response = {
            "id": "task_123",
            "status": "FAILED",
            "failure": "Content policy violation",
            "failureCode": "CONTENT_MODERATED"
        }
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=failed_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.return_value = get_response
        
        with patch.object(runway_adapter, 'session', mock_http_session):
            with pytest.raises(APIError, match="Content policy violation"):
                await runway_adapter.generate(sample_video_request)
    
    @pytest.mark.asyncio
    async def test_generate_video_api_error(self, runway_adapter, sample_video_request, mock_http_session):
        """测试API错误处理"""
        mock_http_session.post.return_value.status = 400
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "error": {
                "type": "invalid_request_error",
                "message": "Invalid prompt provided"
            }
        })
        
        with patch.object(runway_adapter, 'session', mock_http_session):
            with pytest.raises(APIError):
                await runway_adapter.generate(sample_video_request)
    
    @pytest.mark.asyncio
    async def test_generate_video_rate_limit(self, runway_adapter, sample_video_request, mock_http_session):
        """测试速率限制处理"""
        mock_http_session.post.return_value.status = 429
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "error": {
                "type": "rate_limit_exceeded",
                "message": "Rate limit exceeded"
            }
        })
        
        with patch.object(runway_adapter, 'session', mock_http_session):
            with pytest.raises(RateLimitError):
                await runway_adapter.generate(sample_video_request)


class TestVideoAdapterIntegration:
    """视频适配器集成测试"""
    
    @pytest.mark.asyncio
    async def test_adapter_retry_mechanism(self, video_adapter_config, mock_config_manager, mock_cost_controller):
        """测试重试机制"""
        adapter = KlingVideoAdapter(video_adapter_config, mock_config_manager, mock_cost_controller)
        
        # 模拟前两次请求失败，第三次成功
        call_count = 0
        async def mock_post(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            response = AsyncMock()
            if call_count < 3:
                response.status = 500
                response.json = AsyncMock(return_value={"error": "Internal server error"})
            else:
                response.status = 200
                response.json = AsyncMock(return_value={
                    "code": 0,
                    "data": {"task_id": "task_123", "task_status": "submitted"}
                })
            return response
        
        # 模拟任务完成和视频下载
        async def mock_get(*args, **kwargs):
            if "task_123" in str(args):
                response = AsyncMock()
                response.status = 200
                response.json = AsyncMock(return_value={
                    "code": 0,
                    "data": {
                        "task_id": "task_123",
                        "task_status": "succeed",
                        "task_result": {
                            "videos": [{
                                "url": "https://example.com/video.mp4",
                                "duration": 5.0,
                                "width": 1280,
                                "height": 720
                            }]
                        }
                    }
                })
                return response
            else:
                # 视频下载
                response = AsyncMock()
                response.status = 200
                response.read = AsyncMock(return_value=b"video_data")
                return response
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post = mock_post
            mock_session.get = mock_get
            
            request = VideoGenerationRequest(
                prompt="Test retry",
                duration=5.0,
                width=1280,
                height=720,
                quality=VideoQuality.HIGH,
                model="kling-v1"
            )
            
            response = await adapter.generate(request)
            
            assert response.video_url == "https://example.com/video.mp4"
            assert call_count == 3  # 验证重试了3次
    
    @pytest.mark.asyncio
    async def test_cost_tracking(self, video_adapter_config, mock_config_manager, mock_cost_controller):
        """测试成本跟踪"""
        adapter = RunwayAdapter(video_adapter_config, mock_config_manager, mock_cost_controller)
        
        task_response = {
            "id": "task_123",
            "status": "PENDING"
        }
        
        completed_response = {
            "id": "task_123",
            "status": "SUCCEEDED",
            "output": ["https://example.com/video.mp4"]
        }
        
        with patch.object(adapter, 'session') as mock_session:
            post_response = AsyncMock()
            post_response.status = 200
            post_response.json = AsyncMock(return_value=task_response)
            
            get_response = AsyncMock()
            get_response.status = 200
            get_response.json = AsyncMock(return_value=completed_response)
            
            download_response = AsyncMock()
            download_response.status = 200
            download_response.read = AsyncMock(return_value=b"video_data")
            
            mock_session.post.return_value = post_response
            mock_session.get.side_effect = [get_response, download_response]
            
            request = VideoGenerationRequest(
                prompt="Test cost tracking",
                duration=5.0,
                width=1280,
                height=720,
                quality=VideoQuality.HIGH,
                model="gen3a_turbo"
            )
            
            await adapter.generate(request)
            
            # 验证成本控制器被调用
            mock_cost_controller.check_cost_limit.assert_called_once()
            mock_cost_controller.record_cost.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, video_adapter_config, mock_config_manager, mock_cost_controller):
        """测试并发请求处理"""
        adapter = KlingVideoAdapter(video_adapter_config, mock_config_manager, mock_cost_controller)
        
        # 模拟并发任务创建和完成
        task_ids = [f"task_{i}" for i in range(3)]
        
        async def mock_post(*args, **kwargs):
            # 根据请求内容返回不同的任务ID
            request_data = kwargs.get('json', {})
            prompt = request_data.get('prompt', '')
            task_id = f"task_{prompt.split()[-1]}"  # 从prompt中提取任务ID
            
            response = AsyncMock()
            response.status = 200
            response.json = AsyncMock(return_value={
                "code": 0,
                "data": {"task_id": task_id, "task_status": "submitted"}
            })
            return response
        
        async def mock_get(*args, **kwargs):
            url = str(args[0]) if args else str(kwargs.get('url', ''))
            
            if any(task_id in url for task_id in task_ids):
                # 任务状态查询
                task_id = next(tid for tid in task_ids if tid in url)
                response = AsyncMock()
                response.status = 200
                response.json = AsyncMock(return_value={
                    "code": 0,
                    "data": {
                        "task_id": task_id,
                        "task_status": "succeed",
                        "task_result": {
                            "videos": [{
                                "url": f"https://example.com/{task_id}.mp4",
                                "duration": 5.0,
                                "width": 1280,
                                "height": 720
                            }]
                        }
                    }
                })
                return response
            else:
                # 视频下载
                response = AsyncMock()
                response.status = 200
                response.read = AsyncMock(return_value=b"concurrent_video_data")
                return response
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post = mock_post
            mock_session.get = mock_get
            
            requests = [
                VideoGenerationRequest(
                    prompt=f"Concurrent request {i}",
                    duration=5.0,
                    width=1280,
                    height=720,
                    quality=VideoQuality.HIGH,
                    model="kling-v1"
                )
                for i in range(3)
            ]
            
            # 并发执行请求
            responses = await asyncio.gather(*[
                adapter.generate(request) for request in requests
            ])
            
            assert len(responses) == 3
            for i, response in enumerate(responses):
                assert f"task_{i}" in response.video_url
                assert response.video_data is not None
                assert response.duration == 5.0