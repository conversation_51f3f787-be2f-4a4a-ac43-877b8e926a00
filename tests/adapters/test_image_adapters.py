"""图像生成适配器测试

测试FluxImageAdapter和SDXLLightningAdapter的功能。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
import base64

from producer.adapters.image.flux_adapter import FluxImageAdapter
from producer.adapters.image.sdxl_lightning_adapter import SDXLLightningAdapter
from producer.adapters.image.base_image import (
    ImageGenerationRequest, ImageGenerationResponse,
    ImageQuality, ImageStyle
)
from producer.adapters.base import AdapterError, RateLimitError, APIError


class TestFluxImageAdapter:
    """测试FluxImageAdapter"""
    
    @pytest.fixture
    def flux_adapter(self, image_adapter_config, mock_config_manager, mock_cost_controller):
        """创建FluxImageAdapter实例"""
        config = image_adapter_config
        config.model = "flux-pro"
        config.base_url = "https://api.bfl.ml"
        return FluxImageAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, flux_adapter):
        """测试配置验证"""
        result = await flux_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, flux_adapter):
        """测试获取支持的模型"""
        models = flux_adapter.get_supported_models()
        assert "flux-pro" in models
        assert "flux-dev" in models
        assert "flux-schnell" in models
    
    def test_get_supported_resolutions(self, flux_adapter):
        """测试获取支持的分辨率"""
        resolutions = flux_adapter.get_supported_resolutions()
        assert (1024, 1024) in resolutions
        assert (1280, 720) in resolutions
        assert (720, 1280) in resolutions
    
    def test_estimate_cost(self, flux_adapter, sample_image_request):
        """测试成本估算"""
        cost = flux_adapter.estimate_cost(sample_image_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_image_success(self, flux_adapter, sample_image_request, mock_http_session):
        """测试成功生成图像"""
        # 模拟任务创建响应
        task_response = {
            "id": "task_123",
            "status": "pending"
        }
        
        # 模拟任务完成响应
        completed_response = {
            "id": "task_123",
            "status": "ready",
            "result": {
                "sample": "https://example.com/generated_image.jpg"
            }
        }
        
        # 模拟图像下载
        image_data = b"fake_image_data"
        
        # 设置mock响应
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=completed_response)
        
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=image_data)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = [get_response, download_response]
        
        with patch.object(flux_adapter, 'session', mock_http_session):
            response = await flux_adapter.generate(sample_image_request)
        
        assert isinstance(response, ImageGenerationResponse)
        assert response.image_url == "https://example.com/generated_image.jpg"
        assert response.image_data == base64.b64encode(image_data).decode()
        assert response.width == sample_image_request.width
        assert response.height == sample_image_request.height
    
    @pytest.mark.asyncio
    async def test_generate_image_task_polling(self, flux_adapter, sample_image_request, mock_http_session):
        """测试任务轮询机制"""
        # 模拟任务创建
        task_response = {"id": "task_123", "status": "pending"}
        
        # 模拟轮询过程：pending -> running -> ready
        poll_responses = [
            {"id": "task_123", "status": "pending"},
            {"id": "task_123", "status": "running"},
            {
                "id": "task_123",
                "status": "ready",
                "result": {"sample": "https://example.com/image.jpg"}
            }
        ]
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_responses = []
        for poll_response in poll_responses:
            get_resp = AsyncMock()
            get_resp.status = 200
            get_resp.json = AsyncMock(return_value=poll_response)
            get_responses.append(get_resp)
        
        # 添加图像下载响应
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=b"image_data")
        get_responses.append(download_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = get_responses
        
        with patch.object(flux_adapter, 'session', mock_http_session):
            with patch('asyncio.sleep', new_callable=AsyncMock):  # 跳过实际等待
                tasks = [flux_adapter.generate(sample_image_request) for _ in range(3)]
                responses = await asyncio.gather(*tasks)
            
            # 验证所有响应都成功
            for response in responses:
                assert response.success
        # 验证轮询了3次状态
        assert mock_http_session.get.call_count == 4  # 3次状态查询 + 1次下载
    
    @pytest.mark.asyncio
    async def test_generate_image_task_failed(self, flux_adapter, sample_image_request, mock_http_session):
        """测试任务失败处理"""
        task_response = {"id": "task_123", "status": "pending"}
        
        failed_response = {
            "id": "task_123",
            "status": "error",
            "error": "Generation failed due to content policy"
        }
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=failed_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.return_value = get_response
        
        with patch.object(flux_adapter, 'session', mock_http_session):
            with pytest.raises(APIError, match="Generation failed"):
                await flux_adapter.generate(sample_image_request)
    
    @pytest.mark.asyncio
    async def test_generate_image_api_error(self, flux_adapter, sample_image_request, mock_http_session):
        """测试API错误处理"""
        mock_http_session.post.return_value.status = 400
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "error": {
                "type": "invalid_request_error",
                "message": "Invalid prompt provided"
            }
        })
        
        with patch.object(flux_adapter, 'session', mock_http_session):
            with pytest.raises(APIError):
                await flux_adapter.generate(sample_image_request)
    
    @pytest.mark.asyncio
    async def test_generate_image_rate_limit(self, flux_adapter, sample_image_request, mock_http_session):
        """测试速率限制处理"""
        mock_http_session.post.return_value.status = 429
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "error": {
                "type": "rate_limit_exceeded",
                "message": "Rate limit exceeded"
            }
        })
        
        with patch.object(flux_adapter, 'session', mock_http_session):
            with pytest.raises(RateLimitError):
                await flux_adapter.generate(sample_image_request)


class TestSDXLLightningAdapter:
    """测试SDXLLightningAdapter"""
    
    @pytest.fixture
    def sdxl_adapter(self, image_adapter_config, mock_config_manager, mock_cost_controller):
        """创建SDXLLightningAdapter实例"""
        config = image_adapter_config
        config.model = "stable-diffusion-xl-1024-v1-0"
        config.base_url = "https://api.stability.ai"
        return SDXLLightningAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, sdxl_adapter):
        """测试配置验证"""
        result = await sdxl_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, sdxl_adapter):
        """测试获取支持的模型"""
        models = sdxl_adapter.get_supported_models()
        assert "stable-diffusion-xl-1024-v1-0" in models
        assert "sdxl-lightning" in models
        assert "stable-diffusion-xl-beta-v2-2-2" in models
    
    def test_get_supported_resolutions(self, sdxl_adapter):
        """测试获取支持的分辨率"""
        resolutions = sdxl_adapter.get_supported_resolutions()
        assert (1024, 1024) in resolutions
        assert (1152, 896) in resolutions
        assert (896, 1152) in resolutions
    
    def test_estimate_cost(self, sdxl_adapter, sample_image_request):
        """测试成本估算"""
        cost = sdxl_adapter.estimate_cost(sample_image_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_generate_image_success(self, sdxl_adapter, sample_image_request, mock_http_session):
        """测试成功生成图像"""
        # 模拟API响应
        api_response = {
            "artifacts": [{
                "base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                "seed": 12345,
                "finishReason": "SUCCESS"
            }]
        }
        
        mock_http_session.post.return_value.status = 200
        mock_http_session.post.return_value.json = AsyncMock(return_value=api_response)
        
        with patch.object(sdxl_adapter, 'session', mock_http_session):
            response = await sdxl_adapter.generate(sample_image_request)
        
        assert isinstance(response, ImageGenerationResponse)
        assert response.image_data == api_response["artifacts"][0]["base64"]
        assert response.seed == 12345
        assert response.width == sample_image_request.width
        assert response.height == sample_image_request.height
    
    @pytest.mark.asyncio
    async def test_generate_image_with_style_preset(self, sdxl_adapter, mock_http_session):
        """测试带样式预设的图像生成"""
        request = ImageGenerationRequest(
            prompt="A beautiful landscape",
            width=1024,
            height=1024,
            quality=95,
            style="photographic"
        )
        
        api_response = {
            "artifacts": [{
                "base64": "test_image_data",
                "seed": 67890,
                "finishReason": "SUCCESS"
            }]
        }
        
        mock_http_session.post.return_value.status = 200
        mock_http_session.post.return_value.json = AsyncMock(return_value=api_response)
        
        with patch.object(sdxl_adapter, 'session', mock_http_session):
            response = await sdxl_adapter.generate(request)
        
        assert response.image_data == "test_image_data"
        
        # 验证请求包含样式预设
        call_args = mock_http_session.post.call_args
        request_data = call_args[1]['json']
        assert 'style_preset' in request_data
        assert request_data['style_preset'] == 'photographic'
    
    @pytest.mark.asyncio
    async def test_generate_image_with_negative_prompt(self, sdxl_adapter, mock_http_session):
        """测试带负面提示词的图像生成"""
        request = ImageGenerationRequest(
            prompt="A portrait of a person",
            negative_prompt="blurry, low quality, distorted",
            width=1024,
            height=1024,
            quality=95
        )
        
        api_response = {
            "artifacts": [{
                "base64": "sunset_image_data",
                "seed": 11111,
                "finishReason": "SUCCESS"
            }]
        }
        
        mock_http_session.post.return_value.status = 200
        mock_http_session.post.return_value.json = AsyncMock(return_value=api_response)
        
        with patch.object(sdxl_adapter, 'session', mock_http_session):
            response = await sdxl_adapter.generate(request)
        
        assert response.image_data == "sunset_image_data"
        
        # 验证请求包含负面提示词
        call_args = mock_http_session.post.call_args
        request_data = call_args[1]['json']
        assert 'text_prompts' in request_data
        prompts = request_data['text_prompts']
        assert len(prompts) == 2
        assert prompts[0]['text'] == "A beautiful sunset"
        assert prompts[0]['weight'] == 1.0
        assert prompts[1]['text'] == "blurry, low quality, distorted"
        assert prompts[1]['weight'] == -1.0
    
    @pytest.mark.asyncio
    async def test_generate_image_content_filter(self, sdxl_adapter, sample_image_request, mock_http_session):
        """测试内容过滤处理"""
        api_response = {
            "artifacts": [{
                "base64": "",
                "seed": 0,
                "finishReason": "CONTENT_FILTERED"
            }]
        }
        
        mock_http_session.post.return_value.status = 200
        mock_http_session.post.return_value.json = AsyncMock(return_value=api_response)
        
        with patch.object(sdxl_adapter, 'session', mock_http_session):
            with pytest.raises(APIError, match="Content filtered"):
                await sdxl_adapter.generate(sample_image_request)
    
    @pytest.mark.asyncio
    async def test_generate_image_api_error(self, sdxl_adapter, sample_image_request, mock_http_session):
        """测试API错误处理"""
        mock_http_session.post.return_value.status = 400
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "id": "bad_request",
            "name": "bad_request",
            "errors": ["Invalid prompt length"]
        })
        
        with patch.object(sdxl_adapter, 'session', mock_http_session):
            with pytest.raises(APIError):
                await sdxl_adapter.generate(sample_image_request)


class TestImageAdapterIntegration:
    """图像适配器集成测试"""
    
    @pytest.mark.asyncio
    async def test_adapter_retry_mechanism(self, image_adapter_config, mock_config_manager, mock_cost_controller):
        """测试重试机制"""
        adapter = FluxImageAdapter(image_adapter_config, mock_config_manager, mock_cost_controller)
        
        # 模拟前两次请求失败，第三次成功
        call_count = 0
        async def mock_post(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            response = AsyncMock()
            if call_count < 3:
                response.status = 500
                response.json = AsyncMock(return_value={"error": "Internal server error"})
            else:
                response.status = 200
                response.json = AsyncMock(return_value={"id": "task_123", "status": "pending"})
            return response
        
        # 模拟任务完成和图像下载
        async def mock_get(*args, **kwargs):
            if "task_123" in str(args):
                response = AsyncMock()
                response.status = 200
                response.json = AsyncMock(return_value={
                    "id": "task_123",
                    "status": "ready",
                    "result": {"sample": "https://example.com/image.jpg"}
                })
                return response
            else:
                # 图像下载
                response = AsyncMock()
                response.status = 200
                response.read = AsyncMock(return_value=b"image_data")
                return response
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post = mock_post
            mock_session.get = mock_get
            
            request = ImageGenerationRequest(
                prompt="Test retry",
                width=1024,
                height=1024
            )
            
            response = await adapter.generate(request)
            
            assert response.success
            assert call_count == 3  # 验证重试了3次
    
    @pytest.mark.asyncio
    async def test_cost_tracking(self, image_adapter_config, mock_config_manager, mock_cost_controller):
        """测试成本跟踪"""
        # 为SDXL适配器设置正确的模型
        config = image_adapter_config
        config.model = "sdxl-lightning"
        adapter = SDXLLightningAdapter(config, mock_config_manager, mock_cost_controller)
        
        api_response = {
            "artifacts": [{
                "base64": "test_image",
                "seed": 12345,
                "finishReason": "SUCCESS"
            }]
        }
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post.return_value.status = 200
            mock_session.post.return_value.json = AsyncMock(return_value=api_response)
            
            request = ImageGenerationRequest(
                prompt="Test cost tracking",
                width=1024,
                height=1024,
                quality=95
            )
            
            await adapter.generate(request)
            
            # 验证成本控制器被调用
            mock_cost_controller.check_cost_limit.assert_called_once()
            mock_cost_controller.record_cost.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, image_adapter_config, mock_config_manager, mock_cost_controller):
        """测试并发请求"""
        # 确保使用正确的模型
        config = image_adapter_config
        config.model = "flux-pro"
        adapter = FluxImageAdapter(config, mock_config_manager, mock_cost_controller)
        
        # 模拟并发任务创建和完成
        task_ids = [f"task_{i}" for i in range(3)]
        
        async def mock_post(*args, **kwargs):
            # 根据请求内容返回不同的任务ID
            request_data = kwargs.get('json', {})
            prompt = request_data.get('prompt', '')
            task_id = f"task_{prompt.split()[-1]}"  # 从prompt中提取任务ID
            
            response = AsyncMock()
            response.status = 200
            response.json = AsyncMock(return_value={"id": task_id, "status": "pending"})
            return response
        
        async def mock_get(*args, **kwargs):
            url = str(args[0]) if args else str(kwargs.get('url', ''))
            
            if any(task_id in url for task_id in task_ids):
                # 任务状态查询
                task_id = next(tid for tid in task_ids if tid in url)
                response = AsyncMock()
                response.status = 200
                response.json = AsyncMock(return_value={
                    "id": task_id,
                    "status": "ready",
                    "result": {"sample": f"https://example.com/{task_id}.jpg"}
                })
                return response
            else:
                # 图像下载
                response = AsyncMock()
                response.status = 200
                response.read = AsyncMock(return_value=b"concurrent_image_data")
                return response
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post = mock_post
            mock_session.get = mock_get
            
            requests = [
                ImageGenerationRequest(
                    prompt=f"Concurrent request {i}",
                    width=1024,
                    height=1024,
                    quality=95
                )
                for i in range(3)
            ]
            
            # 并发执行请求
            responses = await asyncio.gather(*[
                adapter.generate(request) for request in requests
            ])
            
            assert len(responses) == 3
            for response in responses:
                assert response.success