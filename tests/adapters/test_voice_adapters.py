"""语音合成适配器测试

测试CosyVoiceAdapter和EdgeTTSAdapter的功能。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
import base64

from producer.adapters.voice.cosyvoice_adapter import CosyVoiceAdapter
from producer.adapters.voice.edge_tts_adapter import EdgeTTSAdapter
from producer.adapters.voice.base_voice import (
    VoiceSynthesisRequest, VoiceSynthesisResponse,
    VoiceGender, VoiceStyle
)
from producer.adapters.base import AdapterError, RateLimitError, APIError


class TestCosyVoiceAdapter:
    """测试CosyVoiceAdapter"""
    
    @pytest.fixture
    def cosyvoice_adapter(self, voice_adapter_config, mock_config_manager, mock_cost_controller):
        """创建CosyVoiceAdapter实例"""
        config = voice_adapter_config
        config.model = "cosyvoice-v1"
        config.base_url = "https://dashscope.aliyuncs.com"
        return CosyVoiceAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, cosyvoice_adapter):
        """测试配置验证"""
        result = await cosyvoice_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, cosyvoice_adapter):
        """测试获取支持的模型"""
        models = cosyvoice_adapter.get_supported_models()
        assert "cosyvoice-v1" in models
        assert "cosyvoice-v1-male" in models
        assert "cosyvoice-v1-storyteller" in models
    
    def test_get_available_voices(self, cosyvoice_adapter):
        """测试获取可用语音"""
        voices = cosyvoice_adapter.get_available_voices()
        assert len(voices) > 0
        assert any(voice['id'] == 'zh-CN-XiaoxiaoNeural' for voice in voices)
        assert any(voice['id'] == 'zh-CN-YunxiNeural' for voice in voices)
    
    def test_get_supported_languages(self, cosyvoice_adapter):
        """测试获取支持的语言"""
        languages = cosyvoice_adapter.get_supported_languages()
        assert "zh-CN" in languages
        assert "en-US" in languages
    
    def test_estimate_cost(self, cosyvoice_adapter, sample_voice_request):
        """测试成本估算"""
        cost = cosyvoice_adapter.estimate_cost(sample_voice_request)
        assert isinstance(cost, Decimal)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_synthesize_voice_success(self, cosyvoice_adapter, sample_voice_request, mock_http_session):
        """测试成功合成语音"""
        # 模拟任务创建响应
        task_response = {
            "output": {
                "task_id": "task_123"
            },
            "request_id": "req_123"
        }
        
        # 模拟任务完成响应
        completed_response = {
            "output": {
                "task_id": "task_123",
                "task_status": "SUCCEEDED",
                "results": [{
                    "audio_url": "https://example.com/audio.wav"
                }]
            },
            "request_id": "req_123"
        }
        
        # 模拟音频下载
        audio_data = b"fake_audio_data"
        
        # 设置mock响应
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=completed_response)
        
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=audio_data)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = [get_response, download_response]
        
        with patch.object(cosyvoice_adapter, 'session', mock_http_session):
            response = await cosyvoice_adapter.generate(sample_voice_request)
        
        assert isinstance(response, VoiceSynthesisResponse)
        assert response.audio_url == "https://example.com/audio.wav"
        assert response.audio_data == base64.b64encode(audio_data).decode()
        assert response.duration > 0
        assert response.format == "wav"
    
    @pytest.mark.asyncio
    async def test_synthesize_voice_task_polling(self, cosyvoice_adapter, sample_voice_request, mock_http_session):
        """测试任务轮询机制"""
        # 模拟任务创建
        task_response = {
            "output": {"task_id": "task_123"},
            "request_id": "req_123"
        }
        
        # 模拟轮询过程：PENDING -> RUNNING -> SUCCEEDED
        poll_responses = [
            {
                "output": {
                    "task_id": "task_123",
                    "task_status": "PENDING"
                }
            },
            {
                "output": {
                    "task_id": "task_123",
                    "task_status": "RUNNING"
                }
            },
            {
                "output": {
                    "task_id": "task_123",
                    "task_status": "SUCCEEDED",
                    "results": [{
                        "audio_url": "https://example.com/audio.wav"
                    }]
                }
            }
        ]
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_responses = []
        for poll_response in poll_responses:
            get_resp = AsyncMock()
            get_resp.status = 200
            get_resp.json = AsyncMock(return_value=poll_response)
            get_responses.append(get_resp)
        
        # 添加音频下载响应
        download_response = AsyncMock()
        download_response.status = 200
        download_response.read = AsyncMock(return_value=b"audio_data")
        get_responses.append(download_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.side_effect = get_responses
        
        with patch.object(cosyvoice_adapter, 'session', mock_http_session):
            with patch('asyncio.sleep', new_callable=AsyncMock):  # 跳过实际等待
                response = await cosyvoice_adapter.generate(sample_voice_request)
        
        assert response.audio_url == "https://example.com/audio.wav"
        # 验证轮询了3次状态
        assert mock_http_session.get.call_count == 4  # 3次状态查询 + 1次下载
    
    @pytest.mark.asyncio
    async def test_synthesize_voice_task_failed(self, cosyvoice_adapter, sample_voice_request, mock_http_session):
        """测试任务失败处理"""
        task_response = {
            "output": {"task_id": "task_123"},
            "request_id": "req_123"
        }
        
        failed_response = {
            "output": {
                "task_id": "task_123",
                "task_status": "FAILED",
                "task_metrics": {
                    "FAILED": 1,
                    "error_message": "Text contains unsupported characters"
                }
            }
        }
        
        post_response = AsyncMock()
        post_response.status = 200
        post_response.json = AsyncMock(return_value=task_response)
        
        get_response = AsyncMock()
        get_response.status = 200
        get_response.json = AsyncMock(return_value=failed_response)
        
        mock_http_session.post.return_value = post_response
        mock_http_session.get.return_value = get_response
        
        with patch.object(cosyvoice_adapter, 'session', mock_http_session):
            with pytest.raises(APIError, match="Text contains unsupported characters"):
                await cosyvoice_adapter.generate(sample_voice_request)
    
    @pytest.mark.asyncio
    async def test_synthesize_voice_api_error(self, cosyvoice_adapter, sample_voice_request, mock_http_session):
        """测试API错误处理"""
        mock_http_session.post.return_value.status = 400
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "code": "InvalidParameter",
            "message": "Invalid voice_id parameter",
            "request_id": "req_123"
        })
        
        with patch.object(cosyvoice_adapter, 'session', mock_http_session):
            with pytest.raises(APIError):
                await cosyvoice_adapter.generate(sample_voice_request)
    
    @pytest.mark.asyncio
    async def test_synthesize_voice_rate_limit(self, cosyvoice_adapter, sample_voice_request, mock_http_session):
        """测试速率限制处理"""
        mock_http_session.post.return_value.status = 429
        mock_http_session.post.return_value.json = AsyncMock(return_value={
            "code": "Throttling.User",
            "message": "Request was denied due to request throttling",
            "request_id": "req_123"
        })
        
        with patch.object(cosyvoice_adapter, 'session', mock_http_session):
            with pytest.raises(RateLimitError):
                await cosyvoice_adapter.generate(sample_voice_request)


class TestEdgeTTSAdapter:
    """测试EdgeTTSAdapter"""
    
    @pytest.fixture
    def edge_tts_adapter(self, voice_adapter_config, mock_config_manager, mock_cost_controller):
        """创建EdgeTTSAdapter实例"""
        config = voice_adapter_config
        config.model = "edge-tts"
        config.api_key = None  # Edge TTS不需要API密钥
        return EdgeTTSAdapter(config, mock_config_manager, mock_cost_controller)
    
    @pytest.mark.asyncio
    async def test_validate_config(self, edge_tts_adapter):
        """测试配置验证"""
        result = await edge_tts_adapter.validate_config()
        assert result is True
    
    def test_get_supported_models(self, edge_tts_adapter):
        """测试获取支持的模型"""
        models = edge_tts_adapter.get_supported_models()
        assert "edge-tts" in models
    
    def test_get_available_voices(self, edge_tts_adapter):
        """测试获取可用语音"""
        voices = edge_tts_adapter.get_available_voices()
        assert len(voices) > 0
        
        # 检查中文语音
        zh_voices = [v for v in voices if v['language'] == 'zh-CN']
        assert len(zh_voices) > 0
        assert any(voice['id'] == 'zh-CN-XiaoxiaoNeural' for voice in zh_voices)
        
        # 检查英文语音
        en_voices = [v for v in voices if v['language'] == 'en-US']
        assert len(en_voices) > 0
        assert any(voice['id'] == 'en-US-AriaNeural' for voice in en_voices)
    
    def test_get_supported_languages(self, edge_tts_adapter):
        """测试获取支持的语言"""
        languages = edge_tts_adapter.get_supported_languages()
        assert "zh-CN" in languages
        assert "en-US" in languages
    
    def test_estimate_cost(self, edge_tts_adapter, sample_voice_request):
        """测试成本估算（免费服务）"""
        cost = edge_tts_adapter.estimate_cost(sample_voice_request)
        assert isinstance(cost, Decimal)
        assert cost == Decimal('0')  # Edge TTS是免费的
    
    @pytest.mark.asyncio
    @patch('edge_tts.Communicate')
    async def test_synthesize_voice_success(self, mock_communicate, edge_tts_adapter, sample_voice_request):
        """测试成功合成语音"""
        # 模拟Edge TTS响应
        audio_data = b"fake_audio_data"
        
        # 模拟Communicate实例
        mock_comm_instance = AsyncMock()
        mock_comm_instance.stream.return_value = [
            {"type": "audio", "data": audio_data[:100]},
            {"type": "audio", "data": audio_data[100:]}
        ]
        mock_communicate.return_value = mock_comm_instance
        
        response = await edge_tts_adapter.generate(sample_voice_request)
        
        assert isinstance(response, VoiceSynthesisResponse)
        assert response.audio_data == base64.b64encode(audio_data).decode()
        assert response.duration > 0
        assert response.format == "mp3"
        assert response.cost == Decimal('0')
    
    @pytest.mark.asyncio
    @patch('edge_tts.Communicate')
    async def test_synthesize_voice_with_ssml(self, mock_communicate, edge_tts_adapter):
        """测试使用SSML的语音合成"""
        request = VoiceSynthesisRequest(
            text="Hello, this is a test.",
            voice_id="en-US-AriaNeural",
            language="en-US",
            gender=VoiceGender.FEMALE,
            style=VoiceStyle.CHEERFUL,
            speed=1.2,
            pitch=1.1,
            volume=0.9
        )
        
        audio_data = b"ssml_audio_data"
        
        mock_comm_instance = AsyncMock()
        mock_comm_instance.stream.return_value = [
            {"type": "audio", "data": audio_data}
        ]
        mock_communicate.return_value = mock_comm_instance
        
        response = await edge_tts_adapter.generate(request)
        
        assert response.audio_data == base64.b64encode(audio_data).decode()
        
        # 验证SSML构建
        call_args = mock_communicate.call_args
        ssml_text = call_args[0][0]
        assert "<speak" in ssml_text
        assert "rate=\"1.2\"" in ssml_text
        assert "pitch=\"+10%\"" in ssml_text
        assert "volume=\"90%\"" in ssml_text
        assert "style=\"cheerful\"" in ssml_text
    
    @pytest.mark.asyncio
    @patch('edge_tts.Communicate')
    async def test_synthesize_voice_error_handling(self, mock_communicate, edge_tts_adapter, sample_voice_request):
        """测试错误处理"""
        # 模拟Edge TTS异常
        mock_communicate.side_effect = Exception("Network error")
        
        with pytest.raises(APIError, match="Network error"):
            await edge_tts_adapter.generate(sample_voice_request)
    
    def test_build_ssml(self, edge_tts_adapter):
        """测试SSML构建"""
        request = VoiceSynthesisRequest(
            text="Hello world",
            voice_id="en-US-AriaNeural",
            language="en-US",
            gender=VoiceGender.FEMALE,
            style=VoiceStyle.FRIENDLY,
            speed=0.8,
            pitch=0.9,
            volume=1.1
        )
        
        ssml = edge_tts_adapter._build_ssml(request)
        
        assert "<speak" in ssml
        assert "xmlns=\"http://www.w3.org/2001/10/synthesis\"" in ssml
        assert "xml:lang=\"en-US\"" in ssml
        assert "<voice name=\"en-US-AriaNeural\">" in ssml
        assert "<mstts:express-as style=\"friendly\">" in ssml
        assert "<prosody rate=\"0.8\" pitch=\"-10%\" volume=\"110%\">" in ssml
        assert "Hello world" in ssml
        assert "</prosody>" in ssml
        assert "</mstts:express-as>" in ssml
        assert "</voice>" in ssml
        assert "</speak>" in ssml


class TestVoiceAdapterIntegration:
    """语音适配器集成测试"""
    
    @pytest.mark.asyncio
    async def test_adapter_retry_mechanism(self, voice_adapter_config, mock_config_manager, mock_cost_controller):
        """测试重试机制"""
        adapter = CosyVoiceAdapter(voice_adapter_config, mock_config_manager, mock_cost_controller)
        
        # 模拟前两次请求失败，第三次成功
        call_count = 0
        async def mock_post(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            response = AsyncMock()
            if call_count < 3:
                response.status = 500
                response.json = AsyncMock(return_value={"error": "Internal server error"})
            else:
                response.status = 200
                response.json = AsyncMock(return_value={
                    "output": {"task_id": "task_123"},
                    "request_id": "req_123"
                })
            return response
        
        # 模拟任务完成和音频下载
        async def mock_get(*args, **kwargs):
            if "task_123" in str(args):
                response = AsyncMock()
                response.status = 200
                response.json = AsyncMock(return_value={
                    "output": {
                        "task_id": "task_123",
                        "task_status": "SUCCEEDED",
                        "results": [{"audio_url": "https://example.com/audio.wav"}]
                    }
                })
                return response
            else:
                # 音频下载
                response = AsyncMock()
                response.status = 200
                response.read = AsyncMock(return_value=b"audio_data")
                return response
        
        with patch.object(adapter, 'session') as mock_session:
            mock_session.post = mock_post
            mock_session.get = mock_get
            
            request = VoiceSynthesisRequest(
                text="Test retry",
                voice_id="zh-CN-XiaoxiaoNeural",
                language="zh-CN",
                gender=VoiceGender.FEMALE
            )
            
            response = await adapter.generate(request)
            
            assert response.audio_url == "https://example.com/audio.wav"
            assert call_count == 3  # 验证重试了3次
    
    @pytest.mark.asyncio
    async def test_cost_tracking(self, voice_adapter_config, mock_config_manager, mock_cost_controller):
        """测试成本跟踪"""
        adapter = CosyVoiceAdapter(voice_adapter_config, mock_config_manager, mock_cost_controller)
        
        task_response = {
            "output": {"task_id": "task_123"},
            "request_id": "req_123"
        }
        
        completed_response = {
            "output": {
                "task_id": "task_123",
                "task_status": "SUCCEEDED",
                "results": [{"audio_url": "https://example.com/audio.wav"}]
            }
        }
        
        with patch.object(adapter, 'session') as mock_session:
            post_response = AsyncMock()
            post_response.status = 200
            post_response.json = AsyncMock(return_value=task_response)
            
            get_response = AsyncMock()
            get_response.status = 200
            get_response.json = AsyncMock(return_value=completed_response)
            
            download_response = AsyncMock()
            download_response.status = 200
            download_response.read = AsyncMock(return_value=b"audio_data")
            
            mock_session.post.return_value = post_response
            mock_session.get.side_effect = [get_response, download_response]
            
            request = VoiceSynthesisRequest(
                text="Test cost tracking",
                voice_id="zh-CN-XiaoxiaoNeural",
                language="zh-CN",
                gender=VoiceGender.FEMALE
            )
            
            await adapter.generate(request)
            
            # 验证成本控制器被调用
            mock_cost_controller.check_cost_limit.assert_called_once()
            mock_cost_controller.record_cost.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, voice_adapter_config, mock_config_manager, mock_cost_controller):
        """测试并发请求处理"""
        adapter = EdgeTTSAdapter(voice_adapter_config, mock_config_manager, mock_cost_controller)
        
        with patch('edge_tts.Communicate') as mock_communicate:
            audio_data = b"concurrent_audio_data"
            
            mock_comm_instance = AsyncMock()
            mock_comm_instance.stream.return_value = [
                {"type": "audio", "data": audio_data}
            ]
            mock_communicate.return_value = mock_comm_instance
            
            requests = [
                VoiceSynthesisRequest(
                    text=f"Concurrent request {i}",
                    voice_id="zh-CN-XiaoxiaoNeural",
                    language="zh-CN",
                    gender=VoiceGender.FEMALE
                )
                for i in range(3)
            ]
            
            # 并发执行请求
            responses = await asyncio.gather(*[
                adapter.generate(request) for request in requests
            ])
            
            assert len(responses) == 3
            for response in responses:
                assert response.audio_data == base64.b64encode(audio_data).decode()
                assert response.cost == Decimal('0')  # Edge TTS免费