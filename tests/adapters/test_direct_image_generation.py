#!/usr/bin/env python3
"""
直接测试图像生成API

绕过工作流引擎，直接测试图像适配器是否能正常生成图像
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

# 使用简化的直接导入
from adapters.image.base_image import ImageGenerationRequest
from core.config import ConfigManager
from core.cost_control import CostController


async def test_google_image_api():
    """直接测试Google图像API"""
    print("=" * 60)
    print("🎨 Google图像API直接测试")
    print("=" * 60)
    
    try:
        import aiohttp
        import base64
        
        # 获取API密钥
        api_key = os.getenv('GOOGLE_AI_API_KEY')
        if not api_key:
            print("❌ 未找到GOOGLE_AI_API_KEY环境变量")
            return False
        
        print(f"✅ API密钥已配置: {api_key[:10]}...{api_key[-4:]}")
        
        # 构建请求
        model_name = "gemini-2.0-flash-preview-image-generation"
        base_url = "https://generativelanguage.googleapis.com/v1beta"
        url = f"{base_url}/models/{model_name}:generateContent"
        
        headers = {
            "x-goog-api-key": api_key,
            "Content-Type": "application/json"
        }
        
        # 构建请求数据
        prompt = "一位明朝皇帝坐在龙椅上，身穿黄色龙袍，威严肃穆，宫殿背景，电影级质量，历史画风"
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": f"请为我生成一张图片：{prompt}"
                }]
            }],
            "generationConfig": {
                "responseModalities": ["TEXT", "IMAGE"]
            }
        }
        
        print(f"🎯 生成提示词: {prompt}")
        print("⏳ 发送API请求...")
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                print(f"📡 响应状态: {response.status}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ API请求失败: {error_text}")
                    return False
                
                data = await response.json()
                print("✅ 成功收到API响应")
                
                # 解析响应
                if 'candidates' in data and data['candidates']:
                    candidate = data['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        parts = candidate['content']['parts']
                        
                        images_found = 0
                        text_found = False
                        
                        for part in parts:
                            if 'inlineData' in part and 'data' in part['inlineData']:
                                # 找到图像数据
                                images_found += 1
                                image_data = part['inlineData']['data']
                                
                                # 保存图像
                                output_dir = Path("./output/test_outputs/direct_api_test")
                                output_dir.mkdir(parents=True, exist_ok=True)
                                
                                image_path = output_dir / f"google_test_{images_found}.png"
                                
                                # 解码base64并保存
                                image_bytes = base64.b64decode(image_data)
                                with open(image_path, 'wb') as f:
                                    f.write(image_bytes)
                                
                                print(f"🖼️  图像 {images_found} 已保存: {image_path}")
                                
                            elif 'text' in part:
                                text_found = True
                                print(f"📝 响应文本: {part['text'][:100]}...")
                        
                        if images_found > 0:
                            print(f"🎉 成功生成 {images_found} 张图像!")
                            print("💰 使用免费Google Gemini 2.0 Flash模型，成本: $0.00")
                            return True
                        else:
                            print("⚠️  API响应中未找到图像数据")
                            print(f"📋 完整响应: {data}")
                            return False
                    else:
                        print("⚠️  响应格式异常，未找到content部分")
                        return False
                else:
                    print("⚠️  响应中未找到candidates")
                    print(f"📋 完整响应: {data}")
                    return False
                    
    except Exception as e:
        print(f"💥 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_google_image_api())
    
    if success:
        print("\n🎉 Google图像API测试成功!")
        print("💡 这说明Google免费图像生成API工作正常")
        print("🔍 如果工作流中未生成图像，问题可能在工作流引擎层面")
    else:
        print("\n💔 Google图像API测试失败")
        print("🔍 请检查API密钥配置和网络连接")