#!/usr/bin/env python3
"""DeepSeek API直接连接测试"""

import asyncio
import aiohttp
import os
import pytest
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class TestDeepSeekAPI:
    """DeepSeek API测试类"""
    
    @pytest.mark.asyncio
    async def test_deepseek_api_connection(self):
        """测试DeepSeek API连接"""
        print('🔄 开始直接测试DeepSeek API...')
        
        api_key = os.getenv('DEEPSEEK_API_KEY')
        if not api_key:
            pytest.skip('DEEPSEEK_API_KEY未配置，跳过测试')
        
        print(f'✅ API密钥已配置: {api_key[:10]}...')
        
        url = "https://api.deepseek.com/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user",
                    "content": "请简要介绍清朝的历史背景，不超过100字。"
                }
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                print(f'HTTP状态码: {response.status}')
                
                assert response.status == 200, f"API调用失败，状态码: {response.status}"
                
                result = await response.json()
                print('✅ DeepSeek API测试成功！')
                print(f'生成内容: {result["choices"][0]["message"]["content"]}')
                print(f'Token使用: {result.get("usage", {})}')
                
                # 验证响应结构
                assert "choices" in result
                assert len(result["choices"]) > 0
                assert "message" in result["choices"][0]
                assert "content" in result["choices"][0]["message"]
                assert "usage" in result

if __name__ == "__main__":
    # 支持直接运行
    async def run_test():
        test_instance = TestDeepSeekAPI()
        await test_instance.test_deepseek_api_connection()
    
    asyncio.run(run_test())