#!/usr/bin/env python3
"""GLM API直接连接测试"""

import asyncio
import aiohttp
import os
import pytest
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class TestGLMAPI:
    """GLM API测试类"""
    
    @pytest.mark.asyncio
    async def test_glm_api_connection(self):
        """测试GLM API连接"""
        print('🔄 开始直接测试GLM API...')
        
        api_key = os.getenv('GLM_API_KEY')
        if not api_key:
            pytest.skip('GLM_API_KEY未配置，跳过测试')
        
        print(f'✅ API密钥已配置: {api_key[:10]}...')
        
        url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "glm-4-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请简要介绍明朝的历史背景，不超过100字。"
                }
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                print(f'HTTP状态码: {response.status}')
                
                assert response.status == 200, f"API调用失败，状态码: {response.status}"
                
                result = await response.json()
                print('✅ GLM API测试成功！')
                print(f'生成内容: {result["choices"][0]["message"]["content"]}')
                print(f'Token使用: {result.get("usage", {})}')
                
                # 验证响应结构
                assert "choices" in result
                assert len(result["choices"]) > 0
                assert "message" in result["choices"][0]
                assert "content" in result["choices"][0]["message"]
                assert "usage" in result

if __name__ == "__main__":
    # 支持直接运行
    async def run_test():
        test_instance = TestGLMAPI()
        await test_instance.test_glm_api_connection()
    
    asyncio.run(run_test())