#!/usr/bin/env python3
"""验证Google API配置

简单验证Google API密钥是否配置正确。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def verify_config():
    """验证Google API配置"""
    print("🔍 验证Google API配置...")
    
    # 1. 检查环境变量
    google_key = os.getenv('GOOGLE_AI_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    print(f"GOOGLE_AI_API_KEY: {'✅ 已设置' if google_key else '❌ 未设置'}")
    print(f"GEMINI_API_KEY: {'✅ 已设置' if gemini_key else '❌ 未设置'}")
    
    if google_key:
        print(f"密钥预览: {google_key[:8]}...{google_key[-4:]}")
    elif gemini_key:
        print(f"密钥预览: {gemini_key[:8]}...{gemini_key[-4:]}")
    
    # 2. 检查配置管理器
    try:
        from core.config import ConfigManager
        config_manager = ConfigManager()
        
        google_api_key = config_manager.get_api_key('google')
        print(f"配置管理器读取: {'✅ 成功' if google_api_key else '❌ 失败'}")
        
        if google_api_key:
            print(f"读取到的密钥: {google_api_key[:8]}...{google_api_key[-4:]}")
            
    except Exception as e:
        print(f"❌ 配置管理器错误: {e}")
        return False
    
    # 3. 检查适配器导入
    try:
        # 避免循环导入，只检查模块是否存在
        import importlib.util
        spec = importlib.util.find_spec("adapters.image.google_adapter")
        if spec is not None:
            print("✅ Google适配器模块存在")
        else:
            print("❌ Google适配器模块不存在")
            return False
    except Exception as e:
        print(f"❌ 适配器导入错误: {e}")
        return False
    
    # 4. 简单API连接测试
    if google_key or gemini_key:
        print("\n🌐 测试API连接...")
        try:
            import aiohttp
            import asyncio
            
            async def test_api():
                api_key = google_key or gemini_key
                url = "https://generativelanguage.googleapis.com/v1beta/models"
                headers = {"x-goog-api-key": api_key}
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, headers=headers) as response:
                        if response.status == 200:
                            print("✅ API连接成功")
                            return True
                        else:
                            print(f"❌ API连接失败: {response.status}")
                            error_text = await response.text()
                            print(f"错误详情: {error_text[:200]}")
                            return False
            
            # 运行异步测试
            result = asyncio.run(test_api())
            if not result:
                return False
                
        except Exception as e:
            print(f"❌ API测试错误: {e}")
            return False
    
    print("\n🎉 配置验证完成！")
    return True

def show_next_steps():
    """显示后续步骤"""
    print("""
📋 后续步骤:

1. 🎨 测试图像生成:
   python -m producer.cli produce --title "测试图像" --era "现代" --duration 1

2. 📁 检查输出:
   ls output/

3. 🔧 调整配置 (如需要):
   编辑 .env 文件中的 GOOGLE_AI_API_KEY

4. 📖 查看文档:
   cat docs/Google图像生成快速入门.md
   
5. 🚀 开始制作:
   python -m producer.cli produce --title "明朝风云" --era "明朝" --duration 5
""")

if __name__ == "__main__":
    success = verify_config()
    
    if success:
        show_next_steps()
        sys.exit(0)
    else:
        print("\n❌ 配置验证失败，请检查上述错误")
        print("\n💡 解决建议:")
        print("1. 确保已在 .env 文件中设置 GOOGLE_AI_API_KEY")
        print("2. 检查API密钥是否有效")
        print("3. 确保网络连接正常")
        sys.exit(1)