#!/usr/bin/env python3
"""EdgeTTS免费语音合成测试

测试EdgeTTS免费语音合成功能。
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from adapters.voice.edge_tts_adapter import EdgeTTSAdapter
from adapters.voice.base_voice import VoiceSynthesisRequest, VoiceGender, VoiceStyle
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController


async def test_edgetts_voice_synthesis():
    """测试EdgeTTS语音合成功能"""
    
    print("🚀 开始测试 EdgeTTS 免费语音合成...")
    
    try:
        # 初始化配置
        config_manager = ConfigManager()
        cost_controller = CostController(config_manager)
        
        # 创建EdgeTTS适配器配置
        config = AdapterConfig(
            service_name="edgetts",
            api_key="free",  # EdgeTTS不需要API密钥
            model="zh-CN-XiaoxiaoNeural",  # 中文女声
            max_retries=3
        )
        
        # 创建适配器
        adapter = EdgeTTSAdapter(config, config_manager, cost_controller)
        
        print("🔧 验证适配器配置...")
        is_valid = await adapter.validate_config()
        print(f"配置验证: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        # 获取可用语音
        voices = adapter.get_available_voices()
        print(f"📢 可用语音数量: {len(voices)}")
        
        # 测试语音合成
        test_texts = [
            "欢迎使用EdgeTTS免费语音合成服务。",
            "在明朝嘉靖年间，江南水乡烟雨朦胧，一位书生正在苦读诗书。",
            "Hello, this is a test of EdgeTTS English voice synthesis."
        ]
        
        success_count = 0
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🎤 测试 {i}: {text}")
            
            # 选择合适的语音
            if "Hello" in text:
                voice_id = "en-US-JennyNeural"  # 英文女声
                language = "en-US"
            else:
                voice_id = "zh-CN-XiaoxiaoNeural"  # 中文女声
                language = "zh-CN"
            
            try:
                # 创建语音合成请求
                request = VoiceSynthesisRequest(
                    text=text,
                    voice_id=voice_id,
                    language=language,
                    gender=VoiceGender.FEMALE,
                    style=VoiceStyle.NEUTRAL,
                    speed=1.0,
                    pitch=1.0,
                    volume=1.0,
                    output_format="wav"
                )
                
                # 估算成本
                estimated_cost = adapter.estimate_cost(request)
                print(f"估算成本: ${estimated_cost:.4f} (EdgeTTS完全免费)")
                
                # 生成语音
                print("正在生成语音...")
                result = await adapter.generate(request)
                
                if result.success:
                    response = result.data
                    print(f"✅ 生成成功!")
                    print(f"   语音时长: {response.duration:.2f}秒")
                    print(f"   文件格式: {response.format}")
                    print(f"   语言: {response.language}")
                    print(f"   实际成本: $0.00 (完全免费)")
                    
                    # 保存音频文件
                    output_dir = Path("output/test_outputs/edgetts_test")
                    output_dir.mkdir(parents=True, exist_ok=True)
                    
                    if response.audio_data:
                        audio_file = output_dir / f"test_{i}_{voice_id}.wav"
                        with open(audio_file, "wb") as f:
                            f.write(response.audio_data)
                        print(f"   音频已保存: {audio_file}")
                    
                    success_count += 1
                    
                else:
                    print(f"❌ 生成失败: {result.error_message}")
                    
            except Exception as e:
                print(f"❌ 测试失败: {str(e)}")
            
            # 添加延时
            await asyncio.sleep(1)
        
        # 输出结果
        print(f"\n📊 测试结果:")
        print(f"总测试数: {len(test_texts)}")
        print(f"成功数: {success_count}")
        print(f"成功率: {success_count/len(test_texts)*100:.1f}%")
        print(f"总成本: $0.00 (EdgeTTS完全免费)")
        
        if success_count == len(test_texts):
            print("🎉 所有测试通过！EdgeTTS免费语音合成配置正确。")
            return True
        elif success_count > 0:
            print("⚠️ 部分测试通过，请检查失败的测试。")
            return True
        else:
            print("❌ 所有测试失败，请检查网络连接。")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False


async def test_voice_models():
    """测试不同语音模型"""
    print("\n🔧 测试不同语音模型...")
    
    try:
        config_manager = ConfigManager()
        cost_controller = CostController(config_manager)
        
        # 测试多种语音
        test_voices = [
            ("zh-CN-XiaoxiaoNeural", "晓晓", "zh-CN"),
            ("zh-CN-YunxiNeural", "云希", "zh-CN"), 
            ("en-US-JennyNeural", "Jenny", "en-US"),
            ("en-US-GuyNeural", "Guy", "en-US")
        ]
        
        for voice_id, voice_name, language in test_voices:
            config = AdapterConfig(
                service_name="edgetts",
                api_key="free",
                model=voice_id,
                max_retries=3
            )
            
            adapter = EdgeTTSAdapter(config, config_manager, cost_controller)
            
            text = "这是语音测试。" if language == "zh-CN" else "This is a voice test."
            
            request = VoiceSynthesisRequest(
                text=text,
                voice_id=voice_id,
                language=language,
                gender=VoiceGender.FEMALE if "xiaoxiao" in voice_id.lower() or "jenny" in voice_id.lower() else VoiceGender.MALE,
                style=VoiceStyle.NEUTRAL
            )
            
            result = await adapter.generate(request)
            
            if result.success:
                print(f"✅ {voice_name} ({voice_id}): 生成成功")
            else:
                print(f"❌ {voice_name} ({voice_id}): {result.error_message}")
            
            await asyncio.sleep(0.5)
        
    except Exception as e:
        print(f"❌ 语音模型测试失败: {str(e)}")


def show_usage_guide():
    """显示使用指南"""
    print("""
📋 EdgeTTS 免费语音合成使用指南

✨ 优势特点:
- 🆓 完全免费，无需API密钥
- 🌍 支持多种语言 (中文、英文等)
- 🎭 多种语音风格和角色
- 🚀 响应速度快
- 📱 易于集成

🎤 支持的中文语音:
- zh-CN-XiaoxiaoNeural: 晓晓 (女声，温和)
- zh-CN-YunxiNeural: 云希 (男声，稳重)
- zh-CN-YunyeNeural: 云野 (男声，活力)
- zh-CN-XiaochenNeural: 晓辰 (女声，甜美)

🌍 支持的英文语音:
- en-US-JennyNeural: Jenny (女声，专业)
- en-US-GuyNeural: Guy (男声，清晰)
- en-US-AriaNeural: Aria (女声，友好)

💡 使用示例:
```python
from adapters.voice.edge_tts_adapter import EdgeTTSAdapter

# 创建适配器（无需API密钥）
config = AdapterConfig(
    service_name="edgetts",
    api_key="free",
    model="zh-CN-XiaoxiaoNeural"
)
adapter = EdgeTTSAdapter(config, config_manager, cost_controller)

# 合成语音
request = VoiceSynthesisRequest(
    text="欢迎使用免费语音合成服务",
    voice_id="zh-CN-XiaoxiaoNeural",
    language="zh-CN"
)
result = await adapter.generate(request)
```

🎯 适用场景:
- 历史短剧旁白配音
- 多语言内容制作  
- 快速原型开发
- 成本敏感项目
- 批量语音生成

⚡ 系统要求:
- 稳定的网络连接
- Python 3.11+
- edge-tts 依赖包

💰 成本对比:
- EdgeTTS: $0.00 (完全免费)
- CosyVoice: ~¥0.002/10字符
- ElevenLabs: ~$0.30/1K字符
    """)


async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "guide":
        show_usage_guide()
        return
    
    print("🤖 EdgeTTS 免费语音合成测试")
    print("=" * 50)
    
    # 主测试
    success = await test_edgetts_voice_synthesis()
    
    if success:
        # 测试不同语音模型
        await test_voice_models()
        
        print(f"\n✅ 所有测试通过!")
        print(f"📁 生成的音频保存在: output/test_outputs/edgetts_test/")
        print(f"💰 总成本: $0.00 (EdgeTTS完全免费)")
        print(f"🚀 现在可以开始制作历史短剧了!")
    else:
        print(f"\n❌ 测试失败")
        show_usage_guide()


if __name__ == "__main__":
    asyncio.run(main())