import os
from dotenv import load_dotenv

# 手动加载.env文件
load_dotenv()

# 检查环境变量
print('GLM_API_KEY:', os.getenv('GLM_API_KEY', 'Not found')[:20] + '...' if os.getenv('GLM_API_KEY') else 'Not found')
print('GOOGLE_AI_API_KEY:', os.getenv('GOOGLE_AI_API_KEY', 'Not found')[:20] + '...' if os.getenv('GOOGLE_AI_API_KEY') else 'Not found')
print('DEEPSEEK_API_KEY:', os.getenv('DEEPSEEK_API_KEY', 'Not found')[:20] + '...' if os.getenv('DEEPSEEK_API_KEY') else 'Not found')

# 测试ConfigManager
try:
    from core.config import ConfigManager
    cm = ConfigManager()
    print('ConfigManager创建成功')
    
    # 测试获取输出目录
    output_dir = cm.get_output_dir()
    print(f'输出目录: {output_dir}')
    
    print('环境变量和配置加载正常')
except Exception as e:
    print(f'配置加载错误: {e}')