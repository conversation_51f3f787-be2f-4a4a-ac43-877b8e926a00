#!/usr/bin/env python3
"""简单的Google图像生成API测试

避免复杂依赖，直接测试Google API。
"""

import asyncio
import aiohttp
import os
import base64
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

async def test_google_image_generation():
    """测试Google图像生成API"""
    
    # 获取API密钥
    api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 请设置 GOOGLE_AI_API_KEY 环境变量")
        return False
    
    print("🚀 开始测试Google图像生成...")
    print(f"API Key: {api_key[:8]}...{api_key[-4:]}")
    
    # API配置
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent"
    headers = {
        "x-goog-api-key": api_key,
        "Content-Type": "application/json"
    }
    
    # 测试提示词
    test_prompts = [
        "A simple red circle on white background",
        "古代中国山水画风格的风景，有山有水",
        "A cute robot reading a book"
    ]
    
    success_count = 0
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n🎨 测试 {i}: {prompt}")
        
        # 构建请求
        payload = {
            "contents": [{
                "parts": [{
                    "text": f"Generate an image: {prompt}"
                }]
            }],
            "generationConfig": {
                "responseModalities": ["TEXT", "IMAGE"]
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # 解析响应
                        candidates = data.get("candidates", [])
                        if candidates:
                            content = candidates[0].get("content", {})
                            parts = content.get("parts", [])
                            
                            images_found = 0
                            for part in parts:
                                if "inlineData" in part:
                                    # 保存图像
                                    inline_data = part["inlineData"]
                                    image_data = base64.b64decode(inline_data["data"])
                                    
                                    # 创建输出目录
                                    output_dir = Path("output/test_outputs/simple_google_test")
                                    output_dir.mkdir(parents=True, exist_ok=True)
                                    
                                    # 保存文件
                                    filename = f"test_{i}_{images_found + 1}.png"
                                    file_path = output_dir / filename
                                    
                                    with open(file_path, "wb") as f:
                                        f.write(image_data)
                                    
                                    print(f"✅ 图像已保存: {file_path}")
                                    images_found += 1
                            
                            if images_found > 0:
                                success_count += 1
                                print(f"✅ 测试 {i} 成功，生成了 {images_found} 张图片")
                            else:
                                print(f"❌ 测试 {i} 失败：响应中没有图像")
                        else:
                            print(f"❌ 测试 {i} 失败：响应中没有candidates")
                    else:
                        error_text = await response.text()
                        print(f"❌ 测试 {i} 失败: HTTP {response.status}")
                        print(f"错误详情: {error_text[:200]}")
                        
        except Exception as e:
            print(f"❌ 测试 {i} 异常: {str(e)}")
        
        # 添加延时避免限制
        await asyncio.sleep(2)
    
    # 输出结果
    print(f"\n📊 测试结果:")
    print(f"总测试数: {len(test_prompts)}")
    print(f"成功数: {success_count}")
    print(f"成功率: {success_count/len(test_prompts)*100:.1f}%")
    
    if success_count == len(test_prompts):
        print("🎉 所有测试通过！Google图像生成配置正确。")
        return True
    elif success_count > 0:
        print("⚠️ 部分测试通过，请检查失败的测试。")
        return True
    else:
        print("❌ 所有测试失败，请检查API密钥和网络连接。")
        return False

async def main():
    """主函数"""
    print("🤖 Google图像生成简单测试")
    print("=" * 50)
    
    success = await test_google_image_generation()
    
    if success:
        print(f"\n📁 生成的图片保存在: output/test_outputs/simple_google_test/")
        print(f"💡 提示: 现在您可以使用 Google 免费图像生成功能了！")
        print(f"🚀 开始制作历史短剧: python -m producer.cli produce --title '明朝风云' --era '明朝'")
    else:
        print(f"\n💡 如果遇到问题，请检查：")
        print(f"1. API密钥是否正确")
        print(f"2. 网络连接是否正常")
        print(f"3. Google AI Studio是否已启用图像生成功能")

if __name__ == "__main__":
    asyncio.run(main())