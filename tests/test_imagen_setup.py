#!/usr/bin/env python3
"""
Google Imagen 配置测试脚本
用于验证 Google AI API 配置和 Imagen 模型功能
"""

import os
import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adapters.image.google_adapter import GoogleImageAdapter
from adapters.base import AdapterConfig
from core.config import ConfigManager
from core.cost_control import CostController

async def test_imagen_config():
    """测试 Google Imagen 配置"""
    print("🔍 开始测试 Google Imagen 配置...")
    
    # 1. 检查环境变量
    api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 错误：未找到 Google AI API 密钥")
        print("请设置环境变量：")
        print("  export GOOGLE_AI_API_KEY='your_api_key_here'")
        print("或在 .env 文件中配置：")
        print("  GOOGLE_AI_API_KEY=your_api_key_here")
        return False
    
    print(f"✅ API 密钥已配置（前8位：{api_key[:8]}...）")
    
    try:
        # 2. 初始化配置管理器
        config_manager = ConfigManager()
        cost_controller = CostController(config_manager)
        
        print("✅ 配置管理器初始化成功")
        
        # 3. 测试免费模型
        free_models = [
            "gemini-2.0-flash-exp",
            "gemini-2.0-flash",
            "gemini-2.0-flash-preview-image-generation"
        ]
        
        for model_name in free_models:
            print(f"\n🧪 测试模型：{model_name}")
            
            # 配置适配器
            adapter_config = AdapterConfig(
                service_type="image",
                model=model_name,
                max_retries=2,
                timeout=30
            )
            
            # 创建适配器
            google_adapter = GoogleImageAdapter(
                config=adapter_config,
                config_manager=config_manager,
                cost_controller=cost_controller
            )
            
            # 测试图像生成
            test_prompt = "A serene Chinese garden with traditional pavilion, watercolor painting style"
            
            print(f"📝 测试提示词：{test_prompt}")
            
            result = await google_adapter.generate(
                prompt=test_prompt,
                num_images=1,
                aspect_ratio="1:1"
            )
            
            if result.success:
                print(f"✅ 模型 {model_name} 测试成功！")
                print(f"   生成时间：{result.data.generation_time:.2f}秒")
                print(f"   输出路径：{result.data.output_path}")
                print(f"   成本：${result.data.cost_usd:.4f}")
                
                # 只测试一个成功的模型即可
                break
            else:
                print(f"❌ 模型 {model_name} 测试失败：{result.error_message}")
                continue
        
        # 4. 显示可用模型信息
        print(f"\n📊 支持的模型列表：")
        for model, info in GoogleImageAdapter.SUPPORTED_MODELS.items():
            price_info = "免费" if info["free_tier"] else f"${info['price_per_image']}/张"
            print(f"  • {model}: {info['name']} ({price_info})")
        
        print(f"\n🎉 测试完成！Google Imagen 配置正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        return False

def print_configuration_guide():
    """打印配置指南"""
    print("\n" + "="*60)
    print("📋 Google Imagen 配置指南")
    print("="*60)
    print("""
1. 获取 API 密钥：
   访问 https://aistudio.google.com/
   登录并创建 API 密钥

2. 配置环境变量：
   创建 .env 文件：
   GOOGLE_AI_API_KEY=your_api_key_here

3. 配置系统设置：
   编辑 config/config.yaml：
   image_generation:
     primary_service: "google"
     google:
       model: "gemini-2.0-flash-exp"  # 推荐免费模型

4. 验证配置：
   python test_imagen_setup.py

5. 在生产中使用：
   uv run python -m producer.cli produce \\
     --title "测试视频" \\
     --theme "历史" \\
     --era "唐朝" \\
     --duration 60
""")
    print("="*60)

async def main():
    """主函数"""
    print("🚀 Google Imagen 配置测试工具")
    print_configuration_guide()
    
    success = await test_imagen_config()
    
    if success:
        print("\n🎊 恭喜！您的 Google Imagen 配置已就绪。")
        print("现在可以在 Producer 系统中使用 Google 图像生成功能了。")
    else:
        print("\n🔧 请根据上述指南完成配置后重新运行测试。")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期错误：{e}")
        sys.exit(1)