#!/usr/bin/env python3
"""
测试Google图像和视频适配器
"""

import asyncio
import sys
import os
sys.path.append('.')

from core.config import ConfigManager
from core.cost_control import CostController
from adapters.base import AdapterConfig
from adapters.image.google_adapter import GoogleImageAdapter
from adapters.video.google_adapter import GoogleVideoAdapter
from adapters.video.base_video import VideoGenerationRequest

async def test_google_image():
    """测试Google图像生成"""
    print('🔍 测试Google图像生成适配器...')
    
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    google_config = AdapterConfig(
        service_name='google',
        api_key=config_manager.get_api_key('google'),
        model='imagen-4.0-generate-preview-06-06',
        max_retries=3
    )
    
    adapter = GoogleImageAdapter(google_config, config_manager, cost_controller)
    
    try:
        result = await adapter.generate('唐朝皇宫大殿，金碧辉煌，庄严肃穆')
        
        if result.success:
            print('✅ Google图像生成成功!')
            print(f'💰 成本: ${result.cost_usd:.4f}')
            print(f'⏱️ 耗时: {result.duration_seconds:.2f}秒')
            if result.data and hasattr(result.data, 'image_url') and result.data.image_url:
                print(f'📁 图像URL: {result.data.image_url}')
            elif result.data and hasattr(result.data, 'image_data') and result.data.image_data:
                print(f'📁 图像数据长度: {len(result.data.image_data)} bytes')
        else:
            print(f'❌ Google图像生成失败: {result.error_message}')
    except Exception as e:
        print(f'❌ 图像测试异常: {e}')

async def test_google_video():
    """测试Google视频生成"""
    print('\n🎬 测试Google视频生成适配器...')
    
    config_manager = ConfigManager()
    cost_controller = CostController(config_manager)
    
    google_config = AdapterConfig(
        service_name='google',
        api_key=config_manager.get_api_key('google'),
        model='veo-2',
        max_retries=3
    )
    
    adapter = GoogleVideoAdapter(google_config, config_manager, cost_controller)
    
    try:
        # 创建视频生成请求
        request = VideoGenerationRequest(
            prompt='唐朝皇宫大殿，大臣们正在朝拜皇帝',
            duration=5.0,
            width=1280,
            height=720
        )
        
        result = await adapter.generate(request)
        
        if result:
            print('✅ Google视频生成成功!')
            print(f'💰 成本: ${adapter.estimate_cost(request):.4f}')
            print(f'⏱️ 耗时: {getattr(result, "generation_time", 0):.2f}秒')
            if hasattr(result, 'video_url') and result.video_url:
                print(f'📁 视频URL: {result.video_url}')
            elif hasattr(result, 'video_data') and result.video_data:
                print(f'📁 视频数据长度: {len(result.video_data)} bytes')
        else:
            print('❌ Google视频生成失败: 返回结果为空')
    except Exception as e:
        print(f'❌ 视频测试异常: {e}')

async def main():
    """主测试函数"""
    print('🚀 开始测试Google适配器...')
    
    # 测试图像生成
    await test_google_image()
    
    # 测试视频生成
    await test_google_video()
    
    print('\n✅ 测试完成!')

if __name__ == '__main__':
    asyncio.run(main())