"""pytest配置文件

提供测试夹具和配置。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from core.config import ConfigManager
from core.cost_control import CostController
from adapters.base import AdapterConfig


@pytest.fixture
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_config_manager():
    """模拟配置管理器"""
    config_manager = Mock(spec=ConfigManager)
    config_manager.get_api_key = Mock(return_value="test_api_key")
    config_manager.get_config = Mock(return_value={})
    return config_manager


@pytest.fixture
def mock_cost_controller():
    """模拟成本控制器"""
    cost_controller = Mock(spec=CostController)
    cost_controller.check_cost_limit = AsyncMock(return_value=True)
    cost_controller.record_cost = AsyncMock()
    return cost_controller


@pytest.fixture
def text_adapter_config():
    """文本适配器配置"""
    return AdapterConfig(
        service_name="test_text_service",
        model="gpt-4o-mini",
        api_key="test_api_key",
        base_url="https://api.openai.com/v1",
        max_retries=3,
        timeout=30.0,
        rate_limit={
            "requests_per_minute": 60,
            "tokens_per_minute": 10000
        },
        extra_params={
            "temperature": 0.7,
            "max_tokens": 1000
        }
    )


@pytest.fixture
def image_adapter_config():
    """图像适配器配置"""
    return AdapterConfig(
        service_name="test_image_service",
        model="flux-pro",
        api_key="test_api_key",
        base_url="https://api.bfl.ml",
        max_retries=3,
        timeout=60.0,
        rate_limit={
            "requests_per_minute": 30
        },
        extra_params={
            "width": 1024,
            "height": 1024,
            "steps": 50
        }
    )


@pytest.fixture
def voice_adapter_config():
    """语音适配器配置"""
    return AdapterConfig(
        service_name="test_voice_service",
        model="cosyvoice-v1",
        api_key="test_api_key",
        base_url="https://dashscope.aliyuncs.com",
        max_retries=3,
        timeout=120.0,
        rate_limit={
            "requests_per_minute": 20
        },
        extra_params={
            "voice_id": "zh-CN-XiaoxiaoNeural",
            "speed": 1.0,
            "pitch": 1.0,
            "volume": 1.0
        }
    )


@pytest.fixture
def video_adapter_config():
    """视频适配器配置"""
    return AdapterConfig(
        service_name="test_video_service",
        model="kling-v1",
        api_key="test_api_key",
        base_url="https://api.kuaishou.com/ai/kling",
        max_retries=3,
        timeout=300.0,
        rate_limit={
            "requests_per_minute": 10
        },
        extra_params={
            "max_duration": 10.0,
            "max_resolution": "1280x720",
            "poll_interval": 3.0
        }
    )


@pytest.fixture
def mock_http_session():
    """模拟HTTP会话"""
    session = AsyncMock()
    
    # 模拟成功响应
    response = AsyncMock()
    response.status = 200
    response.json = AsyncMock(return_value={"success": True})
    response.read = AsyncMock(return_value=b"test_data")
    
    session.post = AsyncMock(return_value=response)
    session.get = AsyncMock(return_value=response)
    
    return session


@pytest.fixture
def sample_text_request():
    """示例文本生成请求"""
    from producer.adapters.text.base_text import TextGenerationRequest
    
    return TextGenerationRequest(
        prompt="Generate a creative story about AI",
        max_tokens=500,
        temperature=0.7,
        top_p=0.9,
        model="gpt-4o-mini"
    )


@pytest.fixture
def sample_image_request():
    """示例图像生成请求"""
    from producer.adapters.image.base_image import ImageGenerationRequest
    
    return ImageGenerationRequest(
        prompt="A beautiful sunset over mountains",
        width=1024,
        height=1024,
        quality=95,
        style="realistic"
    )


@pytest.fixture
def sample_voice_request():
    """示例语音合成请求"""
    from producer.adapters.voice.base_voice import VoiceSynthesisRequest, VoiceGender, VoiceStyle
    
    return VoiceSynthesisRequest(
        text="Hello, this is a test of voice synthesis.",
        voice_id="zh-CN-XiaoxiaoNeural",
        language="zh-CN",
        gender=VoiceGender.FEMALE,
        style=VoiceStyle.NEUTRAL,
        speed=1.0,
        pitch=1.0,
        volume=1.0
    )


@pytest.fixture
def sample_video_request():
    """示例视频生成请求"""
    from producer.adapters.video.base_video import VideoGenerationRequest, VideoQuality, VideoStyle
    
    return VideoGenerationRequest(
        prompt="A cat playing in a garden",
        duration=5.0,
        width=1280,
        height=720,
        quality=VideoQuality.HIGH,
        style=VideoStyle.REALISTIC,
        fps=24,
        model="kling-v1"
    )


@pytest.fixture
def mock_successful_response():
    """模拟成功的API响应"""
    return {
        "id": "test_task_123",
        "status": "completed",
        "result": {
            "url": "https://example.com/result.jpg",
            "width": 1024,
            "height": 1024
        },
        "created_at": "2024-01-01T00:00:00Z",
        "completed_at": "2024-01-01T00:01:00Z"
    }


@pytest.fixture
def mock_error_response():
    """模拟错误的API响应"""
    return {
        "error": {
            "code": "invalid_request",
            "message": "Invalid prompt provided"
        }
    }


class MockAdapter:
    """模拟适配器基类"""
    
    def __init__(self, config, config_manager, cost_controller):
        self.config = config
        self.config_manager = config_manager
        self.cost_controller = cost_controller
        self.logger = Mock()
        self.session = AsyncMock()
        self.stats = {}
    
    async def _update_stats(self, key, value=None):
        if value is None:
            self.stats[key] = self.stats.get(key, 0) + 1
        else:
            self.stats[key] = value
    
    async def validate_config(self):
        return True


@pytest.fixture
def mock_adapter(text_adapter_config, mock_config_manager, mock_cost_controller):
    """模拟适配器实例"""
    return MockAdapter(text_adapter_config, mock_config_manager, mock_cost_controller)