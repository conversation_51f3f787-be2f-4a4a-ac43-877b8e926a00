# 测试指南

本目录包含了Producer项目的所有测试文件，按功能分类组织。

## 目录结构

```
tests/
├── adapters/           # 适配器单元测试
├── api_tests/          # API连接测试  
├── integration/        # 集成测试
├── conftest.py         # pytest配置和fixtures
└── README.md          # 本文件
```

## 测试分类

### 1. API连接测试 (`api_tests/`)

测试各种AI服务API的基础连接功能：

- `test_glm_api.py` - GLM文本生成API测试
- `test_deepseek_api.py` - DeepSeek文本生成API测试  
- `test_flux_api.py` - Flux图像生成API测试

### 2. 集成测试 (`integration/`)

测试跨模块的完整工作流：

- `test_script_generation.py` - 剧本生成工作流测试

### 3. 适配器测试 (`adapters/`)

各个适配器的单元测试（已存在）。

## 运行测试

### 运行所有测试
```bash
cd /Volumes/mini_matrix/github/producer
uv run pytest tests/
```

### 按分类运行测试

```bash
# 运行API连接测试
uv run pytest tests/api_tests/ -v

# 运行集成测试  
uv run pytest tests/integration/ -v

# 运行适配器测试
uv run pytest tests/adapters/ -v
```

### 运行特定测试

```bash
# 运行GLM API测试
uv run pytest tests/api_tests/test_glm_api.py -v

# 运行剧本生成测试
uv run pytest tests/integration/test_script_generation.py -v
```

### 按标记运行测试

```bash
# 运行集成测试标记的测试
uv run pytest -m integration -v

# 运行异步测试
uv run pytest -m asyncio -v
```

### 直接运行单个测试文件

```bash
# 可以直接运行单个测试文件
uv run python tests/api_tests/test_glm_api.py
uv run python tests/integration/test_script_generation.py
```

## 测试前准备

确保已正确配置 `.env` 文件中的API密钥：

```bash
# 检查环境变量配置
cat .env | grep -E "(GLM_API_KEY|DEEPSEEK_API_KEY|FLUX_API_KEY)"
```

## 测试输出示例

### 成功的API测试
```
✅ API密钥已配置: eb84206f86...
HTTP状态码: 200
✅ GLM API测试成功！
生成内容: 明朝（1368-1644年），由朱元璋建立...
Token使用: {'completion_tokens': 49, 'prompt_tokens': 16, 'total_tokens': 65}
```

### 跳过的测试
```
SKIPPED [1] tests/api_tests/test_flux_api.py:54: API密钥无效或服务不可用
```

## 故障排除

1. **API密钥未配置**：确保 `.env` 文件中有正确的API密钥
2. **网络连接问题**：检查网络连接和代理设置
3. **API配额不足**：检查各服务的使用配额
4. **模型版本问题**：某些API可能需要特定的模型版本ID

## 注意事项

- API测试会消耗真实的API配额
- 建议在开发环境中设置较低的预算限制
- 集成测试可能需要较长时间完成
- 某些测试可能因为API服务商的变化而需要调整