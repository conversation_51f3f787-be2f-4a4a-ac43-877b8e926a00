#!/usr/bin/env python3
"""
日志系统测试脚本
"""

import asyncio
import time
import random
from infra.logger import get_logger, PerformanceLogger, contextualize, clear_contextualize

# 获取日志记录器
logger = get_logger("test")
perf_logger = PerformanceLogger()

def test_basic_logging():
    """测试基本日志记录功能"""
    logger.info("开始基本日志记录测试")
    
    # 测试不同级别的日志
    logger.debug("这是一条调试信息")
    logger.info("这是一条信息")
    logger.warning("这是一条警告")
    logger.error("这是一条错误")
    logger.critical("这是一条严重错误")
    
    # 测试结构化日志
    user_data = {
        "user_id": "12345",
        "username": "testuser",
        "email": "<EMAIL>"
    }
    logger.info("用户登录", user_data)
    
    logger.info("基本日志记录测试完成")

def test_performance_logging():
    """测试性能日志记录功能"""
    logger.info("开始性能日志记录测试")
    
    # 测试上下文管理器
    with perf_logger.measure("sleep_operation"):
        time.sleep(0.5)
    
    # 测试手动记录
    start_time = time.time()
    time.sleep(0.3)
    end_time = time.time()
    perf_logger.record("manual_operation", end_time - start_time)
    
    # 测试多个操作
    operations = ["op1", "op2", "op3"]
    for op in operations:
        with perf_logger.measure(op):
            time.sleep(random.uniform(0.1, 0.3))
    
    logger.info("性能日志记录测试完成")

def test_exception_logging():
    """测试异常日志记录功能"""
    logger.info("开始异常日志记录测试")
    
    try:
        # 模拟一个异常
        result = 1 / 0
    except Exception as e:
        logger.exception("捕获到异常", {"operation": "division_by_zero"})
    
    try:
        # 模拟另一个异常
        raise ValueError("这是一个测试异常")
    except Exception as e:
        logger.exception("捕获到另一个异常", {"operation": "value_error_test"})
    
    logger.info("异常日志记录测试完成")

async def test_async_logging():
    """测试异步日志记录功能"""
    logger.info("开始异步日志记录测试")
    
    async def async_task(task_id):
        logger.info(f"开始异步任务 {task_id}")
        with perf_logger.measure(f"async_task_{task_id}"):
            await asyncio.sleep(random.uniform(0.1, 0.5))
        logger.info(f"完成异步任务 {task_id}")
        return f"Task {task_id} result"
    
    # 创建多个异步任务
    tasks = [async_task(i) for i in range(1, 6)]
    results = await asyncio.gather(*tasks)
    
    logger.info("异步日志记录测试完成", {"results": results})

def test_contextual_logging():
    """测试上下文日志记录功能"""
    logger.info("开始上下文日志记录测试")
    
    # 设置上下文
    contextualize(request_id="req_12345", user_id="user_67890")
    
    # 记录带有上下文的日志
    logger.info("处理请求")
    logger.info("验证用户权限")
    logger.info("获取用户数据")
    
    # 清除上下文
    clear_contextualize()
    
    # 记录不带上下文的日志
    logger.info("请求处理完成")
    
    logger.info("上下文日志记录测试完成")

def main():
    """主测试函数"""
    logger.info("开始日志系统测试")
    
    # 运行所有测试
    test_basic_logging()
    test_performance_logging()
    test_exception_logging()
    test_contextual_logging()
    
    # 运行异步测试
    asyncio.run(test_async_logging())
    
    logger.info("日志系统测试完成")

if __name__ == "__main__":
    main()