#!/usr/bin/env python3
"""
文本生成链路独立测试脚本

专门测试文本生成功能，包括：
- 大纲生成 (OutlineChain)
- 场景生成 (SceneChain) 
- 对话生成 (DialogueChain)

避免复杂的图像、视频生成，专注于文本链路的调试和验证。
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import litellm
from infra.logger import get_logger

# 启用LiteLLM调试模式（使用新的环境变量方式）
os.environ['LITELLM_LOG'] = 'DEBUG'

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.models import ScriptData, ProjectData, CharacterData, SceneData
from core.chains.outline_chain import OutlineChain, OutlineRequest
from core.chains.scene_chain import SceneChain, SceneRequest
from core.chains.dialogue_chain import DialogueChain, DialogueRequest
from core.config import ConfigManager
from core.cost_control import CostController


class TextChainTester:
    """文本链路测试器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        """初始化测试器"""
        self.config = None
        self.cost_controller = None
        self.outline_chain = None
        self.scene_chain = None
        self.dialogue_chain = None
        self.test_results = {}
        
    async def initialize(self):
        """初始化组件"""
        try:
            print("🔧 初始化文本生成测试环境...")
            
            # 初始化配置
            self.config = ConfigManager()
            print("✅ 配置管理器初始化成功")
            
            # 初始化成本控制器
            self.cost_controller = CostController(self.config)
            print("✅ 成本控制器初始化成功")
            
            # 初始化文本生成链路
            self.outline_chain = OutlineChain(self.config, self.cost_controller)
            self.scene_chain = SceneChain(self.config, self.cost_controller)
            self.dialogue_chain = DialogueChain(self.config, self.cost_controller)
            print("✅ 文本生成链路初始化成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 初始化失败: {str(e)}")
            self.logger.error(f"   详细错误: {type(e).__name__}: {str(e)}")
            import traceback
            self.logger.error(f"   错误堆栈: {traceback.format_exc()}")
            return False
    
    def create_test_data(self) -> Dict[str, Any]:
        """创建测试数据"""
        return {
            'project': ProjectData(
                project_id="text_test_001",
                name="文本生成测试项目",
                description="专门用于测试文本生成功能的项目",
                budget=100.0  # 较小的预算，专注文本生成
            ),
            'outline_request': OutlineRequest(
                title="明朝宫廷风云",
                theme="宫廷权谋斗争",
                dynasty="明朝",
                duration=3,  # 3分钟短剧
                genre="历史剧情",
                target_audience="成人",
                tone="紧张悬疑",
                key_elements=["权谋", "宫廷", "忠诚", "背叛"],
                constraints=["适合短视频", "情节紧凑", "角色鲜明"]
            )
        }
    
    async def test_outline_generation(self, test_data: Dict[str, Any]) -> bool:
        """测试大纲生成"""
        print("\n📝 测试大纲生成链路...")
        
        try:
            outline_request = test_data['outline_request']
            
            print(f"   标题: {outline_request.title}")
            print(f"   主题: {outline_request.theme}")
            print(f"   朝代: {outline_request.dynasty}")
            print(f"   时长: {outline_request.duration}分钟")
            
            # 生成大纲
            start_time = datetime.now()
            outline_response = await self.outline_chain.generate_outline(outline_request)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            
            
            # 验证结果
            if outline_response and outline_response.characters and outline_response.scenes:
                print(f"✅ 大纲生成成功 (耗时: {duration:.2f}秒)")
                print(f"   角色数量: {len(outline_response.characters)}")
                print(f"   场景数量: {len(outline_response.scenes)}")
                
                # 显示角色信息
                print("   角色列表:")
                for char in outline_response.characters[:3]:  # 只显示前3个
                    print(f"     - {char.name}: {char.description[:50]}...")
                
                # 显示场景信息
                print("   场景列表:")
                for scene in outline_response.scenes[:3]:  # 只显示前3个
                    print(f"     - {scene.title}: {scene.background_description[:50]}...")
                
                # 保存结果
                self.test_results['outline'] = {
                    'success': True,
                    'duration': duration,
                    'characters_count': len(outline_response.characters),
                    'scenes_count': len(outline_response.scenes),
                    'data': outline_response
                }
                
                return True
            else:
                self.logger.error("❌ 大纲生成失败: 结果为空或不完整")
                if outline_response:
                    self.logger.error(f"   outline_response内容: {outline_response}")
                    # 检查是否是错误响应
                    if hasattr(outline_response, 'title') and outline_response.title == '错误':
                        self.logger.error("   这是一个错误响应，可能是API调用失败")
                        self.logger.error("   建议检查API密钥配置和网络连接")
                else:
                    self.logger.error("   outline_response为None")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 大纲生成测试失败: {str(e)}")
            self.test_results['outline'] = {'success': False, 'error': str(e)}
            return False
    
    async def test_scene_generation(self) -> bool:
        """测试场景生成"""
        print("\n🎬 测试场景生成链路...")
        
        try:
            # 检查是否有大纲数据
            if 'outline' not in self.test_results or not self.test_results['outline']['success']:
                self.logger.warning("❌ 场景生成测试跳过: 需要先完成大纲生成")
                return False
            
            outline_data = self.test_results['outline']['data']
            
            # 选择第一个场景进行测试
            if not outline_data.scenes:
                self.logger.error("❌ 场景生成测试失败: 没有可用的场景数据")
                return False
            
            test_scene = outline_data.scenes[0]
            print(f"   测试场景: {test_scene.title}")
            print(f"   场景描述: {test_scene.background_description[:100]}...")
            
            # 创建场景请求
            scene_request = SceneRequest(
                scene_data=test_scene,
                characters=outline_data.characters,
                project_context={
                    'dynasty': outline_data.dynasty,
                    'theme': outline_data.theme,
                    'genre': outline_data.genre
                },
                style_preferences={
                    'visual_style': '古典写实',
                    'color_tone': '暖色调',
                    'lighting': '自然光'
                }
            )
            
            # 生成详细场景
            start_time = datetime.now()
            scene_response = await self.scene_chain.generate_scene_details(scene_request)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            
            # 验证结果
            
            if scene_response and hasattr(scene_response, 'shots') and scene_response.shots:
                print(f"✅ 场景生成成功 (耗时: {duration:.2f}秒)")
                print(f"   镜头数量: {len(scene_response.shots)}")
                if hasattr(scene_response, 'total_duration'):
                    print(f"   总时长: {scene_response.total_duration}秒")
                
                # 显示镜头信息
                print("   镜头列表:")
                for shot in scene_response.shots[:3]:  # 只显示前3个
                    print(f"     - {shot.type} ({shot.duration}秒): {shot.composition[:50]}...")
                
                # 保存结果
                self.test_results['scene'] = {
                    'success': True,
                    'duration': duration,
                    'shots_count': len(scene_response.shots),
                    'total_duration': getattr(scene_response, 'total_duration', 0),
                    'data': scene_response
                }
                
                return True
            else:
                self.logger.error("❌ 场景生成失败: 结果为空或不完整")
                if scene_response:
                    self.logger.error(f"   scene_response内容: {scene_response}")
                    # 检查是否是错误响应
                    if hasattr(scene_response, 'scene_id') and scene_response.scene_id == 'error':
                        self.logger.error("   这是一个错误响应，可能是API调用失败")
                        # 尝试从原始数据中获取错误信息
                        if hasattr(scene_response, 'title') and scene_response.title == '错误':
                            self.logger.error("   建议检查API密钥配置和网络连接")
                else:
                    self.logger.error("   scene_response为None")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 场景生成测试失败: {str(e)}")
            self.logger.error(f"   详细错误: {type(e).__name__}: {str(e)}")
            import traceback
            self.logger.error(f"   错误堆栈: {traceback.format_exc()}")
            self.test_results['scene'] = {'success': False, 'error': str(e)}
            return False
    
    async def test_dialogue_generation(self) -> bool:
        """测试对话生成"""
        print("\n💬 测试对话生成链路...")
        
        try:
            # 检查是否有场景数据
            if 'scene' not in self.test_results or not self.test_results['scene']['success']:
                self.logger.warning("❌ 对话生成测试跳过: 需要先完成场景生成")
                return False
            
            outline_data = self.test_results['outline']['data']
            scene_data = self.test_results['scene']['data']
            
            # 创建对话请求
            dialogue_request = DialogueRequest(
                scene_data=outline_data.scenes[0],  # 使用原始场景数据
                characters=outline_data.characters,
                plot_context=outline_data.scenes[0].background_description,
                emotional_context="紧张、悬疑",
                dynasty=outline_data.dynasty,
                duration=30.0,  # 30秒对话
                style_requirements={
                    'language_style': '古典文雅',
                    'formality': '正式',
                    'emotion_intensity': '中等'
                },
                constraints=["符合历史背景", "角色性格鲜明", "推进剧情"]
            )
            
            print(f"   场景: {dialogue_request.scene_data.title}")
            print(f"   参与角色: {len(dialogue_request.characters)}个")
            print(f"   对话时长: {dialogue_request.duration}秒")
            
            # 生成对话
            start_time = datetime.now()
            dialogue_response = await self.dialogue_chain.generate_dialogue(dialogue_request)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            
            # 验证结果
            if dialogue_response and dialogue_response.dialogue_lines:
                print(f"✅ 对话生成成功 (耗时: {duration:.2f}秒)")
                print(f"   对话行数: {len(dialogue_response.dialogue_lines)}")
                print(f"   总时长: {dialogue_response.total_duration}秒")
                
                # 显示对话内容
                print("   对话内容:")
                for line in dialogue_response.dialogue_lines[:5]:  # 只显示前5行
                    print(f"     {line.character}: {line.text[:60]}...")
                
                # 保存结果
                self.test_results['dialogue'] = {
                    'success': True,
                    'duration': duration,
                    'lines_count': len(dialogue_response.dialogue_lines),
                    'total_duration': dialogue_response.total_duration,
                    'data': dialogue_response
                }
                
                return True
            else:
                self.logger.error("❌ 对话生成失败: 结果为空或不完整")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 对话生成测试失败: {str(e)}")
            self.logger.error(f"   详细错误: {type(e).__name__}: {str(e)}")
            import traceback
            self.logger.error(f"   错误堆栈: {traceback.format_exc()}")
            self.test_results['dialogue'] = {'success': False, 'error': str(e)}
            return False
    
    def save_results(self):
        """保存测试结果"""
        try:
            output_dir = Path("output/text_test_results")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = output_dir / f"text_chain_test_{timestamp}.json"
            detailed_file = output_dir / f"text_chain_detailed_{timestamp}.json"
            
            # 准备保存的摘要数据
            save_data = {
                'timestamp': timestamp,
                'summary': {
                    'outline_success': self.test_results.get('outline', {}).get('success', False),
                    'scene_success': self.test_results.get('scene', {}).get('success', False),
                    'dialogue_success': self.test_results.get('dialogue', {}).get('success', False)
                },
                'details': {}
            }
            
            # 添加详细信息（排除data字段）
            for key, result in self.test_results.items():
                save_data['details'][key] = {
                    k: v for k, v in result.items() if k != 'data'
                }
            
            # 保存摘要文件
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            # 保存详细内容文件
            detailed_data = {
                'timestamp': timestamp,
                'generated_content': {}
            }
            
            # 保存大纲详细内容
            if 'outline' in self.test_results and 'data' in self.test_results['outline']:
                outline_data = self.test_results['outline']['data']
                detailed_data['generated_content']['outline'] = {
                    'title': getattr(outline_data, 'title', ''),
                    'theme': getattr(outline_data, 'theme', ''),
                    'dynasty': getattr(outline_data, 'dynasty', ''),
                    'characters': [{
                        'name': char.name,
                        'title': char.title,
                        'description': char.description,
                        'era': char.era
                    } for char in getattr(outline_data, 'characters', [])],
                    'scenes': [{
                        'title': scene.title,
                        'location': scene.location,
                        'background_description': scene.background_description,
                        'key_events': getattr(scene, 'key_events', []),
                        'characters_involved': getattr(scene, 'characters_involved', [])
                    } for scene in getattr(outline_data, 'scenes', [])]
                }
            
            # 保存场景详细内容
            if 'scene' in self.test_results and 'data' in self.test_results['scene']:
                scene_data = self.test_results['scene']['data']
                detailed_data['generated_content']['scene'] = {
                    'shots': [{
                        'shot_id': getattr(shot, 'shot_id', ''),
                        'type': getattr(shot, 'type', ''),
                        'angle': getattr(shot, 'angle', ''),
                        'movement': getattr(shot, 'movement', ''),
                        'duration': getattr(shot, 'duration', 0),
                        'depth_of_field': getattr(shot, 'depth_of_field', ''),
                        'composition': getattr(shot, 'composition', '')
                    } for shot in getattr(scene_data, 'shots', [])]
                }
            
            # 保存对话详细内容
            if 'dialogue' in self.test_results and 'data' in self.test_results['dialogue']:
                dialogue_data = self.test_results['dialogue']['data']
                detailed_data['generated_content']['dialogue'] = {
                    'lines': [{
                        'character': line.character,
                        'content': line.content,
                        'emotion': getattr(line, 'emotion', ''),
                        'duration': getattr(line, 'duration', 0)
                    } for line in getattr(dialogue_data, 'lines', [])]
                }
            
            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump(detailed_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {result_file}")
            print(f"💾 详细内容已保存到: {detailed_file}")
            
        except Exception as e:
            self.logger.warning(f"⚠️  保存测试结果失败: {str(e)}")
            import traceback
            self.logger.warning(f"详细错误: {traceback.format_exc()}")
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 文本生成链路测试总结")
        print("=" * 60)
        
        outline_success = self.test_results.get('outline', {}).get('success', False)
        scene_success = self.test_results.get('scene', {}).get('success', False)
        dialogue_success = self.test_results.get('dialogue', {}).get('success', False)
        
        self.logger.info(f"大纲生成: {'✅ 成功' if outline_success else '❌ 失败'}")
        self.logger.info(f"场景生成: {'✅ 成功' if scene_success else '❌ 失败'}")
        self.logger.info(f"对话生成: {'✅ 成功' if dialogue_success else '❌ 失败'}")
        
        total_tests = 3
        passed_tests = sum([outline_success, scene_success, dialogue_success])
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有文本生成链路测试通过！")
        elif passed_tests > 0:
            self.logger.warning("⚠️  部分测试通过，请检查失败的链路")
        else:
            self.logger.error("❌ 所有测试失败，请检查配置和环境")
        
        print("=" * 60)


async def main():
    """主函数"""
    print("🚀 启动文本生成链路独立测试")
    print("专注测试: 大纲生成 → 场景生成 → 对话生成")
    print("=" * 60)
    
    tester = TextChainTester()
    
    # 初始化
    if not await tester.initialize():
        print("❌ 初始化失败，退出测试")
        return
    
    # 创建测试数据
    test_data = tester.create_test_data()
    print(f"\n📋 测试项目: {test_data['project'].name}")
    
    # 依次执行测试
    success_count = 0
    
    # 测试大纲生成
    if await tester.test_outline_generation(test_data):
        success_count += 1
        
        # 测试场景生成（依赖大纲）
        if await tester.test_scene_generation():
            success_count += 1
            
            # 测试对话生成（依赖场景）
            if await tester.test_dialogue_generation():
                success_count += 1
    
    # 保存结果和打印总结
    tester.save_results()
    tester.print_summary()
    
    return success_count == 3


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {str(e)}")
        sys.exit(1)