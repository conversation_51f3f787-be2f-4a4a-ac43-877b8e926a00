#!/usr/bin/env python3
"""剧本生成集成测试"""

import asyncio
import aiohttp
import os
import pytest
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class TestScriptGeneration:
    """剧本生成集成测试类"""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_script_generation_workflow(self):
        """测试完整的剧本生成工作流"""
        print('🔄 开始测试剧本生成功能...')
        
        api_key = os.getenv('GLM_API_KEY')
        if not api_key:
            pytest.skip('GLM_API_KEY未配置，跳过测试')
        
        url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 步骤1：生成大纲
        print('\n📝 步骤1: 生成剧本大纲...')
        outline_prompt = """
请为一个3分钟的明朝历史短剧生成详细大纲。要求：
- 主题：宫廷斗争
- 时代：明朝
- 角色：2-3个主要角色
- 结构：开端、发展、高潮、结局
- 适合短视频形式

请按以下格式输出：
标题：
主要角色：
剧情大纲：
核心冲突：
"""
        
        async with aiohttp.ClientSession() as session:
            # 生成大纲
            payload = {
                "model": "glm-4-flash",
                "messages": [{"role": "user", "content": outline_prompt}],
                "temperature": 0.7,
                "max_tokens": 500
            }
            
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                assert response.status == 200, f"大纲生成失败，状态码: {response.status}"
                
                result = await response.json()
                outline = result["choices"][0]["message"]["content"]
                print(f'✅ 大纲生成成功：\n{outline[:200]}...')
                
                # 验证大纲包含关键元素
                assert "标题" in outline or "主要角色" in outline
                
                # 步骤2：基于大纲生成具体场景
                print('\n🎬 步骤2: 生成具体场景...')
                scene_prompt = f"""
基于以下大纲，生成第一个场景的详细内容：

{outline}

请生成第一个场景，包括：
- 场景设置（时间、地点、环境）
- 角色出场
- 对话内容
- 动作描述
- 镜头说明

格式：
【场景一】
时间：
地点：
角色：
内容：
"""
                
                payload["messages"] = [{"role": "user", "content": scene_prompt}]
                payload["max_tokens"] = 800
                
                async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                    assert response.status == 200, f"场景生成失败，状态码: {response.status}"
                    
                    result = await response.json()
                    scene = result["choices"][0]["message"]["content"]
                    print(f'✅ 场景生成成功：\n{scene[:200]}...')
                    
                    # 验证场景包含关键元素
                    assert "场景" in scene or "时间" in scene or "地点" in scene
                    
                    # 步骤3：生成媒体提示
                    print('\n🎨 步骤3: 生成图像提示词...')
                    image_prompt_text = f"""
基于以下场景内容，生成图像生成AI的提示词：

{scene}

请生成3个关键画面的英文提示词，适用于AI图像生成：
1. 场景环境
2. 角色特写
3. 关键动作

每个提示词包含：风格、光照、构图、细节描述
"""
                    
                    payload["messages"] = [{"role": "user", "content": image_prompt_text}]
                    payload["max_tokens"] = 400
                    
                    async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                        assert response.status == 200, f"图像提示词生成失败，状态码: {response.status}"
                        
                        result = await response.json()
                        image_prompts = result["choices"][0]["message"]["content"]
                        print(f'✅ 图像提示词生成成功：\n{image_prompts[:200]}...')
                        
                        # 验证提示词包含关键元素（支持中英文）
                        keywords_en = ['style', 'lighting', 'composition', 'detail']
                        keywords_cn = ['风格', '光照', '构图', '细节', '特写', '环境']
                        has_keywords = any(keyword in image_prompts.lower() for keyword in keywords_en) or \
                                     any(keyword in image_prompts for keyword in keywords_cn)
                        assert has_keywords, f"图像提示词缺少关键元素，内容：{image_prompts[:100]}..."
                        
                        print('\n🎉 剧本生成测试完成！')
                        print('✅ 大纲生成 → 场景细化 → 媒体提示 流程验证成功')

if __name__ == "__main__":
    # 支持直接运行
    async def run_test():
        test_instance = TestScriptGeneration()
        await test_instance.test_script_generation_workflow()
    
    asyncio.run(run_test())