#!/usr/bin/env python3
"""
直接测试大纲生成链路

调试为什么大纲生成返回空的角色和场景
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from core.chains.outline_chain import OutlineChain, OutlineRequest
from core.config import ConfigManager
from core.cost_control import CostController


async def test_outline_generation():
    """测试大纲生成"""
    print("=" * 60)
    print("📝 大纲生成链路测试")
    print("=" * 60)
    
    try:
        # 初始化组件
        config_manager = ConfigManager()
        cost_controller = CostController(config_manager)
        outline_chain = OutlineChain(config_manager, cost_controller)
        
        print("✅ 大纲生成链路初始化成功")
        
        # 创建测试请求
        request = OutlineRequest(
            title="调试测试",
            theme="历史",
            dynasty="明朝",
            duration=1,  # 1分钟
            genre="历史剧情",
            target_audience="成人",
            tone="严肃"
        )
        
        print(f"🎯 测试请求:")
        print(f"   标题: {request.title}")
        print(f"   主题: {request.theme}")
        print(f"   朝代: {request.dynasty}")
        print(f"   时长: {request.duration}分钟")
        
        print("\n⏳ 开始生成大纲...")
        
        # 生成大纲
        outline = await outline_chain.generate_outline(request)
        
        print("\n📋 大纲生成结果:")
        print(f"   标题: {outline.title}")
        print(f"   主题: {outline.theme}")
        print(f"   朝代: {outline.dynasty}")
        print(f"   时长: {outline.duration}分钟")
        print(f"   剧情概要: {outline.plot_summary[:100]}...")
        print(f"   角色数量: {len(outline.characters)}")
        print(f"   场景数量: {len(outline.scenes)}")
        print(f"   预估成本: ${outline.estimated_cost:.4f}")
        
        # 详细显示角色信息
        if outline.characters:
            print(f"\n👥 角色信息:")
            for i, char in enumerate(outline.characters):
                print(f"   {i+1}. {char.name} - {char.description}")
        else:
            print("\n⚠️  没有生成任何角色!")
            
        # 详细显示场景信息
        if outline.scenes:
            print(f"\n🎬 场景信息:")
            for i, scene in enumerate(outline.scenes):
                print(f"   {i+1}. {scene.title} - {scene.location} ({scene.duration}秒)")
        else:
            print("\n⚠️  没有生成任何场景!")
            
        return outline
        
    except Exception as e:
        print(f"💥 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None





if __name__ == "__main__":
    print("🚀 开始大纲生成调试测试\n")
    
    # 测试完整的大纲生成链路
    outline_result = asyncio.run(test_outline_generation())
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
        
    if outline_result and (outline_result.characters or outline_result.scenes):
        print("✅ 大纲生成链路: 成功")
        print("💡 系统能够正常生成角色和场景")
    else:
        print("❌ 大纲生成链路: 失败或返回空结果")
        print("🔍 需要检查链路中的错误处理和解析逻辑")